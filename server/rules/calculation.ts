/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CalculationRule, CalculationType, VariableMap } from './rule';
import * as formula from './calculation/formula';
import { getStrategy, preValidate } from './strategy/strategy';
import { conditionProcess, throwableConditionProcess } from './condition';
import { ColumnType, ValueTable } from "../models/public/universalTrackerType";
import { TableData } from "../models/public/universalTrackerValueType";

export {
  Calculation,
  Validation,
  ValidationWithPre,
  ValidationRules,
  ResolvedImportConfiguration
} from './types';

import type { ResolvedImportConfiguration } from './types';

const calculationProcess = function (processingConfig: CalculationRule) {
  switch (processingConfig.value.type) {
    case CalculationType.Formula:
      return formula.calculateValueFromFormula(processingConfig);
    case CalculationType.Average:
      return formula.calculateAverageFromFormula(processingConfig);
    case CalculationType.Sum:
      return formula.calculateSumFromFormula(processingConfig, true);
    case CalculationType.WeightedSum:
      return formula.calculateWeightedSumFromFormula(processingConfig);

    case CalculationType.ValueChainSum:
      return formula.calculateSumFromFormula(processingConfig);
    case CalculationType.ValueChainAverage:
      return formula.calculateAverageFromFormula(processingConfig);
    case CalculationType.Text:
      return processingConfig.value.formula ?? '';
    default:
      throw new Error(
        `calculation "${processingConfig.value.type}" type is not supported`
      );
  }
};

export const calculateValue = async (rules: ResolvedImportConfiguration) => {

  const strategy = getStrategy(rules.calculation.type);
  const value = await strategy({ calculationProcess, conditionProcess, rules });
  return (value === Infinity || value === -Infinity) ? 0 : value;
};

const reduceSingleRowTable = (tableMeta: ValueTable, data: TableData) => {

  const [firstRow] = data;
  if (!Array.isArray(firstRow)) {
    return {};
  }

  const numericCodes = tableMeta.columns
    .filter(({ type }) => type === ColumnType.Number)
    .map(c => c.code);

  return firstRow.reduce((a, { code, value }) => {
    if (numericCodes.includes(code) && !isNaN(value)) {
      a[code] = Number(value);
    } else {
      a[code] = value;
    }
    return a;
  }, ({} as VariableMap));
};

export const reduceTable = (tableMeta: ValueTable, data?: TableData): VariableMap => {

  if (!Array.isArray(data)) {
    return {};
  }

  if (tableMeta.validation?.maxRows === 1 || data.length === 1) {
    return reduceSingleRowTable(tableMeta, data);
  }

  const numericColumns = tableMeta.columns
    .filter(({ type }) => type === ColumnType.Number);

  return data.reduce((a, row) => {
    numericColumns.forEach(({ code }) => {
      const col = row.find((column) => column.code === code);
      if (!col || isNaN(col.value)) {
        return;
      }

      if (a[code] === undefined) {
        a[code] = Number(col.value);
      } else {
        a[code] += Number(col.value);
      }
    });

    return a;
  }, ({} as { [key: string]: number }));
}

export const preValidateValue = async ({ variables, validation }: ResolvedImportConfiguration) => {
  return preValidate({
    validation: {
      validation: validation?.pre,
      variables,
    },
    calculationProcess,
    conditionProcess: throwableConditionProcess,
  }
  );
};

