/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { UniversalTrackerPlain } from '../models/universalTracker';
import { reduceTable } from './calculation';
import { NotApplicableTypes, UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { ConfigurationVariableSetup } from '../survey/compositeUtrConfigs';
import { UtrValueType } from '../models/public/universalTrackerType';
import {
  AggregatedUniversalTracker,
  DisaggregationUniversalTrackerValueFields,
} from '../service/utr/aggregation/AggregatedUniversalTracker';
import {
  getAggregatorByUniversalTrackerFn,
  getUtrValueTypeByAggregator,
} from '../service/utr/aggregation/utrTypeAggregator';
import { AggregationMode } from '../models/public/universalTrackerType';
import { UtrvType } from '../service/utr/constants';

export const resolveVariableByUtrValueType = (
  v: ConfigurationVariableSetup,
  incomingUtr: UniversalTrackerPlain,
  incomingUtrv: UniversalTrackerValuePlain<any> | AggregatedUniversalTracker | DisaggregationUniversalTrackerValueFields
): number | string | undefined => {
  let utr = incomingUtr;
  let utrv = incomingUtrv;

  let valueType: UtrValueType = utr.valueType as UtrValueType;

  if (typeof v.getValue === 'function') {
    return v.getValue({ utr, utrv });
  }

  if (utrv.valueData?.notApplicableType) {
    return utrv.valueData?.notApplicableType === NotApplicableTypes.NA ? NotApplicableTypes.NA : NotApplicableTypes.NR;
  }

  if (v.valueAggregation) {
    const utrAggregator = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
    if (utrAggregator !== v.valueAggregation) {
      // UTR was aggregated using different aggregator, so we need to do it again
      utr = {
        ...utr,
        valueAggregation: v.valueAggregation,
      };
      const utrvs = utrv instanceof AggregatedUniversalTracker ? utrv.disaggregation : [utrv];
      utrv = new AggregatedUniversalTracker(utr, utrvs, utrv.type as UtrvType, AggregationMode.Combined);
      valueType = getUtrValueTypeByAggregator(v.valueAggregation, valueType); // Convert the output
    }
  }

  if (v.valueListCode) {
    if (valueType === UtrValueType.Table && utr.valueValidation?.table) {
      const table = utr.valueValidation.table;
      const tableData = reduceTable(table, utrv.valueData?.table);
      return tableData[v.valueListCode];
    }

    if (valueType === UtrValueType.ValueListMulti) {
      const data = utrv.valueData?.data ?? [];
      return data.find((c: string) => c === v.valueListCode);
    }

    if (utrv.valueData && typeof utrv.valueData.data === 'object') {
      return utrv.valueData.data[v.valueListCode];
    }
    return;
  }

  if ([UtrValueType.ValueList, UtrValueType.Text].includes(valueType)) {
    return utrv?.valueData?.data;
  }

  return utrv.value;
};
