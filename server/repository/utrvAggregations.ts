/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export const userStakeholderLookup = [
  {
    $addFields: {
      userIds: {
        $setUnion: ['$stakeholders.stakeholder', '$stakeholders.verifier'],
      },
    },
  },
  {
    $lookup: {
      from: 'users',
      localField: 'userIds',
      foreignField: '_id',
      as: 'users',
    },
  },
];

export const userStakeholderAndHistoryLookup = [
  {
    $addFields: {
      userIds: {
        $setUnion: ['$stakeholders.stakeholder', '$stakeholders.verifier', '$history.userId'],
      },
    },
  },
  {
    $lookup: {
      from: 'users',
      localField: 'userIds',
      foreignField: '_id',
      as: 'users',
    },
  },
];

export const initiativeLookup = {
  $lookup: {
    from: 'initiatives',
    localField: 'initiativeId',
    foreignField: '_id',
    as: 'initiatives'
  }
};

export const recursiveParentLookup = {
  $graphLookup: {
    from: 'initiatives',
    startWith: '$parentId',
    connectFromField: 'parentId',
    connectToField: '_id',
    as: 'parents',
  }
};

export const universalTrackerLookup = {
  $lookup: {
    from: 'universal-trackers',
    localField: 'universalTrackerId',
    foreignField: '_id',
    as: 'universalTracker'
  }
};

/** @todo: [EvidenceData] this needs to switch to use the new property evidenceData */
export const documentsLookup = {
  $lookup: {
    from: 'document',
    localField: 'history.evidence',
    foreignField: '_id',
    as: 'documents'
  }
};


export const leafSurveyUtrvLookup = () => {
  return {
    $lookup: {
      from: 'universal-tracker-values',
      let: {
        surveyId: '$_id',
      },
      pipeline: [
        {
          $match: {
            deletedDate: { $exists: false },
            'compositeData.fragmentUtrvs': [] as any,
            $expr: {
              $eq: ['$compositeData.surveyId', '$$surveyId']
            }
          }
        }
      ],
      as: 'universalTrackerValues'
    }
  }
};
