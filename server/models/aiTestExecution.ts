/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { type HydratedDocument, model, Schema } from 'mongoose';
import type { ObjectId } from 'bson';
import type { TestResult, TokenUsage } from '../service/ai/ai-testing-types';

export enum TestExecutionType {
  UTRMatching = 'utr-matching',
  DataExtraction = 'data-extraction',
  Custom = 'custom',
}

export enum TestExecutionStatus {
  Pending = 'pending',
  Running = 'running',
  Completed = 'completed',
  Failed = 'failed',
  Cancelled = 'cancelled',
}

export interface ExecutionMetrics {
  executionTime: number;
  tokenUsage: TokenUsage;
  cost: number;
}

export interface AITestExecutionPlain<T = ObjectId> {
  _id: T;
  sessionId: string;
  testType: TestExecutionType;
  prompt: string;
  promptTemplateId?: T;
  aiModel?: string; // The AI model used for this execution (e.g., 'gpt-5', 'claude-3-5-sonnet-latest')
  uploadedFileIds: string[]; // MongoDB ObjectIds of AiUploadedFile documents
  parameters: Record<string, any>; // Contains all request parameters including utrSelection
  results: TestResult[];
  status: TestExecutionStatus;
  error?: string;
  metrics: ExecutionMetrics;
  createdBy: T;
  created: Date;
  updated: Date;
  completedAt?: Date;
}

export type AITestExecutionModel = HydratedDocument<AITestExecutionPlain>;

// Sub-schemas
const TokenUsageSchema = new Schema<TokenUsage>(
  {
    inputTokens: {
      type: Number,
      required: true,
      min: 0,
    },
    outputTokens: {
      type: Number,
      required: true,
      min: 0,
    },
    totalTokens: {
      type: Number,
      required: true,
      min: 0,
    },
    cost: {
      type: Number,
      required: false,
      min: 0,
    },
  },
  { _id: false }
);

const TestResultSchema = new Schema<TestResult>(
  {
    utrCode: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    relevanceScore: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
    },
    explanation: {
      type: String,
      required: false,
      maxlength: 5000,
    },
    matchedContent: {
      type: [String],
      required: false,
      default: undefined,
    },
    confidence: {
      type: Number,
      required: false,
      min: 0,
      max: 1,
    },
  },
  { _id: false }
);

const ExecutionMetricsSchema = new Schema<ExecutionMetrics>(
  {
    executionTime: {
      type: Number,
      required: true,
      min: 0,
    },
    tokenUsage: {
      type: TokenUsageSchema,
      required: true,
    },
    cost: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
  },
  { _id: false }
);

const AITestExecutionSchema = new Schema<AITestExecutionPlain>(
  {
    sessionId: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    testType: {
      type: String,
      required: true,
      enum: Object.values(TestExecutionType),
      trim: true,
    },
    prompt: {
      type: String,
      required: true,
      minlength: 1,
      maxlength: 50000,
    },
    promptTemplateId: {
      type: Schema.Types.ObjectId,
      required: false,
      ref: 'AIPromptTemplate',
    },
    aiModel: {
      type: String,
      required: false,
      trim: true,
      maxlength: 100,
    },
    uploadedFileIds: {
      type: [String],
      default: [],
      trim: true,
    },
    parameters: {
      type: Schema.Types.Mixed,
      default: {},
    },
    results: {
      type: [TestResultSchema],
      default: [],
    },
    status: {
      type: String,
      required: true,
      enum: Object.values(TestExecutionStatus),
      default: TestExecutionStatus.Pending,
    },
    error: {
      type: String,
      required: false,
      maxlength: 5000,
      set: function (value: any) {
        if (!value || value === '') return undefined;
        // Truncate to 5000 characters if exceeds limit
        return String(value).slice(0, 5000);
      },
    },
    metrics: {
      type: ExecutionMetricsSchema,
      required: function (this: AITestExecutionPlain) {
        return this.status === TestExecutionStatus.Completed;
      },
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    completedAt: {
      type: Date,
      required: false,
    },
  },
  {
    timestamps: { createdAt: 'created', updatedAt: 'updated' },
    collection: 'ai-test-executions',
  }
);

// Indexes for actual query patterns
AITestExecutionSchema.index({ created: -1 }); // Default sorting in getTestHistory
AITestExecutionSchema.index({ createdBy: 1, created: -1 }); // User's test history
AITestExecutionSchema.index({ status: 1, created: -1 }); // Filter by status
AITestExecutionSchema.index({ promptTemplateId: 1 }); // Check template usage

// Virtual for duration in milliseconds
AITestExecutionSchema.virtual('duration').get(function () {
  if (this.completedAt && this.created) {
    return this.completedAt.getTime() - this.created.getTime();
  }
  return null;
});

// Virtual for total UTR count
AITestExecutionSchema.virtual('totalUtrCount').get(function () {
  const utrCodes = this.parameters?.utrSelection?.filters?.codes || [];
  return utrCodes.length;
});

// Virtual for result count
AITestExecutionSchema.virtual('resultCount').get(function () {
  return this.results.length;
});

// Virtual for average relevance score
AITestExecutionSchema.virtual('averageRelevanceScore').get(function () {
  if (this.results.length === 0) return 0;
  const sum = this.results.reduce((acc, result) => acc + result.relevanceScore, 0);
  return sum / this.results.length;
});


// Pre-save hook to validate
AITestExecutionSchema.pre('save', function (next) {
  // Ensure completedAt is set for terminal states
  if (
    (this.status === TestExecutionStatus.Completed ||
     this.status === TestExecutionStatus.Failed ||
     this.status === TestExecutionStatus.Cancelled) &&
    !this.completedAt
  ) {
    this.completedAt = new Date();
  }

  // UTR selection is optional - allow document analysis without specific UTRs
  next();
});

const AITestExecution = model<AITestExecutionPlain>('AITestExecution', AITestExecutionSchema);
export default AITestExecution;

// Utility functions instead of schema methods
export async function updateTestExecutionStatus(
  executionId: string,
  status: TestExecutionStatus,
  error?: string
): Promise<AITestExecutionModel | null> {
  const updateData: any = { status };

  if (error) {
    updateData.error = error;
  }

  if (status === TestExecutionStatus.Completed ||
      status === TestExecutionStatus.Failed ||
      status === TestExecutionStatus.Cancelled) {
    updateData.completedAt = new Date();
  }

  return AITestExecution.findByIdAndUpdate(
    executionId,
    updateData,
    { new: true }
  ).exec();
}

export async function addTestExecutionResults(
  executionId: string,
  results: TestResult[]
): Promise<AITestExecutionModel | null> {
  return AITestExecution.findByIdAndUpdate(
    executionId,
    { $push: { results: { $each: results } } },
    { new: true }
  ).exec();
}
