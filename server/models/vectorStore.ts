/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Types, model, Schema, HydratedDocument } from 'mongoose';
import { KeysEnum } from './public/projectionUtils';

export interface VectorStorePlain {
  _id: Types.ObjectId;
  vectorStoreId: string; // OpenAI vector store ID (e.g., vs_xxxxx)
  name: string;
  purpose: 'ai_testing'; // Can be extended in future
  ownerId: Types.ObjectId; // User ID who owns this store
  fileCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export type VectorStoreModel = HydratedDocument<VectorStorePlain>;

export const vectorStoreProjection: KeysEnum<VectorStorePlain, 1> = {
  _id: 1,
  vectorStoreId: 1,
  name: 1,
  purpose: 1,
  ownerId: 1,
  fileCount: 1,
  createdAt: 1,
  updatedAt: 1,
};

const VectorStoreSchema = new Schema<VectorStorePlain>({
  vectorStoreId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  purpose: {
    type: String,
    required: true,
    enum: ['ai_testing'],
    default: 'ai_testing',
  },
  ownerId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  fileCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  collection: 'vector-stores',
  timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
});

// Indexes for efficient querying
VectorStoreSchema.index({ ownerId: 1, purpose: 1 }); // Find user's stores by purpose
// Note: vectorStoreId already has a unique index from the schema definition (unique: true)

const VectorStore = model<VectorStorePlain>('VectorStore', VectorStoreSchema);

export default VectorStore;