import { Schema, model, type Types, type HydratedDocument } from 'mongoose';

export enum ReportDocumentTemplate {
  /** Empty state **/
  Blank = 'blank',
  /** Allow AI to Generate the initial template state **/
  AiGenerated = 'ai_generated',
  /** Simple template with a few pre-defined sections */
  Simple = 'simple',
}

export enum ReportDocumentStatus {
  /**
   * Report document is created, but lexical state not yet generated
   */
  Pending = 'pending',
  /**
   * Lexical state generated, but not yet synced to web socket server
   */
  Generated = 'generated',
  /**
   * Lexical state synced to web socket server, ready for editing
   */
  Completed = 'completed',
  /**
   * Error occurred during lexical state generation
   */
  Error = 'error',
}

interface ReportDocumentConfig {
  template: ReportDocumentTemplate;
}

const configSchema = new Schema<ReportDocumentConfig>(
  {
    template: {
      type: Schema.Types.String,
      required: false,
      trim: true,
      enum: Object.values(ReportDocumentTemplate),
      default: ReportDocumentTemplate.Blank,
    },
  },
  { _id: false }
);

export enum ReportDocumentType {
  CSRD = 'csrd',
  ISSB = 'issb',
}

export interface ReportDocumentPlain {
  _id: Types.ObjectId;
  title: string;
  description?: string;
  type: ReportDocumentType;

  config?: ReportDocumentConfig;

  initiativeId: Types.ObjectId;
  createdBy: Types.ObjectId;
  created: Date;
  lastUpdated: Date;
  status: ReportDocumentStatus;
}

export type CreateReportDocument = Omit<ReportDocumentPlain, '_id' | 'created' | 'lastUpdated' | 'status'>;

export type ReportDocumentModel = HydratedDocument<ReportDocumentPlain>;

const ReportDocumentSchema = new Schema<ReportDocumentPlain>(
  {
    title: { type: Schema.Types.String, required: true },
    description: { type: Schema.Types.String, required: false, trim: true },
    type: { type: Schema.Types.String, required: true, enum: Object.values(ReportDocumentType) },
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    config: { type: configSchema, default: { template: ReportDocumentTemplate.Blank } },
    createdBy: { type: Schema.Types.ObjectId, required: true },
    status: {
      type: Schema.Types.String,
      enum: Object.values(ReportDocumentStatus),
      default: function (this: ReportDocumentPlain) {
        if (!this.config || this.config.template === ReportDocumentTemplate.Blank) {
          return ReportDocumentStatus.Completed;
        }
        return ReportDocumentStatus.Pending;
      },
    },
  },
  {
    collection: 'report-documents',
    timestamps: {
      createdAt: 'created',
      updatedAt: 'lastUpdated',
    },
  }
);

const ReportDocument = model('ReportDocument', ReportDocumentSchema);

export default ReportDocument;
