/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { type HydratedDocument, type Model, model, Schema } from 'mongoose';
import { type ObjectId } from 'bson';
import { type Attribute } from '../service/data/sgx/sgxTypes';
import { LEVEL, validLevels } from '../service/event/Events';
import { generatedUUID } from '../service/crypto/token';

export const MAX_RETRY_COUNT = 3;

/**
 * This is the workflow for status changes for a job:
 * "pending": the job is waiting for an action to trigger
 * "waitForRun": represent for the time that the Cloud needs to setup the background job
 * "processing": represent for the time the job handles its work
 * "pending": switch back to pending if there are any tasks still not completed
 * "completed": if all tasks are completed
 **/
export enum JobStatus {
  Pending = 'pending',
  Processing = 'processing',
  WaitForRun = 'waitForRun',
  Error = 'error',
  Completed = 'completed',
  Deleted = 'deleted'
}

export const finalJobStatuses = [
  JobStatus.Completed,
  JobStatus.Error,
  JobStatus.Deleted,
]

export const activeJobStatuses = [
  JobStatus.Pending,
  JobStatus.Processing,
  JobStatus.WaitForRun,
]

/**
 * Identical for now with JobStatus but could be expanded later
 * @link JobStatus
 **/
export enum TaskStatus {
  Pending = 'pending',
  Processing = 'processing',
  Error = 'error',
  Completed = 'completed'
}

/**
 * We probably want to get rid of this as JobType should control what is allowed?
 **/
export enum TaskType {
  SgxExport = 'sgx_export',
  SurveysBulkCreate = 'surveys_bulk_create',
  AggregatedSurveysBulkCreate = 'aggregated_surveys_bulk_create',

  // Bulk Data import tasks
  UploadFile = 'upload_file',
  /** Process uploaded file **/
  ProcessFile = 'process_file',

  ImportData = 'import_data',
  GenerateReportPPTX = 'generate_report_pptx',
  AutoAggregatedSurvey = 'auto_aggregated_survey',
  UpdateAutoAggregatedSurvey = 'update_auto_aggregated_survey',

  UpdateAllAggregatedSurvey = 'update_all_aggregated_survey',

  InternalReleasePlan = 'internal_release_plan',
  InternalReleaseApprovalRequest = 'internal_release_approval_request',
  InternalReleaseApply = 'internal_release_apply',

  CloneOrganizationExport = 'clone_organization_export',
  CloneOrganizationImport = 'clone_organization_import',

  GenerateSurveyEngagementReport = 'generate_survey_engagement_report',
  GenerateCompanyUpgradesReport = 'generate_company_upgrades_report',

  GenerateMaterialityAssessmentScores = 'generate_materiality_assessment_scores',
  GenerateMaterialityAssessmentMetricGroup = 'generate_materiality_assessment_metric_group',

  GenerateMaterialityTrackerFinancialReport = 'generate_materiality_tracker_financial_report',
  GenerateMaterialityTrackerDoubleMaterialityReport = 'generate_materiality_tracker_double_materiality_report',

  MigrationScriptUp = 'migration_script_up',
  MigrationScriptUpDryRun = 'migration_script_up_dry_run',
  MigrationScriptDown = 'migration_script_down',

  ExportInitiativeSetup = 'export_initiative_setup',
  ExportInitiativeProcess = 'export_initiative_process',
  ExportInitiativeZip = 'export_initiative_zip',

  AIAutoAnswerSetup = 'ai_auto_answer_setup',
  AIAutoAnswerPrepareDocuments = 'ai_auto_answer_prepare_documents',
  AIAutoAnswerProcess = 'ai_auto_answer_process',
  AIAutoAnswerComplete = 'ai_auto_answer_complete',
  AIAutoAnswerCleanup = 'ai_auto_answer_cleanup',

  // Job type: AIDocumentUtrMapping
  FetchDocuments = 'fetch_documents',
  ProcessDocument = 'process_document',

  // Job type: AIReportDocument
  GenerateReportOutline = 'generate_report_outline',
  GenerateReportLexicalState = 'generate_report_lexical_state',
}

type DefaultTaskData = Record<string, unknown>;

export interface Task<TaskData extends DefaultTaskData = DefaultTaskData, T extends TaskType = TaskType> {
  /** Represent UUID4 **/
  id: string;
  /**
   *  Specifies the particular task implementation that Background Task performs.
   */
  type: T;
  name: string;
  status: TaskStatus;
  data: TaskData;
  completedDate?: Date;
}

type SgxExportData = {
  type: 'full';
  files: string[];
};

export const isTaskSgxExport = (task: Task | undefined): task is TaskSgxExport => {
  if (!task) {
    return false;
  }
  return task.type === TaskType.SgxExport;
}

export interface TaskSgxExport extends Task<SgxExportData> {
  type: TaskType.SgxExport,
}

export interface LogMessage {
  severity: number;
  message: string,
  created: Date;
  metadata?: unknown
}

export enum JobType {
  Export = 'export',
  BulkSurveyCreate = 'bulk_survey_create',
  BulkSurveyImport = 'bulk_survey_import',
  GenerateReport = 'generate_report',
  CloneOrganization = 'clone_organization',
  AutoAggregatedSurvey = 'auto_aggregated_survey',
  UpdateAllAggregatedSurvey = 'update_all_aggregated_survey',
  InternalRelease = 'internal_release',
  MigrationScript = 'migration_script',
  MaterialityAssessmentScores = 'materiality_assessment_scores',
  ExportInitiativeDataFull = 'export_initiative_data_full',
  AIAutoAnswerSurvey = 'ai_auto_answer_survey',
  AIReportDocument = 'ai_report_document',

  /** @deprecated */
  SurveyEngagement = 'survey_engagement',
  SuccessReport = 'success_report',
  AIDocumentUtrMapping = 'ai_document_utr_mapping',
}

export const supportedJobTypes = [
  JobType.BulkSurveyCreate,
  JobType.BulkSurveyImport,
  JobType.GenerateReport,
  JobType.AutoAggregatedSurvey,
  JobType.UpdateAllAggregatedSurvey,
  JobType.InternalRelease,
  JobType.SuccessReport,
  JobType.MaterialityAssessmentScores,
  JobType.CloneOrganization,
  JobType.MigrationScript,
  JobType.ExportInitiativeDataFull,
  JobType.AIAutoAnswerSurvey,
  JobType.AIReportDocument,
  JobType.AIDocumentUtrMapping,
];

const validJobTypes = new Set(Object.values(JobType));

export const isJobType = (type: unknown): type is JobType => {
  return typeof type === 'string' && validJobTypes.has(type as JobType);
}

export interface CreateJob<T = ObjectId> {
  _id?: T;
  name: string;
  type: JobType;
  tasks: Task[];
  attributes?: Attribute[];
  priority?: number;
  logs?: LogMessage[];

  idempotencyKey?: string;
  /** User who created the job, otherwise assume this is system created job */
  userId?: T;

  initiativeId?: T;
}

export interface BackgroundJobPlain<Tasks extends Task[] = Task[], T = ObjectId> extends CreateJob<T> {
  _id: T;
  name: string;
  type: JobType;
  attributes: Attribute[];
  priority: number;
  retryCount: number;
  maxRetryCount?: number;
  status: JobStatus;
  tasks: Tasks;
  created: Date;
  updated: Date;
  completedDate?: Date;
  deletedDate?: Date;
  logs: LogMessage[];
}

export type BackgroundJobModel = HydratedDocument<BackgroundJobPlain>;

const AttributesSchema = new Schema({
  name: { type: Schema.Types.String, require: true, trim: true },
  value: { type: Schema.Types.String, require: true, trim: true },
}, { _id: false });


const LogsSchema = new Schema<LogMessage>({
  message: { type: Schema.Types.String, required: true },
  severity: {
    type: Schema.Types.Number,
    enum: validLevels,
    required: true,
    default: LEVEL.INFO,
  },
  metadata: Schema.Types.Mixed,
  created: { type: Schema.Types.Date, default: Date.now },
});

const TaskSchema = new Schema<Task, Model<Task>, Task>({
  id: { type: Schema.Types.String, required: true, default: () => generatedUUID() },
  type: { type: Schema.Types.String, required: true, enum: Object.values(TaskType) },
  name: { type: Schema.Types.String, required: true, trim: true },
  status: { type: Schema.Types.String, required: true, enum: Object.values(TaskStatus) },
  data: { type: Schema.Types.Mixed, default: {} },
  completedDate: Schema.Types.Date,
}, { _id: false, id: false });

const BackgroundJobSchema = new Schema<BackgroundJobModel, Model<BackgroundJobModel>, BackgroundJobModel>({
  name: { type: Schema.Types.String },
  type: { type: Schema.Types.String, required: true, enum: Object.values(JobType) },
  attributes: [AttributesSchema],
  priority: { type: Schema.Types.Number, default: 0 },
  retryCount: { type: Schema.Types.Number, default: 0 },
  maxRetryCount: { type: Schema.Types.Number, default: MAX_RETRY_COUNT },
  status: { type: Schema.Types.String, enum: Object.values(JobStatus), default: JobStatus.Pending },
  tasks: [TaskSchema],
  logs: [LogsSchema],


  created: { type: Schema.Types.Date, default: () => new Date() },
  updated: {
    type: Schema.Types.Date,
    default: () => new Date(),
    pre: ('save')
  },
  completedDate: Schema.Types.Date,
  deletedDate: Schema.Types.Date,
  idempotencyKey: Schema.Types.String,
  initiativeId: Schema.Types.ObjectId,
  userId: Schema.Types.ObjectId,
}, { collection: 'background-jobs' });

BackgroundJobSchema.pre('save', function (next) {
  this.updated = new Date();
  next();
});

BackgroundJobSchema.index({ type: 1, idempotencyKey: 1 });
BackgroundJobSchema.index({ type: 1, status: 1, priority: 1 });
BackgroundJobSchema.index({ initiativeId: 1, status: 1, type: 1 });
BackgroundJobSchema.index({ created: -1 });

const BackgroundJob: Model<BackgroundJobModel> = model('BackgroundJob', BackgroundJobSchema);

export default BackgroundJob;
