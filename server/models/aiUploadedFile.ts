/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Types, model, Schema, HydratedDocument } from 'mongoose';
import { KeysEnum } from './public/projectionUtils';

export interface ProviderFileInfo {
  fileId: string;
  uploadedAt: Date;
  vectorStoreId?: string; // The vector store this file belongs to
}

export interface AiUploadedFilePlain {
  _id: Types.ObjectId;
  originalName: string;
  prefixedName: string;
  uploadedBy: Types.ObjectId;
  fileStoragePath: string; // Required - Path to file in cloud storage system
  // Moved from metadata to root
  tags: string[];
  description?: string;
  providerFiles: {
    openai?: ProviderFileInfo;
    claude?: ProviderFileInfo;
    gemini?: ProviderFileInfo;
  };
  // Keeping in metadata for file-specific info
  metadata: {
    mimetype: string;
    size: number;
    extension: string;
    uploadedAt: Date;
  };
  isActive: boolean;
  created: Date;
  updated: Date;
}

export type AiUploadedFileModel = HydratedDocument<AiUploadedFilePlain>;

export const aiUploadedFileProjection: KeysEnum<AiUploadedFilePlain, 1> = {
  _id: 1,
  originalName: 1,
  prefixedName: 1,
  uploadedBy: 1,
  fileStoragePath: 1,
  tags: 1,
  description: 1,
  providerFiles: 1,
  metadata: 1,
  isActive: 1,
  created: 1,
  updated: 1,
};

// Define subdocument schema for provider files
const ProviderFileSchema = new Schema({
  fileId: {
    type: String,
    required: true,
  },
  uploadedAt: {
    type: Date,
    required: true,
  },
  vectorStoreId: {
    type: String,
    required: false,
  },
}, { _id: false });

const AiUploadedFileSchema = new Schema<AiUploadedFilePlain>({
  originalName: {
    type: String,
    required: true,
    trim: true,
  },
  prefixedName: {
    type: String,
    required: true,
    unique: true,
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  fileStoragePath: {
    type: String,
    required: true,
    trim: true,
  },
  // Moved to root level
  tags: [{
    type: String,
    trim: true,
  }],
  description: {
    type: String,
    trim: true,
  },
  providerFiles: {
    openai: {
      type: ProviderFileSchema,
      default: undefined,
    },
    claude: {
      type: ProviderFileSchema,
      default: undefined,
    },
    gemini: {
      type: ProviderFileSchema,
      default: undefined,
    },
  },
  // Simplified metadata
  metadata: {
    mimetype: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
      required: true,
    },
    extension: {
      type: String,
      required: true,
    },
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
}, {
  collection: 'ai-uploaded-files',
  timestamps: { createdAt: 'created', updatedAt: 'updated' },
});

// Indexes for efficient querying
AiUploadedFileSchema.index({ uploadedBy: 1, isActive: 1, created: -1 }); // Primary query pattern for user files
AiUploadedFileSchema.index({ isActive: 1, updated: 1 }); // For cleanup service
AiUploadedFileSchema.index({ 'providerFiles.gemini.uploadedAt': 1 }); // For Gemini cleanup
AiUploadedFileSchema.index({ 'providerFiles.openai.fileId': 1 }); // For provider file lookups
AiUploadedFileSchema.index({ 'providerFiles.claude.fileId': 1 });
AiUploadedFileSchema.index({ 'providerFiles.gemini.fileId': 1 });
AiUploadedFileSchema.index({ 'providerFiles.openai.vectorStoreId': 1 }); // For finding files in a vector store

const AiUploadedFile = model<AiUploadedFilePlain>('AiUploadedFile', AiUploadedFileSchema);

export default AiUploadedFile;
