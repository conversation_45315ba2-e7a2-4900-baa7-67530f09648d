/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import type { HydratedDocument, Model, SchemaDefinition, Types } from 'mongoose';
import { model, Schema } from 'mongoose';
import type { ObjectId } from 'bson';
import { ValueListOptionsValueSchema } from './valueList';
import type { MetricUnitPlain } from './metricUnit';
import { NumberScale, type Unit } from '../types/units';
import { validUnitTypes } from '../service/units/unitTypes';
import { standards } from '@g17eco/core';
import { TagSchema } from './common/universalTrackerTags';
import type { Tags } from '../types/universalTrackerValue';
import type {
  Alternative,
  TableAggregation,
  TableColumn,
  TableGroupColumn,
  UniversalTrackerPublic,
  ValueTable,
  ValueValidation,
  Variation} from './public/universalTrackerType';
import {
  ColumnType,
  ColumnValueAggregation,
  TableAggregationType,
  UtrValueType,
  ValueAggregation,
  ValueValidationType,
  VariationDataSource,
  VariationType,
} from './public/universalTrackerType';
import type { ConfigurationVariableSetup } from '../survey/compositeUtrConfigs';
import { Blueprints } from '../survey/blueprints';
import type { ValueList } from './public/valueList';
import { hasTextValue } from '../util/universal-trackers';

export const NUMERIC_VALUE_LIST_TOTAL_CODE = '__TOTAL__';

export enum UtrType {
  Calculation = 'calculation',
  Kpi = 'kpi',
  CustomKpi = 'custom_kpi',
  SdgComponent = 'sdg-component',
  Generated = 'generated',
}

export const numberScaleSchema = {
  type: Schema.Types.String,
  required: false,
  enum: Object.values(NumberScale),
  trim: true,
  set: function (value: any) {
    return value === '' ? undefined : value;
  },
};

export const unitInputSchema = {
  type: Schema.Types.String,
  required: false,
  trim: true,
  set: function (value: any) {
    return value === '' ? undefined : value;
  },
};

export interface Calculation {
  variables: Map<string, ConfigurationVariableSetup>;
  formula: string;
  useDisaggregations?: boolean;
}

export interface CalculationLean extends Omit<Calculation, 'variables'> {
  variables: { [key: string]: ConfigurationVariableSetup };
}


export type ConnectionUtr = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'code'
  | 'name'
  | 'valueLabel'
  | 'type'
  | 'valueType'
  | 'valueValidation'
  | 'typeCode'
  | 'unit'
  | 'unitType'
  | 'numberScale'
>;

const calculationSchema = new Schema<Calculation & { score?: number }>(
  {
    variables: {
      type: Schema.Types.Map,
      of: {
        type: {
          code: Schema.Types.String,
          valueListCode: {
            type: Schema.Types.String,
            required: false,
          },
          valueAggregation: {
            type: ValueAggregation,
            required: false,
          },
        },
      },
      required: true,
    },
    formula: {
      type: Schema.Types.String,
      required: true,
    },
    useDisaggregations: {
      type: Schema.Types.Boolean,
      required: false,
      default: false,
    },
  },
  { _id: false }
);

export enum UtrConditionVisibility {
  Disabled = 'disabled',
  Hidden = 'hidden',
}

export interface UtrCondition {
  code: string;
  calculation: Calculation;
  result: {
    visibility: UtrConditionVisibility;
    userMessage?: string;
  };
}

interface UtrConditionLean extends Omit<UtrCondition, 'calculation'> {
  calculation: CalculationLean;
}

const conditionsSchema = new Schema<UtrCondition>({
  code: {
    type: String,
    trim: true,
    lowercase: true,
    required: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) '],
  },
  calculation: {
    type: calculationSchema,
    required: true,
  },
  result: {
    visibility: {
      type: Schema.Types.String,
      enum: Object.values(UtrConditionVisibility),
      default: UtrConditionVisibility.Hidden,
      required: true,
    },
    userMessage: {
      type: Schema.Types.String,
      required: false
    }
  },
}, { _id: false });

interface Connection extends Calculation {
  score: number; // 0-1 (float)
}

interface ConnectionLean extends CalculationLean {
  score: number; // 0-1 (float)
}

const connectionSchema = calculationSchema.clone().add({
  score: {
    type: Schema.Types.Number,
    required: true,
  },
});

export const lockedProperties = {
  unitLocked: { type: Boolean, required: false },
  numberScaleLocked: { type: Boolean, required: false },
};

const VariationSchema = new Schema<Variation>(
  {
    type: {
      type: Schema.Types.String,
      enum: Object.values(VariationType),
      required: true,
    },
    min: {
      type: Schema.Types.Number,
      required: true,
    },
    max: {
      type: Schema.Types.Number,
      required: true,
    },
    dataSource: {
      type: Schema.Types.String,
      enum: Object.values(VariationDataSource),
      required: true,
    },
    confirmationRequired: {
      type: Schema.Types.Boolean,
    },
  },
  { _id: false }
);

const columnValidation = new Schema(
  {
    max: { type: Schema.Types.Number, required: false },
    min: { type: Schema.Types.Number, required: false },
    // only used in initiativeUniversalTracker schema for now
    variations: {
      type: [VariationSchema],
      required: false,
      default: undefined,
    },
    decimal: { type: Schema.Types.Number, min: 0, max: 5, required: false },
    required: {
      type: Schema.Types.Boolean,
      required: false,
    },
    allowCustomOptions: { type: Boolean, required: false },
  },
  { _id: false }
);

const tableCalculationSchema = new Schema<TableColumn['calculation']>(
  {
    formula: { type: Schema.Types.String, required: true },
  },
  { _id: false }
);

const tableVisibilityRuleSchema = new Schema<TableColumn['visibilityRule']>(
  {
    formula: { type: Schema.Types.String, required: true },
  },
  { _id: false }
);

const TableColumns = new Schema<TableColumn>(
  {
    type: {
      type: Schema.Types.String,
      required: true,
      trim: true,
      enum: [
        ColumnType.Number,
        ColumnType.Text,
        ColumnType.Date,
        ColumnType.ValueList,
        ColumnType.ValueListMulti,
        ColumnType.Percentage,
      ],
    },
    calculation: tableCalculationSchema,
    visibilityRule: tableVisibilityRuleSchema,
    valueAggregation: {
      trim: true,
      type: Schema.Types.String,
      enum: Object.values(ColumnValueAggregation),
      required: false,
    },
    instructions: {
      type: Schema.Types.String,
      required: false,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    unitType: {
      type: Schema.Types.String,
      enum: validUnitTypes,
      required: false,
      trim: true,
      // TODO: Add validation to cleanup unitType and valueType
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    unit: {
      type: Schema.Types.String,
      trim: true,
      required: function (this: TableColumn) {
        return Boolean(this.unitType);
      },
    },
    numberScale: numberScaleSchema,
    unitInput: unitInputSchema,
    numberScaleInput: numberScaleSchema,
    ...lockedProperties,
    code: { type: Schema.Types.String, required: true },
    name: { type: Schema.Types.String, required: true },
    shortName: { type: Schema.Types.String, required: false },
    options: { type: [ValueListOptionsValueSchema], required: false, default: undefined },
    validation: columnValidation,
    listId: { type: Schema.Types.ObjectId, required: false },
  },
  { _id: false }
);

const tableValidation = new Schema(
  {
    maxRows: { type: Schema.Types.Number, required: false },
  },
  { _id: false }
);


const groupColumnSchema = new Schema<TableGroupColumn>({
  code: { type: Schema.Types.String, required: true },
}, { _id: false });

const tableAggregation = new Schema<TableAggregation>(
  {
    type: {
      type: Schema.Types.String,
      required: true,
      trim: true,
      enum: Object.values(TableAggregationType),
    },
    columns: {
      type: [groupColumnSchema],
      required: function (this: TableAggregation) {
        return this.type === TableAggregationType.Group;
      }
    },
  },
  { _id: false }
);

const TableSchema = new Schema<ValueTable>(
  {
    validation: tableValidation,
    columns: [TableColumns],
    aggregation: {
      type: tableAggregation,
      required: false,
    },
  },
  { _id: false }
);

const ValueListValidationSchema = new Schema(
  {
    type: {
      type: Schema.Types.String,
      required: true,
      trim: true,
      enum: [ValueValidationType.List, ValueValidationType.Custom],
    },
    // Ref
    listId: { type: Schema.Types.ObjectId, required: false },
    // Custom
    custom: Schema.Types.Mixed,
    allowCustomOptions: { type: Boolean, required: false },
  },
  { _id: false }
);

export const ValueValidationSchema = new Schema(
  {
    min: Schema.Types.Number,
    max: Schema.Types.Number,
    valueList: ValueListValidationSchema,
    // Table
    table: { type: TableSchema },
    decimal: { type: Schema.Types.Number, min: 0, max: 5, required: false },
    // only used in initiativeUniversalTracker schema for now
    variations: {
      type: [VariationSchema],
      required: false,
      default: undefined,
    }
  },
  { _id: false, strict: false }
);

export const TextLabelsSchema = {
  typeCode: {
    type: String,
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
  typeTags: { type: [Schema.Types.String], default: undefined },
  name: { type: String, required: true },
  description: {
    type: String,
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
  valueLabel: { type: String, required: true },
  instructions: {
    type: String,
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
  instructionsEditorState: { type: Schema.Types.Mixed, required: false },
  instructionsLink: {
    type: String,
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
  evidenceInstructions: {
    type: String,
    required: false,
    set: function (value: any) {
      return value === '' ? undefined : value;
    },
  },
};

const languages = ['fr', 'de', 'es'];
const AlternativesLanguageSchema = new Schema(
  {
    ...TextLabelsSchema,
    alternativeType: { type: String, required: true, default: 'language', enum: ['language'] },
    languageCode: {
      type: String,
      required: true,
      enum: languages,
      set: function (value: unknown) {
        return value === '' ? undefined : value;
      },
    },
  },
  { _id: false }
);

const AlternativesStandardSchema = new Schema(
  {
    ...TextLabelsSchema,
  },
  { _id: false }
);

const alternativesSchema: SchemaDefinition = {};
Object.keys(standards).forEach((standardCode) => {
  alternativesSchema[standardCode] = {
    type: AlternativesStandardSchema,
    required: false,
  };
});
languages.forEach((languageCode) => {
  alternativesSchema[languageCode] = {
    type: AlternativesLanguageSchema,
    required: false,
  };
});
const AlternativesSchema = new Schema(alternativesSchema, { _id: false });

const UniversalTrackerSchema = new Schema<UniversalTrackerPlain>(
  {
    code: {
      type: String,
      trim: true,
      lowercase: true,
      required: true,
      unique: true,
      validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) '],
    },
    ...TextLabelsSchema,
    tags: TagSchema,
    alternatives: {
      type: AlternativesSchema,
      default: undefined,
    },
    type: {
      type: Schema.Types.String,
      enum: [UtrType.Kpi, UtrType.Calculation, UtrType.CustomKpi, UtrType.SdgComponent, ...Object.keys(standards)],
      trim: true,
      required: true,
    },
    typeCode: {
      type: String,
      required: function (this: UniversalTrackerModel) {
        return ['sdg', UtrType.SdgComponent].includes(this.type);
      },
    },
    targetDirection: {
      type: String,
      trim: true,
      enum: ['tracker', 'increase', 'decrease'],
      default: 'tracker',
      required: true,
    },
    unitType: {
      type: Schema.Types.String,
      enum: validUnitTypes,
      required: false,
      trim: true,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    unit: {
      type: Schema.Types.String,
      trim: true,
      required: function (this: UniversalTrackerPlain) {
        return Boolean(this.unitType);
      },
    },
    // Override default unit input value, takes priority above survey unitConfig
    unitInput: unitInputSchema,
    numberScale: numberScaleSchema, // This is used for determining the output number scale of the UTR
    numberScaleInput: numberScaleSchema, // This will override the UTRV default number scale during the survey generation
    ...lockedProperties,
    valueType: {
      trim: true,
      type: Schema.Types.String,
      enum: [
        UtrValueType.Number,
        UtrValueType.Sample,
        UtrValueType.Percentage,
        UtrValueType.Date,
        UtrValueType.Text,
        UtrValueType.ValueList,
        UtrValueType.ValueListMulti,
        UtrValueType.NumericValueList,
        UtrValueType.TextValueList,
        UtrValueType.Table,
      ],
      required: true,
    },
    calculation: {
      type: calculationSchema,
      required: function (this: UniversalTrackerModel) {
        return this.type === UtrType.Calculation;
      },
    },
    conditions: {
      type: [conditionsSchema],
      default: undefined, // Will not create empty array by default
    },
    connections: {
      type: [connectionSchema],
      default: undefined, // Will not create empty array by default
    },
    valueAggregation: {
      trim: true,
      type: Schema.Types.String,
      enum: Object.values(ValueAggregation),
      required: false,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    valueListOrdered: {
      type: [
        {
          type: String,
          lowercase: true,
          trim: true,
        },
      ],
      required: function () {
        return false;
      },
      default: undefined,
    },
    valueListTargets: {
      type: [
        {
          type: String,
          lowercase: true,
          trim: true,
        },
      ],
      required: false,
      default: undefined,
    },
    valueValidation: {
      type: ValueValidationSchema,
      required: function () {
        // UTR Imports don't work with this on... need a better solution
        return false; // return [UtrValueType.Table].includes(this.valueType);
      },
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      required: function (this: UniversalTrackerModel) {
        return this.type === UtrType.CustomKpi;
      },
    },
    profile: String,
    blueprintCodes: {
      type: [Schema.Types.String],
      enum: Object.values(Blueprints),
      trim: true,
      default: undefined,
    },
    enableAiSummarization: {
      type: Schema.Types.Boolean,
      required: false,
      validate: {
        validator: function(this: UniversalTrackerPlain) {
          return !this.enableAiSummarization || 
            hasTextValue(this);
        },
        message: 'AI Summarization is only available for Text and Text Value List types'
      }
    },
    created: { type: Date, default: Date.now },
  },
  { collection: 'universal-trackers' }
);

export interface UniversalTrackerMin {
  name: string;
  type: string;
  valueType: string;
}


export interface UniversalTrackerBlueprintMin<T = ObjectId> extends UniversalTrackerMin {
  _id: T;
  code: string;
  typeCode?: string;
  typeTags?: string[];
  valueLabel: string;
  alternatives?: { [key: string]: Alternative };
  ownerId?: T;
  unitType?: string;
  tags?: Tags;
  instructions?: string;
  valueType: string;
  unit?: Unit;
  numberScale?: string;
  valueValidation?: ValueValidation;
}

export interface UniversalTrackerPlain<T = Types.ObjectId, U = Tags> extends UniversalTrackerPublic<T, U> {
  valueListOrdered: any[];
  valueListTargets: any[];
  metricUnit?: MetricUnitPlain;
  valueType: UtrValueType;
  valueAggregation?: ValueAggregation;
  alternatives?: { [key: string]: Alternative };
  unit?: Unit;
  numberScale?: string;
  numberScaleInput?: string;
  unitInput?: Unit;
  unitLocked?: boolean;
  numberScaleLocked?: boolean;
  calculation?: Calculation;
  conditions?: UtrCondition[];
  connections?: Connection[];
  blueprintCodes?: Blueprints[];
  profile?: string;
  enableAiSummarization?: boolean;
}

export interface UniversalTrackerPlainLean<T = Types.ObjectId, U = Tags> extends Omit<UniversalTrackerPlain<T, U>, 'calculation' | 'conditions' | 'connections'> {
  calculation?: CalculationLean;
  conditions?: UtrConditionLean[];
  connections?: ConnectionLean[];
}

export interface UniversalTrackerExtended extends UniversalTrackerPlain {
  numberScale: NumberScale;
  numberScaleInput: NumberScale;
}

export interface UniversalTrackerValueListPlain extends UniversalTrackerPlain {
  valueListOptions?: ValueList;
  tableColumnValueListOptions?: ValueList[];
}

export interface UniversalTrackerValueListPlainLean extends UniversalTrackerPlainLean {
  valueListOptions?: ValueList;
  tableColumnValueListOptions?: ValueList[];
}

export type UniversalTrackerModel = HydratedDocument<UniversalTrackerPlain>;

UniversalTrackerSchema.virtual('valueListOptions', {
  ref: 'ValueListModel', // The model to use
  localField: 'valueValidation.valueList.listId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

UniversalTrackerSchema.virtual('tableColumnValueListOptions', {
  ref: 'ValueListModel', // The model to use
  localField: 'valueValidation.table.columns.listId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
});

UniversalTrackerSchema.virtual('owner', {
  ref: 'Initiative', // The model to use
  localField: 'ownerId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

UniversalTrackerSchema.index({ ownerId: 1 });

const UniversalTracker: Model<UniversalTrackerPlain> = model('UniversalTracker', UniversalTrackerSchema);
export default UniversalTracker;
