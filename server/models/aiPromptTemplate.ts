/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { HydratedDocument, Model, model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { PromptMode } from '../types/aiTesting';

export enum PromptTemplateCategory {
  UTRMatching = 'utr-matching',
  DataExtraction = 'data-extraction',
  Custom = 'custom',
  General = 'general',
}

export interface AIPromptTemplatePlain<T = ObjectId> {
  _id: T;
  name: string;
  description?: string;
  content: string;
  systemPrompt?: string;
  promptMode?: PromptMode; // How system prompt interacts with base prompt
  category: PromptTemplateCategory;
  variables: string[];
  tags: string[];
  isPublic: boolean;
  isActive: boolean;
  createdBy: T;
  usageCount: number;
  version: number;
  metadata?: Record<string, any>;
  created: Date;
  updated: Date;
}

export type AIPromptTemplateModel = HydratedDocument<AIPromptTemplatePlain>;

const AIPromptTemplateSchema = new Schema<AIPromptTemplatePlain>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      minlength: 1,
      maxlength: 200,
    },
    description: {
      type: String,
      required: false,
      trim: true,
      maxlength: 1000,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    content: {
      type: String,
      required: true,
      minlength: 1,
      maxlength: 10000,
    },
    systemPrompt: {
      type: String,
      required: false,
      maxlength: 5000,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    promptMode: {
      type: String,
      required: false,
      enum: Object.values(PromptMode),
      default: PromptMode.Extend,
      trim: true,
    },
    category: {
      type: String,
      required: true,
      enum: Object.values(PromptTemplateCategory),
      trim: true,
    },
    variables: {
      type: [String],
      default: [],
      validate: {
        validator: function (v: string[]) {
          // Validate that each variable is a valid identifier
          const validIdentifier = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
          return v.every(variable => validIdentifier.test(variable));
        },
        message: 'Variables must be valid identifiers (letters, numbers, and underscores only, cannot start with a number)',
      },
    },
    tags: {
      type: [String],
      default: [],
      validate: {
        validator: function (v: string[]) {
          // Ensure tags are lowercase and alphanumeric with hyphens
          const validTag = /^[a-z0-9-]+$/;
          return v.every(tag => validTag.test(tag));
        },
        message: 'Tags must be lowercase and contain only letters, numbers, and hyphens',
      },
      set: function (v: string[]) {
        // Convert tags to lowercase
        return v.map(tag => tag.toLowerCase().trim());
      },
    },
    isPublic: {
      type: Boolean,
      required: true,
      default: false,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    usageCount: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    version: {
      type: Number,
      required: true,
      default: 1,
      min: 1,
    },
    metadata: {
      type: Schema.Types.Mixed,
      required: false,
      default: undefined,
    },
  },
  {
    timestamps: { createdAt: 'created', updatedAt: 'updated' },
    collection: 'ai-prompt-templates',
  }
);

// Indexes for performance - only what's actually used in queries
AIPromptTemplateSchema.index({ category: 1, isActive: 1 }); // Used for filtering in getPromptTemplates
AIPromptTemplateSchema.index({ created: -1 }); // Used for default sorting in getPromptTemplates

// Text index for searching
AIPromptTemplateSchema.index({ name: 'text', description: 'text', tags: 'text' }); // Used for $text search

// Virtual for formatted display name
AIPromptTemplateSchema.virtual('displayName').get(function () {
  return `${this.name} (v${this.version})`;
});

// Virtual for variable count
AIPromptTemplateSchema.virtual('variableCount').get(function () {
  return this.variables.length;
});

// Note: Instance methods and statics are avoided in favor of repository patterns and service methods

// Static method to find templates by user
AIPromptTemplateSchema.statics.findByUser = function (userId: ObjectId, includePublic = true) {
  const query: any = { $or: [{ createdBy: userId }] };
  if (includePublic) {
    query.$or.push({ isPublic: true, isActive: true });
  }
  return this.find(query).sort({ updatedAt: -1 });
};

// Pre-save hook to validate variables in content
AIPromptTemplateSchema.pre('save', function (next) {
  // Extract variables from content (e.g., {{variable}})
  const variablePattern = /\{\{(\w+)\}\}/g;
  const contentVariables = new Set<string>();
  let match;
  
  while ((match = variablePattern.exec(this.content)) !== null) {
    contentVariables.add(match[1]);
  }
  
  // Ensure all content variables are declared
  const declaredVariables = new Set(this.variables);
  const undeclaredVariables = Array.from(contentVariables).filter(v => !declaredVariables.has(v));
  
  if (undeclaredVariables.length > 0) {
    next(new Error(`Undeclared variables found in content: ${undeclaredVariables.join(', ')}`));
  } else {
    next();
  }
});

const AIPromptTemplate = model<AIPromptTemplatePlain, Model<AIPromptTemplatePlain>>('AIPromptTemplate', AIPromptTemplateSchema);
export default AIPromptTemplate;