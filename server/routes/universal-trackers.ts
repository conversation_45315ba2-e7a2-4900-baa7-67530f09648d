/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express, { type Request } from 'express';
import UniversalTracker, { type UniversalTrackerBlueprintMin } from '../models/universalTracker';
import { checkUniversalTrackerPermissions, populateInitiative } from '../middleware/commonMiddlewares';
import type { InitiativePlain } from '../models/initiative';
import sdgMap from '../static/un-sdg';
import UniversalTrackerValueHistoryService from '../service/utr/UniversalTrackerValueHistoryService';
import cache from '../service/cache';
import type { UtrValueType } from '../models/public/universalTrackerType';
import { activeBlueprints } from '../survey/blueprints';
import { getBlueprintRepository } from '../repository/BlueprintRepository';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { universalTrackerPlainFields } from '../repository/projections';
import { FileParserType } from '../service/survey/transfer/parserTypes';
import { getFileExporter } from '../service/survey/FileExporter';
import { setXlsxFileHeaders } from '../http/FileDownload';
import * as XLSX from '@sheet/core';
import moment from 'moment';
import sanitize from 'sanitize-filename';
import { getHiddenStandardCodes } from '../service/utr/utrUtil';
import { CustomTagManager } from '../service/metric/CustomTagManager';
import { ObjectId } from 'bson';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import BadRequestError from '../error/BadRequestError';
import type { AuthRouter } from '../http/AuthRouter';
import { getUtrvFiltersFromDashboardFilters, toPreloadOptions } from '../service/insight-dashboard/utils';
import { mustValidate } from '../util/validation';
import { getUtrsHistoryDtoSchema, getUtrsHistoryQueryDtoSchema } from './validation-schemas/universal-trackers';
import { getInitiativeUniversalTrackerService } from '../service/initiative/InitiativeUniversalTrackerService';
import { getReportingFactory } from '../service/reporting/ReportingFactory';
import { getMappedItem, type ExternalMappingItem } from '../service/reporting/external-mapping-conversions';
import { isDefaultFetching } from '../util/insight-dashboard';
import { getUniversalTrackerHistoricalDataManager } from '../service/utr/historical-data/UniversalTrackerHistoricalDataManager';
import { SurveyRepository } from '../repository/SurveyRepository';
import { createStakeholderGroup } from '../service/stakeholder/StakeholderUtil';
import type { SurveyStakeholderRoles } from '../models/public/surveyType';
import { getSurveyWorkgroupService } from '../service/workgroup/SurveyWorkgroupService';
import type { KeysEnum } from '../models/public/projectionUtils';
import { scopeFiltersUtrProjection, type ScopeFiltersUtr } from '../service/survey/scope/filterScope';
import ContextError from '../error/ContextError';
import { ReportDocumentType } from '../models/reportDocument';
import type { DefinitionMapItem, TagMappingItem } from '../service/reporting/types';
import { getMappingsByType } from '../service/reporting/utils';

const router = express.Router() as AuthRouter;

const historicalDataManager = getUniversalTrackerHistoricalDataManager();

type SearchRequest = Request<
  Record<string, string>,
  unknown,
  unknown,
  {
    s?: string;
    sdgGoal?: string;
    valueType?: UtrValueType;
    initiativeId?: string;
  },
  Record<string, any>
>;

router.route('/search')
  .get(async (req: SearchRequest, res) => {

    if (!req.user) {
      res.NotPermitted();
      return;
    }

    const initiativeId = req.query.initiativeId;
    if (initiativeId) {
      const initiative = await UserInitiativeRepository.getUserInitiative(req.user, initiativeId);
      if (!initiative) {
        res.NotPermitted();
      }
    }

    const sdgGoalFilter = req.query.sdgGoal;
    const searchStr = req.query.s;
    if (!searchStr && !sdgGoalFilter) {
      return res.FromModel([]);
    }

    const whereQuery: any = {};

    if (sdgGoalFilter) {
      // Allow sdg/1 and sdg/1.99
      const sdgGoalRegex = new RegExp('^sdg\/' + sdgGoalFilter + '(($)|(\\.([0-9])))$', 'i');

      whereQuery.type = 'sdg';
      whereQuery.code = { $regex: sdgGoalRegex }
    }

    const hiddenStandardCodes = getHiddenStandardCodes();
    if (hiddenStandardCodes.length > 0) {
      // Doesn't deal with alternatives
      if (!whereQuery.$and) {
        whereQuery.$and = [];
      }
      whereQuery.$and.push({
        type: {
          $nin: hiddenStandardCodes
        }
      });
    }

    if (searchStr) {
      if (!whereQuery.$and) {
        whereQuery.$and = [];
      }

      const searchRegex = new RegExp('.*' + decodeURIComponent(searchStr) + '.*', 'i');
      whereQuery.$and.push({
        $or: [
          { code: { $regex: searchRegex } },
          { name: { $regex: searchRegex } },
          { valueLabel: { $regex: searchRegex } }
        ]
      });

      const repo = getBlueprintRepository();
      const utrCodesInScope = await repo.getSurveyUtrCodes(activeBlueprints);

      let customMetricCodes: string[] = [];
      if (initiativeId) {
        const customMetrics: UniversalTrackerBlueprintMin[] = await InitiativeRepository.getInitiativeKpis(initiativeId);
        if (customMetrics) {
          customMetricCodes = customMetrics.map(m => m.code);
        }
      }

      whereQuery.$and.push({
        code: {
          $in: [
            ...utrCodesInScope,
            ...customMetricCodes
          ]
        }
      });
    }

    const valueTypeFilter = req.query.valueType;
    if (valueTypeFilter) {
      whereQuery.valueType = { $in: valueTypeFilter };
    }

    return UniversalTracker
      .find(whereQuery,
        { code: 1, name: 1, valueType: 1, valueLabel: 1, valueValidation: 1 })
      .populate('valueListOptions')
      .sort({ code: 1, name: 1 })
      .limit(10)
      .lean()
      .exec()
      .then((models) => res.FromModel(models));
  });

router.route('/:id/modal/info')
  .get(async (req: any, res) => {
    try {
      const universalTracker = await UniversalTracker.findById(req.params.id).lean().exec();
      res.FromModel(universalTracker);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:universalTrackerId/history/initiativeId/:initiativeId')
  .get(checkUniversalTrackerPermissions, cache.route(), async function (req, res, next) {
    const utrId = req.params.universalTrackerId;
    const { period, surveyType } = toPreloadOptions(req.query);
    try {
      const universalTracker = await UniversalTracker.findById(utrId, scopeFiltersUtrProjection)
        .orFail(new ContextError('Failed to find universal tracker', { utrId }))
        .lean<ScopeFiltersUtr>()
        .exec();

      const utrService = await UniversalTrackerValueHistoryService.create({
        initiativeId: req.params.initiativeId,
        utrIds: [utrId],
        period,
        surveyType,
      });
      const utrvHistory = await utrService.get({ utrId, showTargets: true });
      const surveyIds = utrvHistory.reduce((acc, utrv) => {
        const surveyId = utrv.compositeData?.surveyId;
        if (surveyId) {
          acc.push(surveyId);
        }
        return acc;
      }, [] as ObjectId[]);
      const surveyProjection: KeysEnum<SurveyStakeholderRoles> = {
        _id: 1,
        roles: 1,
        stakeholders: 1,
        initiativeId: 1,
        sourceName: 1,
        permissions: 1,
        visibleUtrvs: 1,
      };
      const surveys = (await SurveyRepository.findByIds<SurveyStakeholderRoles>(surveyIds, surveyProjection));
      const surveyMap = new Map(surveys.map((survey) => [survey._id.toString(), survey]));

      const result = await Promise.all(
        utrvHistory.map(async (utrv) => {
          const survey = surveyMap.get(utrv.compositeData?.surveyId?.toString() ?? '');
          return {
            ...utrv,
            surveyRoles: survey?.roles,
            surveyStakeholders: survey?.stakeholders ?? createStakeholderGroup(),
            surveyWorkgroups: survey
              ? await getSurveyWorkgroupService().getWorkgroups({
                  survey,
                  userIds: [req.user._id],
                  utrvs: [{ _id: utrv._id, universalTrackerId: utrv.universalTrackerId, universalTracker }],
                })
              : [],
          };
        })
      );
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

// Only used for insight dashboards of CT and PT?
// TODO: Refactor to align the logic with DashboardItemManager
router.route('/utrs-history/initiativeId/:initiativeId').post(async function (req, res, next) {
  try {
    const initiativeId = req.params.initiativeId;
    const user = req.user;
    const canAccess = await InitiativePermissions.canAccessAllSurveyData(user, initiativeId);
    if (!canAccess) {
      return res.NotPermitted();
    }

    const { utrCodes, filters }  = mustValidate(req.body, getUtrsHistoryDtoSchema);

    if (utrCodes.length === 0) {
      return next(new BadRequestError('Please provide at least one Universal Tracker code'));
    }

    const utrIds = await UniversalTracker.find({ code: { $in: utrCodes } }, { _id: 1 })
      .lean()
      .exec()
      .then((utrs) => utrs.map((u) => String(u._id)));

    if (utrIds.length === 0) {
      return res.NotPermitted();
    }

    const queryFilters = mustValidate(req.query, getUtrsHistoryQueryDtoSchema);
    const utrvFilters = getUtrvFiltersFromDashboardFilters({ filters, additionalFilters: queryFilters });

    // Load the utr data based on the number of subsidiaries
    // initiativeIds only contains the initiativeId of the current dashboard then it is a default fetch
    // otherwise it is a disaggregation data fetch
    const { initiativeIds } = filters;
    const utrsData =
      !initiativeIds || isDefaultFetching({ initiativeIds, initiativeId })
        ? await historicalDataManager.getUtrsHistoricalData({
            initiativeId,
            utrIds,
            utrvFilters,
          })
        : await historicalDataManager.getSubsidiariesUtrsData({
            initiativeId,
            initiativeIds,
            utrIds,
            utrvFilters,
          });

    res.FromModel(utrsData);
  } catch (e) {
    next(e);
  }
});

router.route('/:universalTrackerId/history/initiativeId/:initiativeId/download')
  .get(checkUniversalTrackerPermissions, async function (req, res) {
    try {
      const { initiativeId, universalTrackerId } = req.params;
      const utrService = await UniversalTrackerValueHistoryService.create({
        initiativeId,
        utrIds: [universalTrackerId],
      });
      const result = await utrService.get({ utrId: universalTrackerId, showTargets: true });

      const utrvIds = result.reduce<ObjectId[]>((acc, utrv) => {
        if (utrv._id) {
          acc.push(new ObjectId(utrv._id));
        }
        return acc;
      }, []);

      const utrInfo = await UniversalTracker.findById(universalTrackerId).orFail();

      const utrTagMap = await CustomTagManager.getUtrTagMap(initiativeId, [
        new ObjectId(universalTrackerId),
      ]);

      const utrOverridesMap = await getInitiativeUniversalTrackerService().getUtrOverridesMap({
        initiativeId: new ObjectId(initiativeId),
        utrIds: [new ObjectId(universalTrackerId)],
      });

      const exportType = FileParserType.Xlsx;
      const exporter = getFileExporter();
      const user = req.user;
      const xls = await exporter.exportXlsxHistory({
        user: user,
        type: exportType,
        utrvIds,
        additionalColumns: [{ id: 'effectiveDate' }, { id: 'type' }],
        utrTagMap,
        utrOverridesMap
      });
      const date = moment.utc().format('YYYY-MM-DD HH-mm-ss')
      // need to be ASCII characters, @TODO should investigate how to avoid regex here
       
      const fileName = sanitize(`${date} ${utrInfo.name}.${exportType}`).replace(/[^\x00-\x7F]/g, '');
      setXlsxFileHeaders(res, fileName);

      return res.send(XLSX.write(xls, {
        type: 'buffer',
        bookType: exportType
      }));
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/initiativeId/:initiativeId')
  .get(populateInitiative, async (req, res) => {
    const linkedUtrIds = (req.initiative as InitiativePlain).linkedUniversalTrackers
      .map((utr) => utr.universalTrackerId);

    const sdgCodes: string[] = [
      'sdg/score'
    ];
    sdgMap.forEach((goal) => {
      sdgCodes.push('sdg/' + goal.code);
      if (goal.targets) {
        goal.targets.forEach((target) => {
          sdgCodes.push('sdg/' + target.code);
        });
      }
    });

    UniversalTracker.find({
      $or: [
        {
          _id: { $in: linkedUtrIds }
        }
        ,
        {
          code: { $in: sdgCodes }
        }
      ]
    }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrId')
  .get(async (req, res) => {
    return UniversalTracker
      .findById(
        req.params.utrId,
        universalTrackerPlainFields
      )
      .populate('valueListOptions')
      .populate('tableColumnValueListOptions')
      .lean()
      .then((model) => {
        res.FromModel(model);
      });
  });

router.route('/:utrId/initiativeId/:initiativeId/mapping')
  .get((req, res, next) => {
    UniversalTracker.findById(req.params.utrId, universalTrackerPlainFields)
      .lean()
      .orFail()
      .exec()
      .then((model) => {
        const reportingFactory = getReportingFactory();
        const mapping = getMappingsByType({ type: ReportDocumentType.CSRD });
        const csrdDefinition = reportingFactory.getReportDefinition(ReportDocumentType.CSRD);

        const mappings: ExternalMappingItem[] = [];
        for (const mappingItem of Object.values(mapping).filter(
          (item): item is TagMappingItem => !!item
        )) {
          if (mappingItem.utrCode === model.code) {
            const definition: DefinitionMapItem | undefined = csrdDefinition[mappingItem.factName];
            mappings.push(getMappedItem({ mappingItem, definition, type: ReportDocumentType.CSRD }));
          }
        }
        res.FromModel({ utrCode: model.code, mappings: mappings });
      }).catch(next);
  });

module.exports = router;
