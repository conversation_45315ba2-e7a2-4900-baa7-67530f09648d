/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { getAssuranceManager } from '../service/assurance/AssuranceManager';
import FileUpload from '../http/FileUpload';
import { AssuranceRepository } from '../repository/AssuranceRepository';
import Organization from '../models/organization';
import { createAssuranceQuestionManager } from '../service/assurance/AssuranceQuestionManager';
import { AssurancePermissions } from '../service/assurance/AssurancePortfolioPermissions';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { AuthRouter } from '../http/AuthRouter';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { SurveyRepository } from '../repository/SurveyRepository';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { AssurancePortfolioPlain, AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';
import UserError from '../error/UserError';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { ObjectId } from 'bson';
import { UserRepository } from '../repository/UserRepository';
import { canManageAssurancePortfolio, canViewAssurancePortfolio } from '../middleware/assurancePortfolioMiddlewares';
import { getOrganizationOnboardingRepository } from '../repository/OrganizationOnboardingRepository';

const assuranceManager = getAssuranceManager();
const assuranceQuestionManager = createAssuranceQuestionManager();
const organizationOnboardingRepo = getOrganizationOnboardingRepository();

const router = express.Router() as AuthRouter;

router.route('/portfolio')
  .post(ContextMiddleware, FileUpload.any(), (req, res) => {
    UserInitiativeRepository.getUserInitiative(req.user, req.body.initiativeId).then((initiative) => {
      if (!initiative) {
        return res.NotPermitted();
      }
      return assuranceManager
        .createPortfolio(req.body, req.user, req.files)
        .then(assurance => res.FromModel(assurance));

    }).catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/survey/:surveyId')
  .get((req, res) => {
    SurveyRepository.mustFindById(req.params.surveyId).then(async survey => {
      const hasPermissions = await SurveyPermissions.canManage(survey, req.user);
      return AssuranceRepository
        .getAssuranceExpandedExtraByInitiativeId(survey.initiativeId, survey._id, req.user._id, hasPermissions)
        .then((assurances) => res.FromModel({ survey, assurances }))
    }).catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/download')
  .get((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceManager.download(portfolio, req.user))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/questions')
  .get((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceQuestionManager.getSelection(portfolio))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  })
  .post((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceManager.updateQuestions(portfolio, req.body.questions, req.user))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/questions/delegate')
  .post((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceQuestionManager.getSelectedDelegators(portfolio, req.body, req.user))
      .then((result) => {
        res.FromModel(result)
      })
      .catch((err: Error) => res.Exception(err));
  })
  .patch(canManageAssurancePortfolio, (req, res) => {
    const portfolio = res.locals.assurancePortfolio as AssurancePortfolioPlain;
    assuranceQuestionManager.updateQuestionPermissions(portfolio, req.body, req.user)
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/questions/:questionId/download')
  .get(canViewAssurancePortfolio, (req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceQuestionManager.download(portfolio, req.params.questionId, req.user))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/download/multiple')
  .post((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceManager.download(portfolio, req.user, req.body.questionIds))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });


router.route('/portfolio/:id/ready')
  .patch(FileUpload.any(), ContextMiddleware, (req, res) => {
    AssurancePermissions.getPortfolioExpanded(req.params.id, req.user)
      .then(portfolio => {
        if (portfolio.status === AssurancePortfolioStatus.Created) {
          return assuranceManager.setReadyForAssurer(portfolio);
        }
        throw new UserError('This assurance portfolio cannot be sent to assurer because it is already in progress.');
      })
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/documents')
  .delete((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => {
        return assuranceManager.removeDocuments(portfolio, req.user, req.body.documentIds)
      })
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });


router.route('/portfolio/:id')
  .get((req, res, next) => {
    AssuranceRepository.getAssuranceExpandedExtraById(req.params.id, req.user._id)
      .then(async (portfolio) => {
        if (!portfolio) {
          return next(new PermissionDeniedError())
        }
        await AssurancePermissions.hasAccess(portfolio, req.user) ?
          res.FromModel(portfolio) :
          next(new PermissionDeniedError())
      })
      .catch((err: Error) => res.Exception(err));
  })
  .patch(FileUpload.any(), (req, res) => {
    const files = Array.isArray(req.files) ? req.files : [];
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then(portfolio => assuranceManager.update(portfolio, req.user, req.body, files))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolio/:id/search')
  .get((req, res) => UserRepository.searchByAssurancePortfolioId(req.query.s as string, req.params.id)
  .then((data) => UserRepository.anonymize(data))
  .then((data) => res.FromModel(data))
  .catch((e) => res.Exception(e)));

router.route('/assurers')
  .get((req, res) => {
    Organization.find({ partnerTypes: 'assurer' }, { _id: 1, name: 1 })
      .then(result => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/organization/:organizationId/users')
  .get(async (req, res, next) => {
    try {
      const organizationId = new ObjectId(req.params.organizationId);
      const filter = req.user.organizationId
        ? { _id: req.user.organizationId }
        : { 'permissions.userId': req.user._id };
      const organization = await Organization.findOne(filter, { _id: 1, permissions: 1 }).exec();

      if (!organization || !organization._id.equals(organizationId)) {
        return next(new PermissionDeniedError());
      }
      const organizationUsers = await organizationOnboardingRepo.getOrganizationUsers(organizationId);
      res.FromModel(organizationUsers)
    } catch (error) {
      next(error);
    }
  });

module.exports = router;
