import { z } from 'zod';
import { ScheduledType } from '../../models/scheduledNotification';
import { NotificationCategory } from '../../service/notification/NotificationTypes';

// Common pagination schema
const paginationSchema = z.object({
  limit: z.coerce.number().int().min(1).max(100).default(50),
  skip: z.coerce.number().int().min(0).default(0),
});

// Common date range schema
const dateRangeSchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

// Common search and user filter schema
const searchAndUserSchema = z.object({
  searchStr: z.string().optional(),
  userId: z.string().optional(),
});

// Base schema combining common properties
const baseFilterSchema = paginationSchema
  .merge(dateRangeSchema);

// System notification filters schema
export const systemNotificationFiltersSchema = baseFilterSchema
  .merge(searchAndUserSchema)
  .extend({
    category: z.nativeEnum(NotificationCategory).optional(),
  });

// Daily notification filters schema
export const dailyNotificationFiltersSchema = baseFilterSchema
  .merge(searchAndUserSchema)
  .extend({
    notificationType: z.nativeEnum(ScheduledType).optional(),
    status: z.enum(['pending', 'completed', 'errored', 'skipped']).optional(),
  });

// Scheduled notification filters schema
export const scheduledNotificationFiltersSchema = baseFilterSchema
  .extend({
    notificationType: z.nativeEnum(ScheduledType).optional(),
    status: z.enum(['pending', 'completed']).optional(),
  });

// Schedule filters schema
export const scheduleFiltersSchema = baseFilterSchema
  .merge(searchAndUserSchema)
  .extend({
    status: z.enum(['pending', 'completed']).optional(),
  });

// Export types
export type SystemNotificationFilters = z.infer<typeof systemNotificationFiltersSchema>;
export type DailyNotificationFilters = z.infer<typeof dailyNotificationFiltersSchema>;
export type ScheduledNotificationFilters = z.infer<typeof scheduledNotificationFiltersSchema>;
export type ScheduleFilters = z.infer<typeof scheduleFiltersSchema>;