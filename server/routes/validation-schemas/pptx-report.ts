import { PPTXColorScheme, PPTXTemplateScheme } from '../../service/pptx-report/types';
import { z } from 'zod';
import { aiConfigSchema } from './ai-assistant';

export const pptxReportQuerySchema = z.object({
  templateScheme: z.nativeEnum(PPTXTemplateScheme).default(PPTXTemplateScheme.Default),
  colorScheme: z.nativeEnum(PPTXColorScheme).default(PPTXColorScheme.Ocean),
  debug: z.enum(['true']).optional(),
  useAISlideSuggestions: z.enum(['true']).optional(),
  aiConfig: aiConfigSchema.optional(),
});
