/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { z, type ZodType } from 'zod';
import { type CreateReportDocument, ReportDocumentTemplate, ReportDocumentType } from '../../models/reportDocument';

type CreateBase = Omit<CreateReportDocument, 'createdBy' | 'initiativeId'>;
export const createReportDocumentSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ReportDocumentType),
  config: z
    .object({
      template: z.nativeEnum(ReportDocumentTemplate),
    })
    .optional(),
}) satisfies ZodType<CreateBase>;
