import { z } from 'zod';
import { idSchema } from './common';
import { RefineAction } from '../../service/ai/types';

export const additionalContextDto = z.object({
  inputData: z.array(z.record(z.string(), z.union([z.string(), z.number(), z.undefined()]))),
});

export const autoAnswerSchema = z.object({
  useDocumentLibrary: z.coerce.boolean(),
  isOverwriteMetric: z.coerce.boolean(),
});

export const furtherNotesDraftSchema = z.object({
  utrvId: idSchema,
  draftData: z.object({
    value: z.number().optional(),
    unit: z.string().optional(),
    numberScale: z.string().optional(),
    valueData: z.any().optional(),
  }),
});

export const refineFurtherNotesSchema = z.object({
  ...furtherNotesDraftSchema.shape,
  textToRefine: z.string(),
  action: z.nativeEnum(RefineAction),
  additionalContext: z.string().optional(),
});
