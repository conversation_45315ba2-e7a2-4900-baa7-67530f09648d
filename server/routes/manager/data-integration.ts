/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { getDataIntegrationService } from '../../service/data-integration/DataIntegrationService';
import { mustValidate } from '../../util/validation';
import { z } from 'zod';
import { idSchema } from '../validation-schemas/common';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { ObjectId } from 'bson';
import ContextError from '../../error/ContextError';

const dataIntegrationService = getDataIntegrationService();
const router = express.Router({ mergeParams: true }) as AuthRouter;

const connectionIdSchema = z.object({
  initiativeId: idSchema,
  connectionId: z.string()
});

// Legacy Fivetran endpoints (for backward compatibility)
router.get('/fivetran/enable', async (req, res) => {
  const { initiativeId } = req.params;
  const result = await dataIntegrationService.isEnabled(initiativeId);
  res.FromModel(result);
});

router.post('/fivetran/enable', async (req, res) => {
  const { initiativeId } = req.params;
  const result = await dataIntegrationService.enable(initiativeId);
  console.log('result', result);
  res.FromModel(result);
});

router.get('/fivetran/connectors', async (req, res) => {
  const { initiativeId } = req.params;
  const result = await dataIntegrationService.getConnectors(initiativeId);
  res.FromModel(result);
});

router.get('/fivetran/connections', async (req, res) => {
  const { initiativeId } = req.params;
  const result = await dataIntegrationService.getConnections(initiativeId);
  res.FromModel(result);
});


router.delete('/fivetran/connections/:connectionId', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.deleteConnection(initiativeId, connectionId);
  res.FromModel(result);
});

router.patch('/fivetran/connections/:connectionId/pause', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.pauseConnection(initiativeId, connectionId);
  res.FromModel(result);
});

router.patch('/fivetran/connections/:connectionId/resume', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.resumeConnection(initiativeId, connectionId);
  res.FromModel(result);
});

router.post('/fivetran/connections/:connectionId/sync', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.syncConnection(initiativeId, connectionId);
  res.FromModel(result);
});

router.post('/fivetran/connections/:connectionId/resync', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.resyncConnection(initiativeId, connectionId);
  res.FromModel(result);
});

router.post('/fivetran/connections/:connectionId/test', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.testConnection(initiativeId, connectionId);
  if (result.success) {
    res.Success(result.message);
  } else {
    res.Invalid(result.message);
  }
});

router.get('/fivetran/connections/:connectionId', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.getConnectionInfo(initiativeId, connectionId);
  res.FromModel(result);
});

router.get('/fivetran/connections/:connectionId/schemas', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.getConnectionSchemas(initiativeId, connectionId);
  res.FromModel(result);
});

router.patch('/fivetran/connections/:connectionId/schemas/:schemaName/tables/:tableName', async (req, res) => {
  const { initiativeId, connectionId, schemaName, tableName } = mustValidate(req.params, z.object({
    initiativeId: idSchema,
    connectionId: z.string(),
    schemaName: z.string(),
    tableName: z.string()
  }));
  const { enabled } = mustValidate(req.body, z.object({
    enabled: z.boolean()
  }));
  const result = await dataIntegrationService.toggleConnectionSchemaTable(initiativeId, connectionId, schemaName, tableName, enabled);
  return res.FromModel(result);
});

router.get('/fivetran/connections/:connectionId/snowflake/schemas', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.getDatabaseSchemas(initiativeId, connectionId);
  return res.FromModel(result);
});

router.get('/fivetran/connections/:connectionId/snowflake/semantic-model', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const result = await dataIntegrationService.getSemanticModel(initiativeId, connectionId);
  return res.FromModel(result);
});

router.post('/fivetran/connections/:connectionId/snowflake/semantic-model', async (req, res) => {
  const { initiativeId, connectionId } = mustValidate(req.params, connectionIdSchema);
  const { yamlContent } = mustValidate(req.body, z.object({
    yamlContent: z.any()
  }));

  const result = await dataIntegrationService.setDatabaseSemanticModel(initiativeId, connectionId, yamlContent);
  res.FromModel(result);
});

router.post('/chat', async (req, res) => {
  const { initiativeId } = mustValidate(req.params, z.object({
    initiativeId: idSchema,
  }));
  const { message, conversationId, context } = mustValidate(req.body, z.object({
    conversationId: z.string().optional(),
    message: z.string().optional(),
    context: z.object({
      utrId: z.string(),
      utrvId: z.string(),
      type: z.string()
    }).optional()
  }));

  if (context) {
    if (context.type === 'QuestionView') {
      const utr = await UniversalTrackerRepository.findById(new ObjectId(context.utrId), {
        name: 1,
        valueLabel: 1,
        description: 1,
        instructions: 1,
        type: 1,
      });
      const utrv = await UniversalTrackerValueRepository.findById(new ObjectId(context.utrvId), {
        effectiveDate: 1
      });
      if (utr && utrv) {
        const chatContext = {
          utr, utrv
        };
        const result = await dataIntegrationService.getSemanticModelChatUtrContext(initiativeId, chatContext, conversationId);
        res.FromModel(result);
        return;
      }
    }
  }

  if (message) {
    const result = await dataIntegrationService.getSemanticModelChat(initiativeId, message, conversationId);
    res.FromModel(result);
    return;
  }
  throw new ContextError('No message or context provided', {
    initiativeId,
    message,
    conversationId,
    context
  });
});

// 3. Create Embedded Session
router.post('/fivetran/connect-card', async (req, res) => {
  const createDataIntegrationConnectCardSchema = z.object({
    connectorId: z.string(),
    redirectUri: z.string().url(),
  });

  const { initiativeId } = mustValidate(req.params, z.object({ initiativeId: idSchema }));
  const request = mustValidate(req.body, createDataIntegrationConnectCardSchema);

  if (request.connectorId === 'staff_snowflake') {
    if (!req.user?.isStaff) {
      res.NotPermitted('You are not authorized to use this endpoint');
      return;
    }
  }

  const result = await dataIntegrationService.createConnectCard(initiativeId, request);
  if (!result.connect_card) {
    throw new ContextError(result.message ?? 'Unknown error trying to create connect card');
  }
  res.FromModel(result);
  return;
});

module.exports = router;
