/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { MatchParams, ScopeGroupsParams, getStatsRepository } from '../../repository/StatsRepository';
import { reportStatsQueryDto } from '../validation-schemas/stats';
import { mustValidate } from '../../util/validation';
import { availableDataSources, getStaffStatsRepository, StatsGranularity, StatsPeriod } from '../../repository/StaffStatsRepository';

const router = express.Router();

router.route(`/data-sources`)
  .get(async (req, res) => {
    return res.FromModel(availableDataSources);
  });


router.route(`/data`)
  .post(async (req, res) => {
    const { dataSource, granularity, period } = req.body as { dataSource: ReportTypes, granularity: StatsGranularity, period: StatsPeriod };

    const statsRepository = getStaffStatsRepository();
    try {
      res.FromModel(await statsRepository.getReportData(dataSource, period, granularity));
    } catch (e) {
      res.Exception(e as string | Error);
    }
  });


enum ReportTypes {
  Company = 'company',
  Survey = 'survey',
  Scope = 'scope',
  ScopeTag = 'scope_tag',
  Standard = 'standards',
  Questions = 'questions'
}
Object.values(ReportTypes).forEach((reportType) => {

router.route(`/${reportType}`)
  .post(async (req, res) => {
    const statsRepository = getStatsRepository();
    const params: MatchParams = mustValidate(req.body, reportStatsQueryDto);
    const getData = (reportType: ReportTypes) => {
      switch (reportType) {
        case ReportTypes.Company:
          return statsRepository.companyStats(params);
        case ReportTypes.Survey:
          return statsRepository.surveyStats(params, false);
        case ReportTypes.Scope:
          return statsRepository.surveyStats(params, true);
        case ReportTypes.Standard:
          return statsRepository.questionStats(params, true);
        case ReportTypes.Questions:
          return statsRepository.questionStats(params, false);
        case ReportTypes.ScopeTag:
          return statsRepository.questionScopeTag(req.body as ScopeGroupsParams);
      }
    }

    try {
      res.FromModel(await getData(reportType));
    } catch (e) {
      res.Exception(e as string | Error);
    }
  });
});

module.exports = router;
