/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Onboarding from '../../models/onboarding';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { getOnboardingManager } from '../../service/onboarding/OnboardingManager';
import { getOnboardingImporter } from '../../service/onboarding/OnboardingImporter';
import {
  OnboardingStats,
  StatsFilter
} from '../../service/onboarding/OnboardingStats';
import type { AuthRouter } from '../../http/AuthRouter';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { getOnboardingAdminService } from '../../service/onboarding/OnboardingAdminService';
import { toDate } from '../../util/date';

const obManager = getOnboardingManager();
const router = express.Router() as AuthRouter;
router.use(AdminAuditLogs({ service: 'onboarding', skipMethods: [] }));
const onboardingImporter = getOnboardingImporter();

router.route('/')
  .get((req, res, next) => {
    Onboarding.find().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .post((req, res, next) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    const model = new Onboarding(req.body);
    model.save()
      .then(() => res.Success())
      .catch((e: Error) => next(e));
  });

router.route('/list/extended')
  .get((req, res, next) => {
    Onboarding.aggregate([
      {
        $lookup: {
          from: 'onboarding-list',
          localField: 'onboardingListId',
          foreignField: '_id',
          as: 'onboardingList'
        }
      },
      {
        $project: {
          token: 1,
          status: 1,
          initiativeId: 1,
          surveyConfig: 1,
          utrvConfig: 1,
          surveyStakeholders: 1,
          user: 1,
          onboardingListId: 1,
          created: 1,
          onboardingList: { $arrayElemAt: ['$onboardingList', 0] },
        },
      },
      {
        $sort: { created: -1 }
      },
    ]).exec()
      .then((model: any) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/import')
  .post(async (req, res, next) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }

    onboardingImporter.processImport(req.body, req.user)
      .then(async model => {
        const isNew = model.isNew;
        const messages = await onboardingImporter.checkOnboardingData(model);
        return model.save().then(() => res.FromModel({ isNew, messages }));
      })
      .catch((e: Error) => next(e));
  });

router.route('/stats')
  .get((req, res, next) => {
    const { from, to } = req.query as { from: string, to: string };
    const filter = new StatsFilter(toDate(from), toDate(to));

    OnboardingStats.stats(filter)
      .then((result: any) => res.FromModel(result))
      .catch((e: Error) => next(e));
  });

router.route('/initiative-users-accept')
  .post(ContextMiddleware, function acceptPendingUsers(req, res, next) {
    const service = getOnboardingAdminService();
    const data = service.getMustValidate(req.body)

    service.acceptExistingUsers(data, req.user)
      .then((result) => res.FromModel(result))
      .catch((e: Error) => next(e));
  });

router.route('/action/:action')
  .post(ContextMiddleware, (req, res, next) => {
    obManager.executeAction(req.params.action, req.body.ids)
      .then((result: any) => res.FromModel(result))
      .catch((e: Error) => next(e));
  });

router.route('/:id')
  .get((req, res, next) => {
    Onboarding.findById(req.params.id).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .delete(deleteConfirm, (req, res, next) => {
    Onboarding.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => next(e));
  })
  .put(bodyParamIdCheck, (req, res, next) => {
    Onboarding.findById(req.params.id).orFail().exec()
      .then((obj) => {
        obj.set(req.body);
        return obj.save();
      })
      .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
      .catch((e: Error) => next(e));
  });

module.exports = router;
