/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import express from 'express';
import multer from 'multer';
import { isValidObjectId } from 'mongoose';
import { requireStaffScopes } from '../../middleware/staffRoleMiddlewares';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import { StaffScope } from '../../models/staffRole';
import type { AuthRouter, AuthenticatedRequest } from '../../http/AuthRouter';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import deleteConfirm from '../../middleware/deleteConfirm';
import BadRequestError from '../../error/BadRequestError';
import ContextError from '../../error/ContextError';

// Import services
import { getAITestingPromptService } from '../../service/ai/AITestingPromptService';
import { getAITestingFileService } from '../../service/ai/AITestingFileService';
import { getAITestingExecutionService } from '../../service/ai/AITestingExecutionService';
import { getAITestingUTRService } from '../../service/ai/AITestingUTRService';
import { getUnifiedAIModelFactory } from '../../service/ai/UnifiedAIModelFactory';

// Import validators
import {
  improveCustomPromptSchema,
  generatePromptSchema
} from '../../service/ai/utils/ai-testing-validators';

// Configure multer for file uploads (using memory storage for AI service integration)
const memoryStorage = multer.memoryStorage();

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const fileService = getAITestingFileService();
  if (fileService.validateFileType(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ContextError(`File type ${file.mimetype} not supported. Allowed types: ${fileService.getSupportedMimeTypes().join(', ')}`));
  }
};

const upload = multer({
  storage: memoryStorage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter
});

const router = express.Router() as AuthRouter;

// Apply authentication and audit logging middleware
router.use(requireStaffScopes([StaffScope.AdminWrite]));
router.use(AdminAuditLogs({
  service: 'ai-testing'
}));

// POST /api/admin/ai-testing/execute - Execute test
router.post('/execute', async (req: AuthenticatedRequest, res) => {
  const executionService = getAITestingExecutionService();
  const request = executionService.parseExecuteTestRequest(req.body);
  const response = await executionService.executeTest(request, req.user._id.toString());
  res.FromModel(response);
});

// POST /api/admin/ai-testing/utrs/search - Search UTRs
router.post('/utrs/search', async (req: AuthenticatedRequest, res) => {
  const utrService = getAITestingUTRService();
  const response = await utrService.searchUTRs(req.body);
  res.FromModel(response);
});

// POST /api/admin/ai-testing/upload - File upload endpoint
router.post('/upload', (req: AuthenticatedRequest, res, next) => {
  upload.single('file')(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return next(new BadRequestError('File size exceeds 50MB limit'));
        }
        return next(new BadRequestError(`Upload error: ${err.message}`));
      }
      return next(new BadRequestError(`File validation error: ${err.message}`));
    }
    next();
  });
}, async (req: AuthenticatedRequest, res, next) => {
  if (!req.file) {
    return next(new BadRequestError('No file uploaded'));
  }

  const fileService = getAITestingFileService();
  const response = await fileService.uploadFile(req.file, req.user._id.toString());
  res.FromModel(response);
});

// GET /api/admin/ai-testing/prompts/defaults - Get default prompt templates
router.get('/prompts/defaults', async (req: AuthenticatedRequest, res) => {
  const promptService = getAITestingPromptService();
  const prompts = await promptService.getDefaultPrompts();
  res.FromModel({
    prompts,
    total: prompts.length,
    hasMore: false
  });
});

// GET /api/admin/ai-testing/models - Get available AI models
router.get('/models', async (req: AuthenticatedRequest, res) => {
  const modelFactory = getUnifiedAIModelFactory();
  const models = modelFactory.getSupportedModels();

  // Transform to frontend-friendly format
  const formattedModels = models.map(model => ({
    value: model.name,
    label: model.name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' '),
    provider: model.provider,
    capabilities: model.capabilities,
    baseSystemPrompt: model.baseSystemPrompt,
    specialPrefixes: model.specialPrefixes
  }));

  res.FromModel({
    models: formattedModels,
    defaultModel: modelFactory.getDefaultModel()
  });
});

// GET /api/admin/ai-testing/files - Get uploaded files
router.get('/files', async (req: AuthenticatedRequest, res, next) => {
  const fileService = getAITestingFileService();
  const response = await fileService.getUserFiles({
    userId: req.user._id.toString(),
    limit: Number(req.query.limit) || 50,
    offset: Number(req.query.offset) || 0,
    search: req.query.search as string
  });
  res.FromModel(response);
});

// DELETE /api/admin/ai-testing/files/:fileId - Delete uploaded file
router.delete('/files/:fileId', async (req: AuthenticatedRequest, res) => {
  const fileService = getAITestingFileService();
  const response = await fileService.deleteFile(
    req.params.fileId,
    req.user._id.toString()
  );
  res.FromModel(response);
});

// GET /api/admin/ai-testing/prompts - Get prompt templates
router.get('/prompts', async (req: AuthenticatedRequest, res) => {
  const promptService = getAITestingPromptService();
  const response = await promptService.getPromptTemplates({
    category: req.query.category as string,
    isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined,
    search: req.query.search as string,
    limit: Number(req.query.limit) || 50,
    offset: Number(req.query.offset) || 0,
    userId: req.user._id.toString()
  });
  res.FromModel(response);
});

// POST /api/admin/ai-testing/prompts - Create prompt template
router.post('/prompts', async (req: AuthenticatedRequest, res) => {
  const promptService = getAITestingPromptService();
  const response = await promptService.createPromptTemplate(
    req.body,
    req.user._id.toString()
  );
  res.FromModel(response);
});

// GET /api/admin/ai-testing/prompts/:id - Get specific prompt template
router.get('/prompts/:id', async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid prompt template ID'));
  }

  const promptService = getAITestingPromptService();
  const response = await promptService.getPromptTemplate(req.params.id);
  res.FromModel(response);
});

// PUT /api/admin/ai-testing/prompts/:id - Update prompt template
router.put('/prompts/:id', bodyParamIdCheck, async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid prompt template ID'));
  }

  const promptService = getAITestingPromptService();
  const response = await promptService.updatePromptTemplate(
    req.params.id,
    req.body,
    req.user._id.toString()
  );
  res.FromModel(response);
});

// DELETE /api/admin/ai-testing/prompts/:id - Delete prompt template
router.delete('/prompts/:id', deleteConfirm, async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid prompt template ID'));
  }

  const promptService = getAITestingPromptService();
  await promptService.deletePromptTemplate(
    req.params.id,
    req.user._id.toString()
  );
  res.Success(`Successfully deleted prompt template with id=${req.params.id}`);
});

// POST /api/admin/ai-testing/prompts/:id/improve - Improve a prompt using AI
router.post('/prompts/:id/improve', async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid prompt template ID'));
  }

  const promptService = getAITestingPromptService();
  const improvement = await promptService.improvePromptTemplate(req.params.id);
  res.FromModel(improvement);
});

// POST /api/admin/ai-testing/prompts/generate - Generate a prompt from description
router.post('/prompts/generate', async (req: AuthenticatedRequest, res, next) => {
  const promptService = getAITestingPromptService();
  const generated = await promptService.generatePromptFromDescription(
    req.body.description,
    req.body.category
  );
  res.FromModel(generated);
});

// GET /api/admin/ai-testing/tests - Get test history
router.get('/tests', async (req: AuthenticatedRequest, res, next) => {
  const executionService = getAITestingExecutionService();
  const response = await executionService.getTestHistory({
    limit: Number(req.query.limit) || 20,
    offset: Number(req.query.offset) || 0,
    status: req.query.status as string,
    testType: req.query.testType as string,
    startDate: req.query.startDate as string,
    endDate: req.query.endDate as string,
    createdBy: req.query.createdBy as string
  });
  res.FromModel(response);
});

// GET /api/admin/ai-testing/tests/:id - Get specific test result
router.get('/tests/:id', async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid test execution ID'));
  }

  const executionService = getAITestingExecutionService();
  const response = await executionService.getTestExecution(req.params.id);
  res.FromModel(response);
});

// GET /api/admin/ai-testing/tests/:id/status - Get test status (lightweight for polling)
router.get('/tests/:id/status', async (req: AuthenticatedRequest, res, next) => {
  if (!isValidObjectId(req.params.id)) {
    return next(new BadRequestError('Invalid test execution ID'));
  }

  const executionService = getAITestingExecutionService();
  const response = await executionService.getTestStatus(req.params.id);
  res.FromModel(response);
});

// PATCH /api/admin/ai-testing/files/:fileId/rename - Rename file
router.patch('/files/:fileId/rename', async (req: AuthenticatedRequest, res) => {
  const fileService = getAITestingFileService();
  await fileService.renameFile(
    req.params.fileId,
    req.body.name,
    req.user._id.toString()
  );
  res.FromModel({ success: true, message: 'File renamed successfully' });
});

// GET /api/admin/ai-testing/files/:fileId/download - Download file
router.get('/files/:fileId/download', async (req: AuthenticatedRequest, res) => {
  const fileService = getAITestingFileService();
  const downloadInfo = await fileService.downloadFile(
    req.params.fileId,
    req.user._id.toString()
  );

  // Set appropriate headers for file download
  res.setHeader('Content-Type', downloadInfo.mimetype);
  res.setHeader('Content-Disposition', `attachment; filename="${downloadInfo.originalName}"`);
  res.setHeader('Content-Length', downloadInfo.buffer.length.toString());

  // Send the file buffer
  res.send(downloadInfo.buffer);
});


// POST /api/admin/ai-testing/improve-custom-prompt - Improve a custom prompt using AI
router.post('/improve-custom-prompt', async (req: AuthenticatedRequest, res, next) => {
  const { promptContent } = improveCustomPromptSchema.parse(req.body);
  const promptService = getAITestingPromptService();
  const improvement = await promptService.improveCustomPrompt(promptContent);
  res.FromModel(improvement);
});

// POST /api/admin/ai-testing/generate-prompt - Generate a prompt from description
router.post('/generate-prompt', async (req: AuthenticatedRequest, res, next) => {
  const validatedData = generatePromptSchema.parse(req.body);
  const { description, context, outputFormat, additionalRequirements } = validatedData;

  // Combine all input into a comprehensive description
  const fullDescription = `${description}${context ? `\n\nContext: ${context}` : ''}${outputFormat ? `\n\nOutput Format: ${outputFormat}` : ''}${additionalRequirements ? `\n\nAdditional Requirements: ${additionalRequirements}` : ''}`;

  const promptService = getAITestingPromptService();
  const result = await promptService.generatePromptFromDescription(fullDescription);

  res.FromModel({
    generatedPrompt: result.prompt,
    name: result.name,
    variables: result.variables
  });
});


module.exports = router;
