/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { write } from '@sheet/core';
import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import { getMaterialityAssessmentReportService } from '../../service/materiality-assessment/MaterialityAssessmentReportService';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { AssessmentResultType } from '../../service/materiality-assessment/types';
import { ObjectId } from 'bson';
import BackgroundJob, { JobStatus, JobType } from '../../models/backgroundJob';
import MetricGroup from '../../models/metricGroup';
import Survey from '../../models/survey';
import { wwgLogger } from '../../service/wwgLogger';
import { z } from 'zod';
import { mustValidate } from '../../util/validation';
import { LEVEL } from '../../service/event/Events';
import PermissionDeniedError from '../../error/PermissionDeniedError';
import BadRequestError from '../../error/BadRequestError';
import { getMaterialityAssessmentBackgroundJobService } from '../../service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import { refineIdSchema } from '../validation-schemas/common';
import { createLogEntry } from '../../service/jobs/logMessage';

const router = express.Router() as AuthRouter;
const materialityAssessmentReportService = getMaterialityAssessmentReportService();

Object.values(AssessmentResultType).forEach((type) => {
  router.route(`/initiatives/:initiativeId/surveys/:surveyId/download/${type}`).get(async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const exportType = FileParserType.Xlsx;
      const { workbook, filename } = await materialityAssessmentReportService.downloadAssessmentResults({
        initiativeId,
        surveyId: new ObjectId(surveyId),
        type,
      });
      setXlsxFileHeaders(res, `${filename}.${exportType}`);

      return res.send(write(workbook, { type: 'buffer', bookType: exportType, cellStyles: true }));
    } catch (e) {
      next(e);
    }
  });
});

// Endpoint to revert materiality assessment scores
// NOTE: This is a hotfix endpoint - no tests or extensive route handler structure
// were added intentionally to keep the change minimal and reduce risk
router.route('/revert-scores').post(async (req, res) => {
  const { initiativeId, surveyId, reason } = mustValidate(
    req.body,
    z.object({
      initiativeId: refineIdSchema('initiativeId'),
      surveyId: refineIdSchema('surveyId'),
      reason: z.string().min(1).describe('Reason for reverting scores'),
    }),
  );

  const timestamp = new Date().toISOString();
  const userId = req.user._id;

  // Validate that the survey belongs to the initiative
  const survey = await Survey.findOne({
    _id: surveyId,
    initiativeId,
  }).exec();

  if (!survey) {
    throw new PermissionDeniedError('Survey not found or does not belong to the specified initiative');
  }

  // Step 1: Find and invalidate score job using the same method as the service
  const materialityAssessmentBackgroundJobService = getMaterialityAssessmentBackgroundJobService();
  const scoreJob = await materialityAssessmentBackgroundJobService.findExistingJob({
    initiativeId,
    surveyId,
  });

  if (!scoreJob) {
    throw new BadRequestError(
      'No materiality assessment score job found for this survey. The survey may not have been scored yet.',
    );
  }

  // Log the action request with full context
  wwgLogger.info('Materiality assessment score deletion initiated', {
    initiativeId: initiativeId.toString(),
    surveyId: surveyId.toString(),
    userId: userId.toString(),
    reason,
    timestamp,
    action: 'DELETE_SCORES',
    scoreJobId: scoreJob._id.toString(),
  });

  const scoreJobId = scoreJob._id;

  // Get the original idempotency key using the same method as the service
  const originalIdempotencyKey = materialityAssessmentBackgroundJobService.getIndempotencyKey({
    initiativeId,
    surveyId,
  });

  // Store the previous status before updating
  const previousStatus = scoreJob.status;

  // Invalidate by changing idempotency key and status
  scoreJob.idempotencyKey = `${originalIdempotencyKey}-reverted-${timestamp}`;
  scoreJob.status = JobStatus.Deleted;
  scoreJob.deletedDate = new Date();
  scoreJob.updated = new Date();

  // Add log entry
  scoreJob.logs.push(
    createLogEntry(`Job disabled via admin deletion endpoint. Reason: ${reason}`, {
      severity: LEVEL.WARNING,
      metadata: {
        userId: userId.toString(),
        reason,
        action: 'delete',
        previousStatus,
        timestamp,
      },
    }),
  );

  // Save the updated job
  await scoreJob.save();

  wwgLogger.info('Invalidated score job', {
    jobId: scoreJobId.toString(),
    initiativeId: initiativeId.toString(),
    surveyId: surveyId.toString(),
  });

  // Step 2: Clear metric group job references
  // Find all metric groups that reference this score job
  const metricGroups = await MetricGroup.find({
    initiativeId,
    'source.jobId': scoreJobId,
  }).exec();

  let metricGroupsModified = 0;

  wwgLogger.info('Found metric groups to update', {
    count: metricGroups.length,
    scoreJobId: scoreJobId.toString(),
    groupIds: metricGroups.map(g => g._id.toString()),
  });

  // Update each metric group to clear the jobId reference
  for (const metricGroup of metricGroups) {
    // Clear the jobId by setting it to undefined
    if (metricGroup.source) {
      metricGroup.source.jobId = undefined;
      metricGroup.updated = new Date();
      await metricGroup.save();
      metricGroupsModified++;

      wwgLogger.info('Cleared metric group job reference', {
        metricGroupId: metricGroup._id.toString(),
        groupName: metricGroup.groupName,
        initiativeId: initiativeId.toString(),
        surveyId: surveyId.toString(),
      });
    }
  }

  // Step 3: Invalidate PPTX report jobs
  let invalidatedPptxJobs = 0;
  // First find all non-deleted PPTX jobs related to this score job
  const pptxJobs = await BackgroundJob.find({
    type: JobType.GenerateReport,
    'tasks.data.scoreJobId': scoreJobId,
    status: { $ne: JobStatus.Deleted },
  }).exec();

  wwgLogger.info('Found PPTX report jobs to invalidate', {
    scoreJobId: scoreJobId.toString(),
    pptxJobCount: pptxJobs.length,
    pptxJobIds: pptxJobs.map((job) => job._id.toString()),
  });

  // Update each PPTX job individually for better logging
  for (const pptxJob of pptxJobs) {
    // Update the job properties directly on the model instance
    pptxJob.idempotencyKey = pptxJob.idempotencyKey
      ? `${pptxJob.idempotencyKey}-reverted-${timestamp}`
      : `pptx-${pptxJob._id}-reverted-${timestamp}`;
    pptxJob.status = JobStatus.Deleted;
    pptxJob.deletedDate = new Date();
    pptxJob.updated = new Date();

    // Add log entry
    pptxJob.logs.push(
      createLogEntry(`PPTX report job disabled due to materiality score deletion. Reason: ${reason}`, {
        severity: LEVEL.WARNING,
        metadata: {
          userId: userId.toString(),
          reason,
          action: 'delete',
          relatedScoreJobId: scoreJobId.toString(),
          previousStatus: pptxJob.status,
          timestamp,
        },
      }),
    );

    // Save the updated job
    await pptxJob.save();
    invalidatedPptxJobs++;

    wwgLogger.info('Invalidated PPTX report job', {
      pptxJobId: pptxJob._id.toString(),
      scoreJobId: scoreJobId.toString(),
      initiativeId: initiativeId.toString(),
      surveyId: surveyId.toString(),
    });
  }

  wwgLogger.info(`Invalidated ${invalidatedPptxJobs} of ${pptxJobs.length} PPTX report jobs`, {
    scoreJobId: scoreJobId.toString(),
    found: pptxJobs.length,
    invalidated: invalidatedPptxJobs,
  });

  // Log the final result
  wwgLogger.info('Materiality assessment score deletion completed', {
    initiativeId: initiativeId.toString(),
    surveyId: surveyId.toString(),
    scoreJobId: scoreJobId.toString(),
    invalidatedPptxJobs,
    metricGroupsCleared: metricGroupsModified,
    reason,
    userId: userId.toString(),
  });

  // Return detailed summary
  res.json({
    success: true,
    summary: {
      scoreJob: {
        invalidated: true,
        jobId: scoreJobId.toString(),
        message: 'Score job invalidated and will be regenerated on next access',
      },
      metricGroups: {
        cleared: metricGroupsModified,
        message: metricGroupsModified > 0
          ? `${metricGroupsModified} metric group(s) job reference cleared`
          : 'No metric groups found referencing this score job',
      },
      pptxReports: {
        invalidated: invalidatedPptxJobs,
        message:
          invalidatedPptxJobs > 0
            ? `${invalidatedPptxJobs} PPTX report job(s) invalidated`
            : 'No PPTX report jobs found',
      },
      metadata: {
        timestamp,
        reason,
        performedBy: userId.toString(),
      },
    },
    message: 'Materiality assessment scores successfully reverted. The next access will trigger regeneration.',
  });
});

module.exports = router;
