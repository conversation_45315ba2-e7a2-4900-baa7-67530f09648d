/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import config from '../../config';
import { getPgConnection } from '../../service/db/Db';
import cache, { redisCache } from '../../service/cache';
import { getOktaManager } from '../../service/user/OktaManager';
import { getStorage } from '../../service/storage/fileStorage';
import StripeClient from '../../service/payment/StripeClient';
import { getMagicBell } from '../../service/notification/delivery/MagicBell';
import { getLedgerService } from '../../service/ledger/microservice/LedgerService';
import mongoose from 'mongoose';
import { WebsocketService } from '../../service/websocket/websocketService';
import { getDataIntegrationService } from '../../service/data-integration/DataIntegrationService';

const router = express.Router();

interface Checks {
  environment: {
    [key: string]: boolean | number | string;
  },
  services: {
    name: string;
    [key: string]: boolean | number | string | undefined;
  }[];
}

router.route('/').get(async (req, res) => {
  const cloudAccount = config.cloudEnv === 'google' ? config.googleCloud?.credentials.client_email ?? 'Not set' : config.azure?.storage.accountName ?? 'Not set';

  const checks: Checks = {
    environment: {
      release: config.release.release || '',
      build: config.release.build || '',
      appEnv: config.appEnv || '',
      isProduction: config.isProduction ?? false,
      appSubdomain: config.appSubdomain || '-',
      isKubernetes: config.containerEnv === 'kubernetes',
      node: process.version,
      cloudEnv: config.cloudEnv,
      cloudAccount
    },
    services: [],
  }


  try {
    // Check Storage
    const storage = getStorage();
    checks.services.push({
      name: 'Storage',
      status: 'Connected',
      ...await storage.getChecks()
    });
  } catch (e) {
    checks.services.push({
      name: 'Storage',
      error: String(e.message),
    });
  }

  try {
    // Check Websocket
    const websocket = await WebsocketService.getChecks();
    checks.services.push({
      name: 'Websocket',
      ...websocket
    });
  } catch (e) {
    checks.services.push({
      name: 'Websocket',
      error: String(e.message),
    });
  }

  try {
    // Check Data Integration
    const dataIntegrationService = getDataIntegrationService();
    const dataIntegrationStatus = await dataIntegrationService.getHealth();
    checks.services.push({
      name: 'Data Integration - Snowflake',
      status: dataIntegrationStatus.snowflake.status,
      ...dataIntegrationStatus.snowflake,
    });
    checks.services.push({
      name: 'Data Integration - Fivetran',
      status: dataIntegrationStatus.fivetran.status,
      ...dataIntegrationStatus.fivetran,
    });
    // checks.services.push({
    //   name: 'Data Integration - Airbyte',
    //   ...dataIntegrationStatus.airbyte,
    // });
  } catch (e) {
    checks.services.push({
      name: 'Data Integration',
      error: String(e.message),
    });
  }

  // Check Background Jobs
  // Check SES
  // Check QLDB
  // Check OpenAI
  // Check Claude AI
  // Check Logging
  // Check Sentry

  const stripeMode = config.payment.stripe.secret.startsWith('sk_test_') ? 'test' : 'live';
  try {
    // Check Stripe
    await StripeClient.accounts.retrieve();
    checks.services.push({
      name: 'Stripe',
      status: 'Connected',
      mode: stripeMode
    });
  } catch (e) {
    checks.services.push({
      name: 'Stripe',
      status: 'Not Connected',
      mode: stripeMode,
      error: String(e.message),
    });
  }

  try {
    // Check Ledger Microservice
    if (!config.ledgerMicroservice.host) {
      checks.services.push({
        name: 'Ledger Microservice',
        status: 'Not Enabled',
      });
    } else {
      const ledgerService = getLedgerService();
      const ledgerStatus = await ledgerService.getStatus();
      checks.services.push({
        name: 'Ledger Microservice',
        ...ledgerStatus,
      });
    }
  } catch (e) {
    checks.services.push({
      name: 'Ledger Microservice',
      status: 'Not Connected',
      host: config.ledgerMicroservice.host ?? 'Not set',
      port: config.ledgerMicroservice.port ?? 'Not set',
      error: String(e.message),
    });
  }

  try {
    // Check Okta
    checks.services.push({
      name: 'Okta',
      status: await getOktaManager().getStatus(),
      host: config.authentication.oidc.issuer.split('/').slice(0, 3).join('/'),
    });
  } catch (e) {
    checks.services.push({
      name: 'Okta',
      error: String(e.message),
    });
  }

  checks.services.push(await getMagicBell().getStatus())


  try {
    // Check Redis
    checks.services.push({
      name: 'Redis',
      status: cache.client.connected ? 'Connected' : 'Disconnected',
      version: cache.client.server_info.redis_version,
    });
  } catch (e) {
    checks.services.push({
      name: 'Redis',
      error: String(e.message),
    });
  }

  const publicApiCache = 'Redis Public Api';
  try {
    const info = await redisCache.info();
    const infoResponse = info.split('\r\n').reduce((acc, line) => {
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string,string>)

    checks.services.push({
      name: publicApiCache,
      status: redisCache.isReady ? 'Connected' : 'Disconnected',
      version: infoResponse.redis_version,
      connectedClients: infoResponse.connected_clients,
      uptimeInSeconds: infoResponse.uptime_in_seconds,
    });
  } catch (e) {
    checks.services.push({
      name: publicApiCache,
      error: String(e.message),
    });
  }

  try {
    // Check MongoDB
    const mongoConnection = mongoose.connection;
    const mongoDBReadyState = mongoConnection.readyState;
    const mongoDBStatus = await mongoConnection.db?.admin().buildInfo() ?? 'Unknown';
    checks.services.push({
      name: 'MongoDB',
      status: mongoDBReadyState === 1 ? 'Connected' : mongoDBReadyState,
      version: typeof mongoDBStatus === 'object' ? String(mongoDBStatus.version) : 'Unknown',
      uri: `${mongoConnection.host}:${mongoConnection.port}`,
      databaseName: mongoConnection.db?.databaseName ?? 'Unknown',
    });
  } catch (e) {
    checks.services.push({
      name: 'MongoDB',
      error: String(e.message),
    });
  }

  try {
    // Check Postgres
    checks.services.push({
      name: 'Postgres',
      status: 'Connected',
      version: await getPgConnection().databaseVersion(),
    });
  } catch (e) {
    checks.services.push({
      name: 'Postgres',
      status: 'Disconnected',
      error: String(e.message),
    });
  }

  return res.FromModel(checks);
});

module.exports = router;
