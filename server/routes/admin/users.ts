/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import User, { CompleteUser, UserModel, UserType, UsersStatsQueryRequest } from '../../models/user';
import deleteConfirm from '../../middleware/deleteConfirm';
import { getUserManager } from '../../service/user/UserManager';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import FileUpload from '../../http/FileUpload';
import { saveProfile } from '../../service/file/profile';
import { wwgLogger } from '../../service/wwgLogger';
import { AuthRouter } from '../../http/AuthRouter';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import { ContextMiddleware, getContext } from '../../middleware/audit/contextMiddleware';
import BadRequestError from '../../error/BadRequestError';
import { UserErrorMessages } from '../../error/ErrorMessages';
import Initiative from '../../models/initiative'
import { setPassword } from "../public/publicAuth";
import { ObjectId } from "bson";
import HttpError from "../../error/HttpError";
import Addon from "../../models/addon";
import { toBoolean } from '../../http/query';
import { projectDate } from '../../util/date';
import { UserRepository } from '../../repository/UserRepository';
import { isValidObjectId, PipelineStage } from 'mongoose';
import { getCombinedStaffScopes, requireStaffScopes } from "../../middleware/staffRoleMiddlewares";
import { mustValidate } from '../../util/validation';
import { getEmailTransactionDtoSchema } from '../validation-schemas/email-transaction';
import { getEmailTransactionService } from '../../service/email/EmailTransactionService';
import { getEmailActivationService } from '../../service/user/EmailActivationService';
import { resendActivationEmailDtoSchema } from '../validation-schemas/user';
import { getSearchRegex } from '../../util/string';
import { StaffScope } from "../../models/staffRole";
import { checkIsSuperAdmin } from "../../middleware/userMiddlewares";

const router = express.Router() as AuthRouter;
router.use(AdminAuditLogs({ service: 'user' }));
const userManager = getUserManager();
const emailTransactionService = getEmailTransactionService();
const emailActivationService = getEmailActivationService();

router.route('/current')
  .get((req, res) => {
    const completeUser = req.user.getComplete();
    return res.FromModel({
      ...completeUser,
      staffScopes: getCombinedStaffScopes(completeUser),
    });
  })
  .patch(FileUpload.single('profile'), async (req, res, next) => {
    try {
      const user = req.user;
      await userManager.updateProfile(user, req.body);
      if (req.file) {
        await saveProfile(user._id, 'user', [req.file]).catch(wwgLogger.error);
      }
      res.FromModel(user);
    } catch (e) {
      next(e)
    }
  });

router
  .route('/')
  .get((req: UsersStatsQueryRequest<string>, res) => {
    const { startDate, endDate, hideNonActive = false, hideStaff = false, searchStr } = req.query;

    const searchRegex = searchStr ? getSearchRegex(searchStr) : undefined;
    const match: PipelineStage.Match = {
      $match: {
        ...projectDate({ field: 'created', startDate, endDate }),
        ...(toBoolean(hideNonActive) ? { active: true } : undefined),
        ...(toBoolean(hideStaff) ? { isStaff: false } : undefined),
        ...(searchStr
          ? {
              $or: [
                { email: { $regex: searchRegex } },
                { firstName: { $regex: searchRegex } },
                { surname: { $regex: searchRegex } },
              ],
            }
          : undefined),
      },
    };
    UserRepository.getUsersWithExtendedPermissions(match)
      .then((users) => {
        const data: CompleteUser<ObjectId>[] = [];
        for (const completeUser of users) {
          const email = req.user.isSuperUser
            ? completeUser.email
            : UserRepository.convertEmail(completeUser.email, { numOfFirstLetters: 4, numOfAtLetters: 5 });
          data.push({
            ...completeUser,
            email,
            permissions: completeUser.permissions.filter((permission) => permission.initiativeName),
          });
        }
        return res.FromModel(data);
      })
      .catch((e: Error) => res.Exception(e));
  })
  .post((req, res) => {
    userManager
      .createUser(req.body)
      .then((userModel) => res.FromModel(userModel))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/email/:email')
  .get(function (req, res) {
    User.findOne({ email: req.params.email }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/idp/check')
  .post(function (req, res, next) {
    userManager.hasExternalIdp(req.body.email)
      .then(r => res.FromModel(r))
      .catch(next)
  });

router.route('/idp/:id')
  .get(function (req, res, next) {
    userManager.getIdp(req.params.id)
      .then(r => res.FromModel(r))
      .catch(next)
  });

router.route('/:id/activate')
  .put(ContextMiddleware, (req, res) => {
    userManager.activateUser({
      userId: req.params.id,
      isManager: Boolean(req.query.manager),
      domain: getContext().origin,
      domainConfig: getContext().domainConfig,
      appConfig: undefined,
    })
      .then((userModel) => res.FromModel(userModel))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id/password')
  .put(ContextMiddleware, (req, res) => {
    User.findOne({ _id: req.params.id, type: UserType.ServiceAccount })
      .orFail()
      .exec()
      .then(async (model) => {
        const serviceAccount = await setPassword(model, req.body.password);
        return res.FromModel(await serviceAccount.save())
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/service-account')
  .post(ContextMiddleware, async (req, res, next) => {
    const id = new ObjectId();

    const conn = req.body.connection;
    if (!conn) {
      return next(new HttpError('Missing connection', 400))
    }
    if (!conn.addonCode) {
      return next(new HttpError('Missing connection.addonCode', 400))
    }

    try {
      const addon = await Addon.findOne({ code: conn.addonCode }).orFail().exec();
      const initiative = await Initiative.findById(conn.initiativeId, { name: 1 }).orFail().exec();
      wwgLogger.info(`Creating service account for ${initiative.name}`, conn);

      const model = new User({
        _id: id,
        email: `service-account-${id}@g17.eco`,
        firstName: 'Service',
        surname: 'Account',
        connection: {
          ...conn,
          addonId: addon._id,
          initiativeId: initiative._id,
        },
        oktaUserId: id.toString(),
        type: UserType.ServiceAccount,
        active: true,
      });
      const r = await model.save();
      res.FromModel(r)
    } catch (e) {
      next(e)
    }
  });

router.route('/:id/lock')
  .put((req, res) => {
    User.findById(req.params.id).orFail().exec()
      .then(user => userManager.adminLock(user))
      .then((userModel) => res.FromModel(userModel))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id/unlock')
  .put((req, res) => {
    User.findById(req.params.id).orFail().exec()
      .then(user => userManager.adminUnlock(user))
      .then((userModel) => res.FromModel(userModel))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id')
  .get(function (req, res) {
    if (!isValidObjectId(req.params.id)) {
      return res.Exception(new BadRequestError('Invalid user id'));
    }
    const match: PipelineStage.Match = {
      $match: {
        _id: new ObjectId(req.params.id),
      },
    };
    UserRepository.getUsersWithExtendedPermissions(match)
      .then((users) => {
        if (!users.length) {
          return res.Exception(new BadRequestError(UserErrorMessages.NotFound));
        }
        const [completeUser] = users;

        const email = req.user.isSuperUser
          ? completeUser.email
          : UserRepository.convertEmail(completeUser.email, { numOfFirstLetters: 4, numOfAtLetters: 5 });

        res.FromModel({
          ...completeUser,
          email,
          permissions: completeUser.permissions.filter((permission) => permission.initiativeName),
        });
      })
      .catch((e: Error) => res.Exception(e));
  })
  .delete(deleteConfirm, checkIsSuperAdmin, (req, res) => {
    User.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => res.Exception(e));
  })
  .put(bodyParamIdCheck, requireStaffScopes([StaffScope.UserWrite]), (req, res) => {
    User.findById(req.params.id).orFail().exec()
      .then((model) => userManager.updateUserByAdmin(model, req.body))
      .then((model) => res.FromModel(model.getSafeUser()))
      .catch((e: Error) => res.Exception(e));
  });

router.get('/migrate-to-okta/:groupId', async (req, res) => {

  const limit = Number(req.query.limit ?? 0);
  const groupId = req.params.groupId;

  if (!groupId || !limit) {
    return res.Exception('Please provide a groupId and limit: /api/admin/users/migrate-to-okta/:groupId?limit=n');
  }

  const unmigratedUsers = await User
    .find(
      { oktaUserId: { $exists: false } },
      { firstName: 1, surname: 1, email: 1 }
    )
    .sort({ created: -1 })
    .limit(limit)
    .exec();

  const results = {
    count: unmigratedUsers.length,
    success: 0,
    errors: 0,
    migrated: [] as string[],
    failed: [] as { user: UserModel, error: Error }[],
  }

  for (let i = 0; i < unmigratedUsers.length; i++) {
    const user = unmigratedUsers[i];
    try {
      await userManager.migrateOktaUser(user, { hook: { type: "default" } }, [groupId]);
      results.success += 1;
      results.migrated.push(user.email);
    } catch (error) {
      results.errors += 1;
      results.failed.push({
        user,
        error
      });
    }
  }

  res.FromModel(results);
});

router
  .route('/:userId/email')
  .get(async (req, res, next) => {
    const userId = req.params.userId;
    const { service, types } = mustValidate(req.query, getEmailTransactionDtoSchema);

    try {
      const emails = await emailTransactionService.findAll({
        userId,
        service,
        types,
      });
      res.FromModel(emails)
    } catch (e) {
      next(e);
    }
  })
  .post(ContextMiddleware, async (req, res, next) => {
    const { skipSingleCompanyCheck } = mustValidate(req.body, resendActivationEmailDtoSchema);
    const existedUser = await User.findById(req.params.userId);
    if (!existedUser) {
      return res.Exception(new BadRequestError(UserErrorMessages.NotFound));
    }

    try {
      await emailActivationService.resendActivationEmail(existedUser, skipSingleCompanyCheck);
      res.Success('Successfully resend activation email');
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
