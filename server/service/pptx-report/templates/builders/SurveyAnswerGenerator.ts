import { TableColumn, UtrValueType } from '../../../../models/public/universalTrackerType';
import { RowData } from '../../../../models/public/universalTrackerValueType';
import { SupportedMeasureUnits, UnitConfig } from '../../../units/unitTypes';
import { checkMatchedNumberScale, checkMatchedUnit } from '../../../units/utils';
import {
  getNumberScaleCode,
  getTableNumberScaleCode,
  getTableUnitCode,
  getUnitCode,
  getUtrvDataProp,
  getUtrvTableProp,
  getUtrvValue,
} from '../../../utr/utrvUtil';
import { generateMapKey } from '../../utils';
import { InputData, InputMapData, SlideData, TableInputData, ValueListInputData } from '../PPTXTemplateInterface';
import { HelperUtrv } from '../PPTXTemplateSurveyCache';
import { PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { ReportOffset } from './constants';

interface GenerateInputMapParams {
  utrvs: { [code: string]: HelperUtrv | undefined };
  utrs: { utrCode: string; columnCodes?: string[]; previousSurveyIncluded?: boolean }[];
  surveysConfig: {
    unitConfig?: UnitConfig;
    prevUnitConfig?: UnitConfig;
  };
}

interface InputParams {
  utrv: HelperUtrv;
  columnCodes?: string[];
  surveysConfig: GenerateInputMapParams['surveysConfig'];
  displayUserInput?: boolean;
}

type SlideMap = {
  [slideId: number]: {
    utrs: { utrCode: string; columnCodes?: string[]; previousSurveyIncluded?: boolean }[];
    previousSurveyIncluded?: boolean;
  };
};

export class SurveyAnswerGenerator {
  constructor(private slideMap: SlideMap, private repositoryManager: PPTXTemplateSurveyCacheManager) {}

  /**
   * Generates a map of SlideData objects, keyed by slideId.
   *
   * For each slide:
   * 1. Creates a map (utrsInputMap) of input data based on the provided UTR codes.
   *    - Return user input data if all unit and number scale values match.
   *    - Otherwise, return converted data
   * 2. Determines the unit and number scale for the slide.
   *    - The first non-null value from utrsInputMap is used.
   *    - If no valid unit or number scale is found, the value remains undefined.
   */
  public async loadSlidesData(): Promise<Map<number, SlideData>> {
    const slideData = await Promise.all(
      Object.entries(this.slideMap).map(async ([slideId, { utrs, previousSurveyIncluded = true }]) => {
        const { utrsInputMap, unit, numberScale } = await this.getSlideAnswers(utrs, previousSurveyIncluded);
        return {
          slideId: Number(slideId),
          utrsInputMap,
          unit,
          numberScale,
        };
      })
    );

    return new Map(
      slideData.map(({ slideId, utrsInputMap, unit, numberScale }) => [slideId, { utrsInputMap, unit, numberScale }])
    );
  }

  public async getSlideAnswers(utrs: { utrCode: string }[], previousSurveyIncluded = false) {
    const unitConfig = await this.repositoryManager.getCachedSurvey()?.getUnitConfig();
    const prevUnitConfig = previousSurveyIncluded
      ? await this.repositoryManager.getCachedSurvey(-1)?.getUnitConfig()
      : undefined;
    const utrCodes = utrs.map(({ utrCode }) => utrCode);
    const utrvs = await this.getUtrvs(utrCodes);

    const utrsInputMap = this.generateUtrsInputMap({
      utrvs,
      utrs,
      surveysConfig: { unitConfig, prevUnitConfig },
    });

    const { units, numberScales } = this.getSlideUnitsAndNumberScales(Array.from(utrsInputMap.values()).flat(2));
    return {
      utrsInputMap,
      unit: Array.from(units).find(Boolean),
      numberScale: Array.from(numberScales).find(Boolean),
    };
  }

  private async getUtrvs(utrCodes: string[], previousSurveyIncluded = true) {
    const utrvs = await Promise.all(
      utrCodes.map(async (utrCode) => {
        const utrv = await this.repositoryManager.getCachedSurvey()?.getUTRV(utrCode);
        const prevUtrv = previousSurveyIncluded
          ? await this.repositoryManager.getCachedSurvey(ReportOffset.Previous)?.getUTRV(utrCode)
          : undefined;

        return {
          [generateMapKey(utrCode)]: utrv,
          [generateMapKey(utrCode, ReportOffset.Previous)]: prevUtrv,
        };
      })
    );

    return Object.assign({}, ...utrvs) as Record<string, HelperUtrv | undefined>;
  }

  private generateUtrsInputMap(params: GenerateInputMapParams) {
    const inputMap = this.generateInputMap({ ...params, displayUserInput: true });
    if (this.checkMatchUnitAndNumberScale(Array.from(inputMap.values()).flat(2))) {
      return inputMap;
    }
    return this.generateInputMap(params);
  }

  private generateInputMap({
    utrvs,
    utrs = [],
    surveysConfig,
    displayUserInput,
  }: GenerateInputMapParams & {
    displayUserInput?: boolean;
  }) {
    return Object.entries(utrvs).reduce<Map<string, InputMapData>>((acc, [code, utrv]) => {
      if (!utrv) {
        return acc;
      }

      const columnCodes = utrs.find(({ utrCode }) => code.startsWith(utrCode))?.columnCodes;
      const input = this.getInput({
        utrv,
        columnCodes,
        surveysConfig,
        displayUserInput,
      });

      if (!input) {
        return acc;
      }

      acc.set(code, input);
      return acc;
    }, new Map());
  }

  private getInput({
    utrv,
    displayUserInput = false,
    columnCodes,
    surveysConfig,
  }: InputParams): InputMapData | undefined {
    switch (utrv.universalTracker?.valueType) {
      case UtrValueType.Number:
      case UtrValueType.Percentage:
        return this.getNumericInput({ utrv, displayUserInput, surveysConfig });
      case UtrValueType.Table:
        return this.getTableInput({ utrv, displayUserInput, columnCodes, surveysConfig });
      case UtrValueType.NumericValueList:
        return this.getNumericValueListInput({ utrv, displayUserInput, surveysConfig });
      default:
        return;
    }
  }

  private getNumericInput({
    utrv,
    displayUserInput = false,
    surveysConfig,
  }: Omit<InputParams, 'utrConfig'>): InputData | undefined {
    const input = getUtrvValue(utrv, displayUserInput);
    const { universalTracker } = utrv;
    if (!universalTracker) {
      return;
    }

    const unit = this.getUtrUnitCode({ utrv, surveysConfig, displayUserInput });
    const numberScale = getNumberScaleCode(utrv, universalTracker, { displayUserInput });

    return { value: input.value, unit, numberScale };
  }

  private getTableInput({
    utrv,
    displayUserInput = false,
    columnCodes,
    surveysConfig,
  }: InputParams): TableInputData[][] | undefined {
    let table = getUtrvTableProp(utrv, displayUserInput);
    const { universalTracker } = utrv;
    if (!universalTracker) {
      return;
    }

    const columns = universalTracker.valueValidation?.table?.columns ?? [];

    // unanswered - create table row with undefined value to get unit and numberScale
    if (!table || table.length === 0) {
      table = [
        columns.map((col) => ({ code: col.code, unit: col.unit, numberScale: col.numberScale, value: undefined })),
      ];
    }

    return table.reduce<TableInputData[][]>((tableAcc, row) => {
      const filteredRow = row.reduce<TableInputData[]>((rowAcc, inputColumn) => {
        const { code } = inputColumn;
        if (columnCodes && !columnCodes.includes(code)) {
          return rowAcc;
        }

        const column = columns.find((column) => column.code === code);
        if (!column) {
          return rowAcc;
        }

        const unit = this.getTableUnitCode({ column, inputColumn, surveysConfig, displayUserInput });
        const numberScale = getTableNumberScaleCode(column, inputColumn, { displayUserInput });

        rowAcc.push({ ...inputColumn, unit, numberScale });
        return rowAcc;
      }, []);
      tableAcc.push(filteredRow);
      return tableAcc;
    }, []);
  }

  private getNumericValueListInput({
    utrv,
    displayUserInput = false,
    surveysConfig,
  }: Omit<InputParams, 'utrConfig'>): ValueListInputData[] | undefined {
    const input = getUtrvDataProp(utrv, displayUserInput);
    const { universalTracker } = utrv;
    if (!universalTracker) {
      return;
    }

    const unit = this.getUtrUnitCode({ utrv, surveysConfig, displayUserInput });
    const numberScale = getNumberScaleCode(utrv, universalTracker, { displayUserInput });

    if (!input || !input.data) {
      return [{ valueListCode: '', value: undefined, unit, numberScale }];
    }

    return Object.entries(input.data).map(([valueListCode, value]) => ({
      valueListCode,
      value: value as string | number | undefined,
      unit,
      numberScale,
    }));
  }

  private getUtrUnitCode({ utrv, surveysConfig, displayUserInput = false }: InputParams) {
    const { universalTracker } = utrv;
    if (!universalTracker) {
      return;
    }
    if (universalTracker.unitType === SupportedMeasureUnits.currency) {
      return this.getCurrency(surveysConfig, universalTracker.unit);
    }
    return getUnitCode(utrv, universalTracker, { displayUserInput });
  }

  private getTableUnitCode({
    column,
    inputColumn,
    surveysConfig,
    displayUserInput,
  }: Pick<InputParams, 'surveysConfig' | 'displayUserInput'> & {
    column: TableColumn;
    inputColumn: RowData;
  }) {
    if (column.unitType === SupportedMeasureUnits.currency) {
      return this.getCurrency(surveysConfig, column.unit);
    }
    return getTableUnitCode(column, inputColumn, { displayUserInput });
  }

  private getCurrency(surveysConfig: GenerateInputMapParams['surveysConfig'], fallback: string | undefined) {
    const { unitConfig, prevUnitConfig } = surveysConfig;
    if (!prevUnitConfig || unitConfig?.currency === prevUnitConfig?.currency) {
      return unitConfig?.currency;
    }
    return fallback;
  }

  private getSlideUnitsAndNumberScales(inputs: InputData[]) {
    const hasAtLeastOneAnswered = inputs.some(({ value }) => value !== undefined);
    return inputs.reduce<{ units: Set<string>; numberScales: Set<string> }>(
      (acc, { value, unit, numberScale }) => {
        // ignore unanswered if input contains a mix of answered/unanswered values
        if (hasAtLeastOneAnswered && value === undefined) {
          return acc;
        }
        acc.units.add(unit ?? '');
        acc.numberScales.add(numberScale ?? '');
        return acc;
      },
      { units: new Set(), numberScales: new Set() }
    );
  }

  private hasMixedUnitType(uniqueValues: Set<string | undefined>) {
    return uniqueValues.size === 2 && Array.from(uniqueValues).some((s) => s === '');
  }

  private checkMatchUnitAndNumberScale(inputs: InputData[]) {
    const { units, numberScales } = this.getSlideUnitsAndNumberScales(inputs);
    const isMatchedUnit = checkMatchedUnit(units) || this.hasMixedUnitType(units);
    const isMatchedNumberScale = checkMatchedNumberScale(numberScales) || this.hasMixedUnitType(numberScales);
    return isMatchedUnit && isMatchedNumberScale;
  }
}
