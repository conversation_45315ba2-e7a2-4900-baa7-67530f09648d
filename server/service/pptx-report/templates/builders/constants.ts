import { CT_STARTER_ECONOMIC_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-economic';
import { CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-environmental';
import { CT_STARTER_GOVERNANCE_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-governance';
import { CT_STARTER_SOCIAL_SLIDE_UTRS } from '../CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-social';

export const CT_STARTER_SLIDE_UTRS: Record<
  number,
  {
    utrs: { utrCode: string; columnCodes?: string[] }[];
    previousSurveyIncluded?: boolean;
  }
> = {
  ...CT_STARTER_ECONOMIC_SLIDE_UTRS,
  ...CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS,
  ...CT_STARTER_SOCIAL_SLIDE_UTRS,
  ...CT_STARTER_GOVERNANCE_SLIDE_UTRS,
};

export enum ReportOffset {
  Current = 0,
  Previous = -1,
}

// in inch
export const CHART_POSITIONS = {
  x: 10.5,
  y: 3.5,
  w: 8,
  h: 4.5,
};

// in inch
export const CHART_WITH_TABLE_POSITIONS = {
  x: 11,
  y: 2,
  w: 6.5,
  h: 3.5,
};

// in cm
export const TABLE_POSITIONS = {
  x: 25.4,
  y: 10.16,
};

// TODO: dynamically get variant colors from templates - or hardcode for other themes: block, valley
export const OCEAN_VARIANT_COLORS = ['4F88BC', '2FD1D8', '5A9BD4', '2870C1', '2B9FD4', 'F5F4F0'];