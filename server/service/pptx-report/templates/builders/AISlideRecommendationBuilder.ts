import { zodResponseFormat } from 'openai/helpers/zod';
import { UtrValueType } from '../../../../models/public/universalTrackerType';
import { getYear } from '../../../../util/date';
import { type AIModel, type AIPrompt, type AIResponse } from '../../../ai/models/AIModel';
import { PromptChainBuilder, type ChainStep } from '../../../ai/PromptChainBuilder';
import { SupportedMeasureUnits } from '../../../units/unitTypes';
import { ActionList } from '../../../utr/constants';
import { isInputData, isTableInputData, isValueListInputData } from '../PPTXTemplateInterface';
import { type PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { type SurveyAnswerGenerator } from './SurveyAnswerGenerator';
import {
  SlideSection,
  type AnsweredUtr,
  type CategorizationContent,
  type PPTXSlideRecommendation,
  type SlideCategorization,
  type SlideVisualizationParams,
  type VisualizationContent,
  slideCategorizationDto,
  slideVisualizationDto,
} from './types';

const DEFAULT_MAX_TOKEN = 10000;

export class AISlideRecommendationBuilder {
  constructor(
    private aiModel: AIModel,
    private repositoryManager: PPTXTemplateSurveyCacheManager,
    private surveyAnswerGenerator: SurveyAnswerGenerator
  ) {}

  public async getSlideRecommendations(): Promise<Map<SlideSection, PPTXSlideRecommendation[]>> {
    const answeredUtrs = await this.getAnsweredUtrs();

    if (answeredUtrs.length === 0) {
      return new Map();
    }
    // TODO: implement other sections (governance, social, economic) in the future
    const environmentSlideMap = new Map<number, PPTXSlideRecommendation>();

    const categorizationStep: ChainStep = {
      model: this.aiModel,
      maxTokens: DEFAULT_MAX_TOKEN,
      format: zodResponseFormat(slideCategorizationDto, 'PPTXSlideCategorization'),
      generatePrompt: async () => this.generateRelevantUtrsPrompt(answeredUtrs),
    };

    const visualizationStep: ChainStep<CategorizationContent> = {
      model: this.aiModel,
      maxTokens: DEFAULT_MAX_TOKEN,
      format: zodResponseFormat(slideVisualizationDto, 'PPTXSlideVisualization'),
      generatePrompt: async (response?: AIResponse<CategorizationContent>) => {
        if (!response?.content?.result || response.content.result.length === 0) {
          return;
        }
        const result: SlideVisualizationParams[] = [];
        for (const [index, slide] of response.content.result.entries()) {
          const { answers, unit, numberScale } = await this.getSlideInputs(slide.utrs);
          environmentSlideMap.set(index, {
            heading: slide.heading,
            category: slide.category,
            utrs: slide.utrs,
            unit,
            numberScale,
            displayFormat: null,
            chart: null,
            table: null,
          });
          result.push({
            id: index,
            category: slide.category,
            utrs: slide.utrs,
            answers,
          });
        }
        return this.generateVisualizationPrompt(result);
      },
    };

    const builder = new PromptChainBuilder([categorizationStep, visualizationStep]);
    const response = await builder.execute<VisualizationContent>();

    for (const slide of response.content.result) {
      const current = environmentSlideMap.get(slide.id);
      if (!current) {
        continue;
      }
      current.displayFormat = slide.displayFormat;
      current.chart = slide.chart;
      current.table = slide.table;
    }

    return new Map([[SlideSection.Environmental, Array.from(environmentSlideMap.values())]]);
  }

  private async getAnsweredUtrs() {
    const utrvsMap = await this.repositoryManager.getCachedSurvey()?.getUtrvsMap();

    if (!utrvsMap) {
      return [];
    }

    return Array.from(utrvsMap.values()).reduce((acc, utrv) => {
      // Skip private metrics from AI processing
      if (utrv.status === ActionList.Created || !utrv.universalTracker || utrv.isPrivate) {
        return acc;
      }

      acc.push({
        code: utrv.universalTracker.code,
        valueLabel: utrv.universalTracker.valueLabel,
        status: utrv.status,
        valueType: utrv.universalTracker.valueType,
        instructions: utrv.universalTracker.instructions,
        unitType: utrv.universalTracker.unitType,
        unit: utrv.universalTracker.unit,
        numberScale: utrv.universalTracker.numberScale,
        tableColumns: utrv.universalTracker.valueValidation?.table?.columns,
      });

      return acc;
    }, [] as AnsweredUtr[]);
  }

  private generateRelevantUtrsPrompt(answeredUtrs: AnsweredUtr[]): AIPrompt {
    const content = `You are an AI assistant that recommends the most impactful environmental metrics for a company's sustainability report.

      You will receive a JSON array of metric objects. Each object will have the following structure:
      {
        "code": "The unique code of the metric",
        "valueType": "The type of value the metric represents, such as ${Object.values(UtrValueType).join(', ')}",
        "valueLabel": "The title of the metric",
        "instructions": "Any specific instructions or context provided for the metric",
        "unitType": "The type of unit used for the metric, such as ${Object.values(SupportedMeasureUnits).join(
          ', '
        )}, etc.",
        "unit": "The specific unit of measurement",
        "numberScale": "The scale of the number, such as thousands, millions, etc."
        "status": "The status of the metric, such as ${Object.values(ActionList).join(', ')}"
        "tableColumns": "If the metric is a table, this is an array of the columns, each having a 'code' and a 'name'"
      }

      Here is the list of metrics: ${JSON.stringify(answeredUtrs)}

      Your primary task is to analyze a given list of company metrics and perform the following actions:

      Step 1: Classification
      - Iterate through the full list of input metrics. For each metric, determine if it is an environmental metric. Use the valueLabel, instructions, code, tableColumns and unitType fields to make this determination.
      - If 'tableColumns' is present, the metric is only considered environmental if the overall theme is confirmed to be environmental AND it contains at least one relevant environmental data column, which you will verify by inspecting each column's name, instructions, and unitType.
      - Keywords to look for: "Emissions", "GHG", "Scope 1", "Scope 2", "Scope 3", "Carbon", "CO2", "Energy", "Water", "Waste", "Effluents", "Recycling", "Consumption", "Spills", "Environmental", "Biodiversity", "Land use", "Air quality", "NOx", "SOx".
      - Explicitly discard any metric that is non-environmental, where the unitType is 'currency', or 'time' or other information indicates a social or economic topic (e.g., "Employee Turnover", "Revenue")
      - If the filtered list of environmental metrics is empty, the process stops here. Return a final JSON output of {"result": []} and do not proceed to the subsequent steps.

      Step 2: Scoring & Ranking
      - For each environmental metric identified in Step 1, evaluate its relevance from 0 to 100 and assign it to **score**. If the metric is not impactful enough for a high-level sustainability report, omit it from further processing.

      Step 3: Categorize
      - Group each scored environmental metric into a logical environmental category and assign it to **category** (e.g., 'GHG Emissions', 'Energy Management', 'Waste', 'Significant Air Emissions', 'Biodiversity').
      - Each group within a category must contain metrics that share the same 'valueType' and 'unitType'. This means a category (e.g., ‘Energy Management’) can have multiple groups with the same name, as long as they differ by 'valueType' or 'unitType'
      - If a metric does not fit into an existing category, create a new category

      Step 4: Output Generation and Filtering
      - From each category, you must select a strict maximum of the top three metrics based on the **score**
      - Construct: Create the final output as a single JSON object with a key named "result". The value of "result" must be an array, where each element represents one of the final categories.
      Each object in the array must strictly adhere to the following structure:
        {
          "category": "The name of the category",
          "heading": Generate a concise, descriptive heading with a maximum of 5 words that summarizes the category's metrics.
          "utrs": [{
            "utrCode": The 'code' from the original input metric,
            "valueType": "The 'valueType' from the original input metric",
          }],
        }
      As a final step, arrange the categories array of "result" in order of their environmental significance.  
      Exclude any commentary or explanation — return only the structured JSON result as specified
    `;

    return {
      role: 'system',
      content,
    };
  }

  private async generateVisualizationPrompt(result: SlideVisualizationParams[]): Promise<AIPrompt> {
    const year = await this.getYear();
    const prevYear = await this.getYear(-1);
    const content =
      'You are an AI data visualization expert. Your task is to analyze a JSON array of environmental metric categories and, for each category, recommend the most effective way to display the data.' +
      'You will receive a JSON array where each object represents a category of metrics. Each category object contains:' +
      `
      **id**: The unique identifier for the category.
      **category**: The name of the category.
      **utrs**: An array of metadata for the metrics in this category, including their 'utrCode' (The unique code of the metric) and 'valueType' (The type of value the metric represents, such as ${Object.values(
        UtrValueType
      ).join(', ')})
      **answers**: An object containing the actual answer data for the metrics, keyed by the metric's code and survey period ('_SURVEY_0' for the current period, which is ${year}, "SURVEY-1" for the previous period, which is ${prevYear})
      the value is an array of objects, each representing a data point for the metric, with the following structure: 
        {
          "value": The answer value for this row,
          "unit": The unit of the answer value,
          "numberScale": The scale of the number, such as thousands, millions, etc.
          "rowIndex": The index of the row in a table answer if metric's valueType is 'table'
          "valueListCode": The code of the column if metric's valueType is 'numericValueList' or 'table'
        }
      ` +
      `Here is the JSON input you must process: ${JSON.stringify(result)}` +
      'You will iterate through each object in the input array. For each object you process, you must generate a corresponding object for a final output array by following these steps:';
    `
    Step 1: Data Analysis and Strategy

    Examine the 'utrs' and 'answers' arrays for the category.
    Consider the number of metrics, their 'valueType', the complexity of the data (e.g., a single number vs. a multi-row table), and the availability of data for both the current and previous periods.
    Devise a strategy to best tell the story of the data. This requires performing calculations to create insightful summaries. Calculations can include, but are not limited to:
      - Aggregation: Summing individual metrics to create a total (e.g., summing Scope 1, 2, and 3 emissions).
      - Comparative Analysis: Calculating year-over-year (YoY) percentage changes.
      - Composition: Aggregating table data (e.g., summing hazardous and non-hazardous waste) to show parts of a whole.
    
    Step 2: Determine Display Format

    Based on your strategy, choose the optimal display format from three options and assign it to **displayFormat**:
    'chart': For high-level comparisons, trends, or showing composition.
    'table': For displaying precise values, detailed breakdowns, or complex datasets.
    'hybrid': When a chart provides a powerful visual summary, but a table is necessary for detail and transparency.

    Step 3: Construct the Output Object
    Create the final output as a single JSON object with a key named "result". The value of "result" must be an array, Your final output must be a single JSON array, where each object corresponds to an item from the input and contains your recommendations.
    Each object in the array must strictly adhere to the following structure:
     {
          "id": "The original id",
          "category": "The original category name",
          "displayFormat": "The chosen display format from 'chart', 'table', or 'hybrid'",
          "chart": If **displayFormat** is 'table', this must be null. If **displayFormat** is 'chart' or 'hybrid', this object must contain the following structure:
            {
              "type": "Determine the best chart type to visualize the data in each category, choose from: 'line', 'doughnut', 'bar', or 'column'",
              "title": "A concise, descriptive title for the chart with a maximum of 10 words, append unit and numberScale to the title in parentheses if applicable. For example: 'Emissions (thousands mt)'",
              "data": "An array of objects, each with 'name', 'labels' and 'values'. This data should be derived and calculated from the 'answers' to best summarize the story:
                  - 'name': A concise, descriptive name for the data series with a maximum of 5 words
                  - 'labels': An array of labels for each data point
                  - 'values': An array of numerical values corresponding to each label, where null represents a missing value"
            }
            "table": If **displayFormat** is 'chart', this must be null. If **displayFormat** is 'table' or 'hybrid', this object must contain the following structure:
              {
                "title": "A concise, descriptive title for the table with a maximum of 10 words",
                "columns": "An array of column header strings, append unit and numberScale to the title in parentheses if applicable. For example: ["Region", "Revenue (millions USD)" "Emissions (thousands mt)"]",
                "rows": "An array of arrays, each representing a row in the table. Each sub-array contains the values for each column."
              }
      }
      
    ` + 'Do not include any explanations or commentary outside of the JSON structure';
    return {
      role: 'system',
      content,
    };
  }

  private async getSlideInputs(utrs: SlideCategorization['utrs']) {
    const { utrsInputMap, unit, numberScale } = await this.surveyAnswerGenerator.getSlideAnswers(utrs, true);
    const answers: SlideVisualizationParams['answers'] = {};
    for (const [utrCodeWithOffset, input] of utrsInputMap) {
      if (isInputData(input)) {
        answers[utrCodeWithOffset] = [input];
        continue;
      }
      if (isValueListInputData(input)) {
        answers[utrCodeWithOffset] = input;
        continue;
      }
      // flatten table input
      if (isTableInputData(input)) {
        answers[utrCodeWithOffset] = input.flatMap((row, rowIndex) =>
          row.map((item) => ({
            valueListCode: item.code,
            value: item.value,
            unit: item.unit,
            numberScale: item.numberScale,
            rowIndex,
          }))
        );
      }
    }
    return { answers, unit, numberScale };
  }

  private async getYear(periodOffset: number = 0): Promise<number | null> {
    const survey = await this.repositoryManager.getCachedSurvey(periodOffset)?.getSurvey();
    if (survey) {
      return getYear(survey.effectiveDate);
    }
    if (periodOffset === 0) {
      return null;
    }
    const currentYear = await this.repositoryManager.getCachedSurvey(0)?.getSurvey();
    if (!currentYear) {
      return null;
    }
    return getYear(currentYear.effectiveDate) + periodOffset;
  }
}
