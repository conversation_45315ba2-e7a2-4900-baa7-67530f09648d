import { z } from 'zod';
import { UtrValueType, type TableColumn } from '../../../../models/public/universalTrackerType';
import { type UniversalTrackerPlain } from '../../../../models/universalTracker';
import { type InputData } from '../PPTXTemplateInterface';
import { type HelperUtrv } from '../PPTXTemplateSurveyCache';

export enum SlideSection {
  Environmental = 'environmental',
  Social = 'social',
  Governance = 'governance',
  Economic = 'economic',
}

export enum SlideChartType {
  Doughnut = 'doughnut',
  Bar = 'bar',
  Column = 'column',
  Line = 'line',
}

export enum PPTXDisplayFormat {
  Chart = 'chart',
  Table = 'table',
  Hybrid = 'hybrid',
}

export type AnsweredUtr = Pick<
  UniversalTrackerPlain,
  'code' | 'valueLabel' | 'valueType' | 'instructions' | 'unitType' | 'unit' | 'numberScale'
> &
  Pick<HelperUtrv, 'status'> & { tableColumns?: TableColumn[] };

export const slideCategorizationDto = z.object({
  result: z.array(
    z.object({
      category: z.string(),
      heading: z.string(),
      utrs: z.array(
        z.object({
          utrCode: z.string(),
          valueType: z.nativeEnum(UtrValueType), // todo: maybe unneeded?
        })
      ),
    })
  ),
});

export type CategorizationContent = z.infer<typeof slideCategorizationDto>;

export type SlideCategorization = CategorizationContent['result'][number];

export const slideVisualizationDto = z.object({
  result: z.array(
    z.object({
      id: z.number(),
      category: z.string(),
      displayFormat: z.nativeEnum(PPTXDisplayFormat),
      chart: z.union([
        z.null(),
        z.object({
          type: z.nativeEnum(SlideChartType),
          title: z.string(),
          data: z.array(
            z.object({
              name: z.string(),
              labels: z.array(z.string()),
              values: z.array(z.union([z.null(), z.number()])),
            })
          ),
        }),
      ]),
      table: z.union([
        z.null(),
        z.object({
          title: z.string(),
          columns: z.array(z.string()),
          rows: z.array(z.array(z.string())),
        }),
      ]),
    })
  ),
});

export type VisualizationContent = z.infer<typeof slideVisualizationDto>;

export type SlideVisualization = VisualizationContent['result'][number];

export interface PPTXSlideRecommendation
  extends Pick<SlideCategorization, 'category' | 'heading' | 'utrs'>,
    Pick<SlideVisualization, 'chart' | 'table'> {
  unit?: string;
  numberScale?: string;
  displayFormat: PPTXDisplayFormat | null;
}

export interface SlideVisualizationParams extends Pick<SlideCategorization, 'category' | 'utrs'> {
  id: number;
  answers: Record<string, (InputData & { valueListCode?: string; rowIndex?: number })[]>;
}
