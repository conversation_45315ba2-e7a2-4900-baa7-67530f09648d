/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { DEFAULT_TABLE_CELL_VALUE } from '../../constants';
import { DoubleMaterialityBuilder } from '../builders/materiality-tracker/DoubleMaterialityBuilder';
import { FinancialBuilder } from '../builders/materiality-tracker/FinancialBuilder';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';

const defaultRow = {
  values: [
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
    DEFAULT_TABLE_CELL_VALUE,
  ],
};

const MAX_ROWS = 10;

export const pptxMTConfigBoundaries = async (builder: FinancialBuilder): Promise<PPTXTemplateLayoutItem[]> => {
  return [
    {
      slideId: 6,
      slideRepeat: async () => Math.ceil(builder.getTopicsLength() / MAX_ROWS),
      tableReplacements: [
        [
          'TABLE_MT_BOUNDARIES',
          {
            rows: async ({ repeatNum = 1 }) => {
              const table = await (await builder.getTableBuilder()).getBoundariesTable(builder.getTopicsLength());
              if (!table) {
                return [defaultRow];
              }
              const offset = (repeatNum - 1) * MAX_ROWS;
              return [defaultRow, ...table.slice(offset, offset + MAX_ROWS)];
            },
          },
        ],
      ],
    },
  ];
};

export const pptxDoubleMaterialityBoundaries = async (builder: DoubleMaterialityBuilder): Promise<PPTXTemplateLayoutItem[]> => {
  return [
    {
      slideId: 6,
      slideRepeat: async () => Math.ceil(builder.getTopicsLength() / MAX_ROWS),
      tableReplacements: [
        [
          'TABLE_MT_BOUNDARIES',
          {
            rows: async ({ repeatNum = 1 }) => {
              const table = await (await builder.getTableBuilder()).getBoundariesTable(builder.getTopicsLength());
              if (!table) {
                return [defaultRow];
              }
              const offset = (repeatNum - 1) * MAX_ROWS;
              return [defaultRow, ...table.slice(offset, offset + MAX_ROWS)];
            },
          },
        ],
      ],
    },
  ];
};
