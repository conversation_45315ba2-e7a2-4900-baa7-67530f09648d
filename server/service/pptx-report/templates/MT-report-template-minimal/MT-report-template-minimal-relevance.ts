/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ESGCategory } from '../../../../models/materialTopics';
import { DoubleMaterialityBuilder } from '../builders/materiality-tracker/DoubleMaterialityBuilder';
import { FinancialBuilder } from '../builders/materiality-tracker/FinancialBuilder';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';

const MAX_ROWS = 10;

export const pptxMTConfigRelevantTopics = (builder: FinancialBuilder): PPTXTemplateLayoutItem[] => {
  const topTopics = builder.getRelevantTopTopics();
  const chartCategories = topTopics.map((topic) => {
    return {
      label: topic.name,
      values: Object.values(ESGCategory).map((category) =>
        category === topic.category && topic.relativeScore ? topic.relativeScore : ''
      ),
    };
  });

  return [
    {
      slideId: 4,
      slideRepeat: async () => Math.ceil(builder.getTopicsLength() / MAX_ROWS),
      chartReplacements: [
        [
          'CHART_RELEVANT_TOPICS',
          {
            chartData: async ({ repeatNum = 1 }) => {
              const offset = (repeatNum - 1) * MAX_ROWS;
              return {
                series: [
                  {
                    label: ESGCategory.Environmental,
                  },
                  {
                    label: ESGCategory.Social,
                  },
                  {
                    label: ESGCategory.Governance,
                  },
                ],
                categories: chartCategories.slice(offset, offset + MAX_ROWS),
              };
            },
          },
        ],
      ],
    },
  ];
};

export const pptxDoubleMaterialityRelevantTopics = (
  builder: DoubleMaterialityBuilder
): PPTXTemplateLayoutItem[] => {
  const topTopics = builder.getRelevantTopTopics();
  const chartCategories = topTopics.map((topic) => {
    return {
      label: topic.name,
      values: [topic.financialRelativeScore ?? 0, topic.nonFinancialRelativeScore ?? 0],
    };
  });

  return [
    {
      slideId: 4,
      slideRepeat: async () => Math.ceil(builder.getTopicsLength() / MAX_ROWS),
      chartReplacements: [
        [
          'CHART_RELEVANT_TOPICS',
          {
            chartData: async ({ repeatNum = 1 }) => {
              const offset = (repeatNum - 1) * MAX_ROWS;
              return {
                series: [
                  {
                    label: 'Financial Score',
                  },
                  {
                    label: 'Impact Score',
                  },
                ],
                categories: chartCategories.slice(offset, offset + MAX_ROWS),
              };
            },
          },
        ],
      ],
    },
  ];
};
