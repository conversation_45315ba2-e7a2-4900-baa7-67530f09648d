/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { MaterialPillar } from '../../../../models/materialTopics';
import { BaseBuilder } from '../builders/materiality-tracker/BaseBuilder';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';

export const pptxMTConfigPillars = async (builder: BaseBuilder): Promise<PPTXTemplateLayoutItem[]> => {
  const initiativeName = await builder.getCompanyName();
  const pillarTextReplacements = builder.getPillarsTextReplacement(initiativeName);
  return [
    {
      slideId: 7,
      textReplacements: pillarTextReplacements,
      chartReplacements: [
        [
          'PILLARS_CHART',
          {
            chartData: async () => {
              const pillarMap = builder.getRelevantTopTopics().reduce<Record<MaterialPillar, string[]>>(
                (acc, topic) => {
                  if (topic.pillars) {
                    topic.pillars.forEach((pillar) => {
                      acc[pillar].push(topic.name);
                    })
                  }
                  return acc;
                },
                {
                  [MaterialPillar.People]: [],
                  [MaterialPillar.Partnership]: [],
                  [MaterialPillar.Prosperity]: [],
                  [MaterialPillar.Planet]: [],
                  [MaterialPillar.Principle]: [],
                }
              );
              const categories = Object.entries(pillarMap).map(([pillar, topics]) => ({
                label: pillar.toUpperCase(),
                values: [topics.length],
              }));

              return {
                series: [
                  {
                    label: 'KEY PILLARS',
                  },
                ],
                categories,
              };
            },
          },
        ],
      ],
    },
    {
      slideId: 8,
      textReplacements: pillarTextReplacements,
    },
  ];
};
