/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const CT_STARTER_GOVERNANCE_SLIDE_UTRS = {
  31: {
    utrs: [{ utrCode: 'gri/2020/405-1/a' }],
  },
  32: {
    utrs: [{ utrCode: 'gri/2020/205-2/c' }, { utrCode: 'gri/2020/205-2/d' }],
  },
  33: {
    utrs: [{ utrCode: 'gri/2020/418-1/a' }, { utrCode: 'gri/2020/407-1/a' }],
  },
};

export const getPPTXConfigGovernance = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Governance
    slideId: 30,
  },
  // Board profile
  {
    slideId: 31,
    appendix: async () => CT_STARTER_GOVERNANCE_SLIDE_UTRS[31].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const boardComposition = await builder.createUTRTableBuilder('gri/2020/405-1/a', 31).toJSONString();
            return builder
              .createAIBuilder()
              .ask([
                `As a company diversity and equal opportunities are important to me.`,
                `Here is information the composition of the company board:`,
                ...(boardComposition ? ['', boardComposition, ''] : []),
                `Write a narrative about this but totally ignore any information about gender.`,
              ])
              .addTarget('gri/2020/405-1/a')
              .addFurtherExplanation('gri/2020/405-1/a')
              .max(200)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const boardComposition = await builder.createUTRTableBuilder('gri/2020/405-1/a', 31).toJSONString();
            return builder
              .createAIBuilder()
              .ask([
                `As a company diversity and equal opportunities are important to me.`,
                `Here is information the gender composition of the company board:`,
                ...(boardComposition ? ['', boardComposition, ''] : []),
                `Write a narrative about this but totally ignore any information that isn't about gender.`,
              ])
              .addTarget('gri/2020/405-1/a')
              .addFurtherExplanation('gri/2020/405-1/a')
              .max(100)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART',
        {
          chartData: async () => {
            const male = await builder.createUTRTableBuilder('gri/2020/405-1/a', 31).multiply('number_members', 'male');
            const female = await builder
              .createUTRTableBuilder('gri/2020/405-1/a', 31)
              .multiply('number_members', 'female');
            return {
              series: [
                {
                  label: String(await builder.getYear()),
                },
              ],
              categories: [
                {
                  label: 'Male',
                  values: [male],
                },
                {
                  label: 'Female',
                  values: [female],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  // Anti-corruption
  {
    slideId: 32,
    appendix: async () => CT_STARTER_GOVERNANCE_SLIDE_UTRS[32].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const antiCorruption1 = await builder.createUTRTableBuilder('gri/2020/205-2/c', 32).toJSONString();
            const antiCorruption2 = await builder.createUTRTableBuilder('gri/2020/205-2/d', 32).toJSONString();
            return builder
              .createAIBuilder()
              .ask([`As a company anti-corruption is an important part of our good governance measures.`])
              .appendConditional(
                `Here is information about our anti-corruption measures:`,

                !!antiCorruption1 || !!antiCorruption2
              )
              .appendConditional(antiCorruption1)
              .appendConditional(antiCorruption2)
              .addFurtherExplanation('gri/2020/205-2/c')
              .addFurtherExplanation('gri/2020/205-2/d')
              .narrative()
              .max(150)
              .min(100)
              .exec();
          },
        },
      ],
    ],
  },
  {
    // Complaints / Rights
    slideId: 33,
    appendix: async () => CT_STARTER_GOVERNANCE_SLIDE_UTRS[33].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName = await builder.createUTRBuilder('gri/2020/418-1/a').getUtrName();
            const total = await builder.createUTRBuilder('gri/2020/418-1/a', 33).getTotalFromNumericValueList();
            return builder
              .createAIBuilder()
              .ask([
                `Create a clear narrative explaining ${utrName}. Focus on data insights and creating a summary of how many complaints there have been: ${
                  total ?? 'Not Reported'
                }`,
                'Focus on data provided and any year on year change if available',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('gri/2020/418-1/a')
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const utrName = await builder.createUTRBuilder('gri/2020/407-1/a').getUtrName();
            const risk = await builder.createUTRTableBuilder('gri/2020/407-1/a', 33).toJSONString();
            return builder
              .createAIBuilder()
              .ask([
                `Create a clear narrative explaining ${utrName}.`,
                `Focus on data insights and creating a summary of how many risks there are and what they are: ${
                  risk ?? 'Not Reported'
                }`,
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('gri/2020/407-1/a')
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
    ],
  },
];
