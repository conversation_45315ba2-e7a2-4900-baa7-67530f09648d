/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { getValueByColumnCode, getValueFromDivision } from '../CT-report-template-minimal/utils';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const CT_STARTER_ECONOMIC_SLIDE_UTRS = {
  11: {
    utrs: [
      { utrCode: 'gri/2020/201-1/a', columnCodes: ['economic_value_revenues'] },
      { utrCode: 'survey/sdg/9.5/pc-opex-research-and-development', columnCodes: ['r_and_d'] },
      { utrCode: 'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives' },
    ],
  },
  12: {
    utrs: [{ utrCode: 'gri/2020/201-1/a', columnCodes: ['economic_value_revenues', 'econ_distributed'] }],
  },
  13: {
    utrs: [
      { utrCode: 'survey/sdg/12.5/does-company-implement-circular-business-model' },
      { utrCode: 'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives' },
      { utrCode: 'gri/2020/204-1/a' },
    ],
  },

  14: {
    utrs: [
      { utrCode: 'survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption' },
      { utrCode: 'survey/sdg/9.5/pc-opex-research-and-development', columnCodes: ['r_and_d'] },
      { utrCode: 'survey/sdg/13.1/pc-r-d-technology-spend' },
      { utrCode: 'survey/sdg/13.1/pc-r-d-technology' },
    ],
  },
  15: {
    utrs: [
      {
        utrCode: 'survey/sdg/17.9/spend-partnerships-developing-countries',
        columnCodes: ['investment', 'partnership'],
      },
    ],
    previousSurveyIncluded: false,
  },
  16: {
    utrs: [
      {
        utrCode: 'gri/2020/207-4/b',
        columnCodes: [
          'financial_info_cash_basis_income_tax',
          'financial_info_resident_entities',
          'financial_info_number_employees',
        ],
      },
    ],
  },
};

export const getPPTXConfigEconomic = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  // Economic
  {
    slideId: 10,
  },
  // Economic Summary
  {
    slideId: 11,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[11].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName1 = await builder.createUTRBuilder('gri/2020/201-1/a').getUtrName();
            const utrName2 = await builder
              .createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .getUtrName();
            const utrName3 = await builder
              .createUTRBuilder('survey/sdg/9.5/pc-opex-research-and-development')
              .getUtrName();
            return builder
              .createAIBuilder()
              .ask([
                'Create a clear narrative explaining a list of metrics: ',
                [utrName1, utrName2, utrName3].join(', '),
                'Focus on data insights and creating a summary of key economic indicators.',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('gri/2020/201-1/a')
              .addFurtherExplanation('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .addFurtherExplanation('survey/sdg/9.5/pc-opex-research-and-development')
              .addTarget('gri/2020/201-1/a')
              .addTarget('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .addTarget('survey/sdg/9.5/pc-opex-research-and-development')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_REVENUE',
        {
          text: async () => {
            const prevTotalRevenue = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 11)
              .periodOffset(-1)
              .getRawValue('economic_value_revenues');
            builder.setKeyStore('PREV_YEAR_TOTAL_REVENUE', prevTotalRevenue);
            return prevTotalRevenue;
          },
        },
      ],
      [
        'YEAR_TOTAL_REVENUE',
        {
          text: async () => {
            const totalRevenue = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 11)
              .getRawValue('economic_value_revenues');
            builder.setKeyStore('YEAR_TOTAL_REVENUE', totalRevenue);
            return totalRevenue;
          },
        },
      ],
      [
        'CHANGE_TOTAL_REVENUE',
        {
          text: async () => {
            const prevTotalRevenue = Number(builder.getKeyStore('PREV_YEAR_TOTAL_REVENUE'));
            const totalRevenue = Number(builder.getKeyStore('YEAR_TOTAL_REVENUE'));
            return builder.getChangeText(prevTotalRevenue, totalRevenue);
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_LCR',
        {
          text: async () =>
            builder
              .createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', 11)
              .periodOffset(-1)
              .getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_LCR',
        {
          text: async () =>
            builder.createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', 11).getAsNumber(),
        },
      ],
      [
        'CHANGE_ECONOMIC_LCR',
        {
          text: async () =>
            builder.getUTRValueChange('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', { slideId: 11 }),
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_RND',
        {
          text: async () => {
            const prevEconomicRND = await builder
              .createUTRTableBuilder('survey/sdg/9.5/pc-opex-research-and-development', 11)
              .periodOffset(-1)
              .getAsNumber('r_and_d');
            builder.setKeyStore('PREV_YEAR_ECONOMIC_RND', prevEconomicRND);
            return prevEconomicRND;
          },
        },
      ],
      [
        'YEAR_ECONOMIC_RND',
        {
          text: async () => {
            const economicRND = await builder
              .createUTRTableBuilder('survey/sdg/9.5/pc-opex-research-and-development', 11)
              .getAsNumber('r_and_d');
            builder.setKeyStore('YEAR_ECONOMIC_RND', economicRND);
            return economicRND;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_RND',
        {
          text: async () => {
            const prevEconomicRND = Number(builder.getKeyStore('PREV_YEAR_ECONOMIC_RND'));
            const economicRND = Number(builder.getKeyStore('YEAR_ECONOMIC_RND'));
            return builder.getChangeText(prevEconomicRND, economicRND);
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 11 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const year = await builder.getYear();
            const prevYear = await builder.getYear(-1);
            const prevValue = builder.getKeyStore('PREV_YEAR_TOTAL_REVENUE') ?? '';
            const currentValue = builder.getKeyStore('YEAR_TOTAL_REVENUE') ?? '';

            return {
              series: [
                {
                  label: 'Total Revenue',
                },
              ],
              categories: [
                {
                  label: String(prevYear),
                  values: [prevValue],
                },
                {
                  label: String(year),
                  values: [currentValue],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  // Creating Value for All
  {
    slideId: 12,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[12].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName = await builder.createUTRBuilder('gri/2020/201-1/a').getUtrName();
            return builder
              .createAIBuilder()
              .ask([
                `Create a clear narrative explaining ${utrName}.`,
                'Focus on data insights and creating a summary of economic impact in relation to Direct economic value generated and distributed (EVG&D).',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('gri/2020/201-1/a')
              .addTarget('gri/2020/201-1/a')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_REVENUE',
        {
          text: async () => {
            const prevTotalRevenue = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 12)
              .periodOffset(-1)
              .getRawValue('economic_value_revenues');
            builder.setKeyStore('PREV_YEAR_TOTAL_REVENUE_12', prevTotalRevenue);
            return prevTotalRevenue;
          },
        },
      ],
      [
        'YEAR_TOTAL_REVENUE',
        {
          text: async () => {
            const totalRevenue = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 12)
              .getRawValue('economic_value_revenues');
            builder.setKeyStore('YEAR_TOTAL_REVENUE_12', totalRevenue);
            return totalRevenue;
          },
        },
      ],
      [
        'CHANGE_TOTAL_REVENUE',
        {
          text: async () => {
            const prevTotalRevenue = Number(builder.getKeyStore('PREV_YEAR_TOTAL_REVENUE_12'));
            const totalRevenue = Number(builder.getKeyStore('YEAR_TOTAL_REVENUE_12'));
            return builder.getChangeText(prevTotalRevenue, totalRevenue);
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_EVD',
        {
          text: async () => {
            const prevEconomicEVD = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 12)
              .periodOffset(-1)
              .getRawValue('econ_distributed');
            builder.setKeyStore('PREV_YEAR_ECONOMIC_EVD', prevEconomicEVD);
            return prevEconomicEVD;
          },
        },
      ],
      [
        'YEAR_ECONOMIC_EVD',
        {
          text: async () => {
            const economicEVD = await builder
              .createUTRTableBuilder('gri/2020/201-1/a', 12)
              .getRawValue('econ_distributed');
            builder.setKeyStore('YEAR_ECONOMIC_EVD', economicEVD);
            return economicEVD;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_EVD',
        {
          text: async () => {
            const prevEconomicEVD = Number(builder.getKeyStore('PREV_YEAR_ECONOMIC_EVD'));
            const economicEVD = Number(builder.getKeyStore('YEAR_ECONOMIC_EVD'));
            return builder.getChangeText(prevEconomicEVD, economicEVD);
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_EVDPC',
        {
          text: async () => {
            const prevEconomicEVD = Number(builder.getKeyStore('PREV_YEAR_ECONOMIC_EVD'));
            const prevTotalRevenue = Number(builder.getKeyStore('PREV_YEAR_TOTAL_REVENUE_12'));
            const prevPercentage = builder.getPercentageText(prevEconomicEVD, prevTotalRevenue);
            builder.setKeyStore('PREV_YEAR_ECONOMIC_EVDPC', prevPercentage);
            return prevPercentage;
          },
        },
      ],
      [
        'YEAR_ECONOMIC_EVDPC',
        {
          text: async () => {
            const economicEVD = Number(builder.getKeyStore('YEAR_ECONOMIC_EVD'));
            const totalRevenue = Number(builder.getKeyStore('YEAR_TOTAL_REVENUE_12'));
            const percentage = builder.getPercentageText(economicEVD, totalRevenue);
            builder.setKeyStore('YEAR_ECONOMIC_EVDPC', percentage);
            return percentage;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_EVDPC',
        {
          text: async () => {
            const prevValue = Number(builder.getKeyStore('PREV_YEAR_ECONOMIC_EVDPC'));
            const value = Number(builder.getKeyStore('YEAR_ECONOMIC_EVDPC'));
            return builder.getChangeText(prevValue, value);
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => {
            return builder.getUnitAndNumberScale({ slideId: 12 });
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const year = await builder.getYear();
            const prevYear = await builder.getYear(-1);
            const prevTotalRevenue = builder.getKeyStore('PREV_YEAR_TOTAL_REVENUE_12') ?? '';
            const totalRevenue = builder.getKeyStore('YEAR_TOTAL_REVENUE_12') ?? '';
            const prevEconomicEVD = builder.getKeyStore('PREV_YEAR_ECONOMIC_EVD') ?? '';
            const economicEVD = builder.getKeyStore('YEAR_ECONOMIC_EVD') ?? '';

            return {
              series: [
                {
                  label: 'Total Revenue',
                },
                {
                  label: 'Economic value distributed',
                },
              ],
              categories: [
                {
                  label: String(prevYear),
                  values: [prevTotalRevenue, prevEconomicEVD],
                },
                {
                  label: String(year),
                  values: [totalRevenue, economicEVD],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  // Sustainable Sales & Local Procurement
  {
    slideId: 13,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[13].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const initiative = await builder.getInitiative();

            const utrName1 = await builder
              .createUTRBuilder('survey/sdg/12.5/does-company-implement-circular-business-model')
              .getUtrName();
            const utrName2 = await builder
              .createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .getUtrName();
            const utrName3 = await builder.createUTRBuilder('gri/2020/204-1/a').getUtrName();

            return builder
              .createAIBuilder()
              .ask([
                'Create a clear narrative explaining a list of metrics: ',
                [utrName1, utrName2, utrName3].join(', '),
                `Focus on data insights and creating a summary of sustainable initiatives and impact back to local regions where ${initiative.name} operates.`,
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('survey/sdg/12.5/does-company-implement-circular-business-model')
              .addFurtherExplanation('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .addFurtherExplanation('gri/2020/204-1/a')
              .addTarget('survey/sdg/12.5/does-company-implement-circular-business-model')
              .addTarget('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives')
              .addTarget('gri/2020/204-1/a')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_CIRCULAR',
        {
          text: async () =>
            builder
              .createUTRBuilder('survey/sdg/12.5/does-company-implement-circular-business-model', 13)
              .periodOffset(-1)
              .getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_CIRCULAR',
        {
          text: async () => {
            const circular = await builder
              .createUTRBuilder('survey/sdg/12.5/does-company-implement-circular-business-model', 13)
              .getAsNumber();
            builder.setKeyStore('YEAR_ECONOMIC_CIRCULAR', circular);
            return circular;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_CIRCULAR',
        {
          text: async () =>
            builder.getUTRValueChange('survey/sdg/12.5/does-company-implement-circular-business-model', {
              slideId: 13,
            }),
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_LOCAL',
        {
          text: async () => builder.createUTRBuilder('gri/2020/204-1/a', 13).periodOffset(-1).getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_LOCAL',
        {
          text: async () => {
            const localProcurement = await builder.createUTRBuilder('gri/2020/204-1/a', 13).getAsNumber();
            builder.setKeyStore('YEAR_ECONOMIC_LOCAL', localProcurement);
            return localProcurement;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_LOCAL',
        {
          text: async () => builder.getUTRValueChange('gri/2020/204-1/a', { slideId: 13 }),
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_LCR',
        {
          text: async () =>
            builder
              .createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', 13)
              .periodOffset(-1)
              .getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_LCR',
        {
          text: async () =>
            builder.createUTRBuilder('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', 13).getAsNumber(),
        },
      ],
      [
        'CHANGE_ECONOMIC_LCR',
        {
          text: async () =>
            builder.getUTRValueChange('survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives', { slideId: 13 }),
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 13 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART_1',
        {
          chartData: async () => {
            const circular = Number(builder.getKeyStore('YEAR_ECONOMIC_CIRCULAR') ?? 0);
            const nonCircular = 100 - circular;

            return {
              series: [
                {
                  label: 'Circular v Less Sustainable Sales',
                },
              ],
              categories: [
                {
                  label: 'Circular',
                  values: [circular],
                },
                {
                  label: 'Non-Circular',
                  values: [nonCircular],
                },
              ],
            };
          },
        },
      ],
      [
        'DONUT_CHART_2',
        {
          chartData: async () => {
            const localProcurement = Number(builder.getKeyStore('YEAR_ECONOMIC_LOCAL') ?? 0);
            const other = 100 - localProcurement;

            return {
              series: [
                {
                  label: 'Local v Other Procurement',
                },
              ],
              categories: [
                {
                  label: 'Local',
                  values: [localProcurement],
                },
                {
                  label: 'Other',
                  values: [other],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  // Research & Development
  {
    slideId: 14,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[14].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName1 = await builder
              .createUTRBuilder('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption')
              .getUtrName();
            const utrName2 = await builder
              .createUTRBuilder('survey/sdg/9.5/pc-opex-research-and-development')
              .getUtrName();
            const utrName3 = await builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology-spend').getUtrName();
            const utrName4 = await builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology').getUtrName();

            return builder
              .createAIBuilder()
              .ask([
                'Create a clear narrative explaining a list of metrics: ',
                [utrName1, utrName2, utrName3, utrName4].join(', '),
                'Focus on data insights and creating a summary of the company’s research and development spending and how it relates to Sustainability and sustainable initiatives.',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption')
              .addFurtherExplanation('survey/sdg/9.5/pc-opex-research-and-development')
              .addFurtherExplanation('survey/sdg/13.1/pc-r-d-technology-spend')
              .addFurtherExplanation('survey/sdg/13.1/pc-r-d-technology')
              .addTarget('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption')
              .addTarget('survey/sdg/9.5/pc-opex-research-and-development')
              .addTarget('survey/sdg/13.1/pc-r-d-technology-spend')
              .addTarget('survey/sdg/13.1/pc-r-d-technology')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_RND',
        {
          text: async () => {
            const prevEconomicRND = await builder
              .createUTRTableBuilder('survey/sdg/9.5/pc-opex-research-and-development', 14)
              .periodOffset(-1)
              .getAsNumber('r_and_d');
            builder.setKeyStore('PREV_YEAR_ECONOMIC_RND', prevEconomicRND);
            return prevEconomicRND;
          },
        },
      ],
      [
        'YEAR_ECONOMIC_RND',
        {
          text: async () => {
            const economicRND = await builder
              .createUTRTableBuilder('survey/sdg/9.5/pc-opex-research-and-development', 14)
              .getAsNumber('r_and_d');
            builder.setKeyStore('YEAR_ECONOMIC_RND', economicRND);
            return economicRND;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_RND',
        {
          text: async () => {
            const prevEconomicRND = Number(builder.getKeyStore('PREV_YEAR_ECONOMIC_RND'));
            const economicRND = Number(builder.getKeyStore('YEAR_ECONOMIC_RND'));
            return builder.getChangeText(prevEconomicRND, economicRND);
          },
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_SUSP',
        {
          text: async () =>
            builder
              .createUTRBuilder('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption', 14)
              .periodOffset(-1)
              .getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_SUSP',
        {
          text: async () => {
            const SUSPValue = await builder
              .createUTRBuilder('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption', 14)
              .getAsNumber();
            builder.setKeyStore('YEAR_ECONOMIC_SUSP', SUSPValue);
            return SUSPValue;
          },
        },
      ],
      [
        'CHANGE_ECONOMIC_SUSP',
        {
          text: async () =>
            builder.getUTRValueChange('survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption', { slideId: 14 }),
        },
      ],
      [
        'PREV_YEAR_ECONOMIC_LCT',
        {
          text: async () =>
            builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology-spend', 14).periodOffset(-1).getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_LCT',
        {
          text: async () => builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology-spend', 14).getAsNumber(),
        },
      ],
      [
        'CHANGE_ECONOMIC_LCT',
        { text: async () => builder.getUTRValueChange('survey/sdg/13.1/pc-r-d-technology-spend', { slideId: 14 }) },
      ],
      [
        'PREV_YEAR_ECONOMIC_ENERGY',
        {
          text: async () =>
            builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology', 14).periodOffset(-1).getAsNumber(),
        },
      ],
      [
        'YEAR_ECONOMIC_ENERGY',
        {
          text: async () => builder.createUTRBuilder('survey/sdg/13.1/pc-r-d-technology', 14).getAsNumber(),
        },
      ],
      [
        'CHANGE_ECONOMIC_ENERGY',
        { text: async () => builder.getUTRValueChange('survey/sdg/13.1/pc-r-d-technology', { slideId: 14 }) },
      ],
      ['UNIT_NUMBERSCALE', { text: async () => builder.getUnitAndNumberScale({ slideId: 14 }) }],
    ],
    chartReplacements: [
      [
        'DONUT_CHART',
        {
          chartData: async () => {
            const year = await builder.getYear();
            const totalValue = Number(builder.getKeyStore('YEAR_ECONOMIC_RND') ?? 0);
            const SUSPValue = Number(builder.getKeyStore('YEAR_ECONOMIC_SUSP') ?? 0);

            return {
              series: [
                {
                  label: String(year),
                },
              ],
              categories: [
                {
                  label: 'Circular',
                  values: [totalValue],
                },
                {
                  label: 'Non-Circular',
                  values: [SUSPValue],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  // Partnerships
  {
    slideId: 15,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[15].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName = await builder
              .createUTRBuilder('survey/sdg/17.9/spend-partnerships-developing-countries')
              .getUtrName();
            return builder
              .createAIBuilder()
              .ask([
                `Create a clear narrative explaining ${utrName}`,
                'Focus on data insights and creating a summary of the company’s global partnerships and how it relates to Sustainability in business. ',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('survey/sdg/17.9/spend-partnerships-developing-countries')
              .addTarget('survey/sdg/17.9/spend-partnerships-developing-countries')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => {
            return builder.getUnitAndNumberScale({ slideId: 15 });
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const tableData = await builder
              .createUTRTableBuilder('survey/sdg/17.9/spend-partnerships-developing-countries', 15)
              .getSortedTable({
                columnCode: 'investment',
                limit: 3,
              });

            const categories = tableData.length
              ? tableData.map((row) => {
                  return {
                    label: String(row.find((c) => c.code === 'partnership')?.value ?? 'Partnership name'),
                    values: [row.find((c) => c.code === 'investment')?.value ?? ''],
                  };
                })
              : [{ label: 'Partnership name', values: [''] }];

            return {
              series: [
                {
                  label: 'Revenue',
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  {
    // Tax
    slideId: 16,
    appendix: async () => CT_STARTER_ECONOMIC_SLIDE_UTRS[16].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const utrName = await builder.createUTRBuilder('gri/2020/207-4/b').getUtrName();
            return builder
              .createAIBuilder()
              .ask([
                `Create a clear narrative explaining ${utrName}`,
                'Focus on data insights and creating a summary of the company’s top paying tax regions and how it relates to Sustainability in business. ',
              ])
              .and('Use straightforward business language.')
              .and('Be concise and factual.')
              .addFurtherExplanation('gri/2020/207-4/b')
              .addTarget('gri/2020/207-4/b')
              .and("Don't say what you can't do.")
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'REGION_1',
        {
          text: async () => {
            const tableData = await builder.createUTRTableBuilder('gri/2020/207-4/b', 16).getSortedTable({
              columnCode: 'financial_info_cash_basis_income_tax',
              limit: 3,
            });
            builder.setTableStore('SORTED_TAX_TABLE', tableData);
            if (!tableData.length) {
              return '-';
            }
            return getValueByColumnCode({ tableData, columnCode: 'financial_info_resident_entities' }) || '-';
          },
        },
      ],
      [
        'ECONOMIC_TAX_1',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return getValueByColumnCode({ tableData, columnCode: 'financial_info_cash_basis_income_tax' }) || '-';
          },
        },
      ],
      [
        'ECONOMIC_TAXPE_1',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return getValueFromDivision({
              tableData,
              numeratorCode: 'financial_info_cash_basis_income_tax',
              denominatorCode: 'financial_info_number_employees',
            });
          },
        },
      ],
      [
        'REGION_2',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return (
              getValueByColumnCode({ tableData, columnCode: 'financial_info_resident_entities', rowIndex: 1 }) || '-'
            );
          },
        },
      ],
      [
        'ECONOMIC_TAX_2',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return (
              getValueByColumnCode({ tableData, columnCode: 'financial_info_cash_basis_income_tax', rowIndex: 1 }) ||
              '-'
            );
          },
        },
      ],
      [
        'ECONOMIC_TAXPE_2',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return getValueFromDivision({
              tableData,
              numeratorCode: 'financial_info_cash_basis_income_tax',
              denominatorCode: 'financial_info_number_employees',
              rowIndex: 1,
            });
          },
        },
      ],
      [
        'REGION_3',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return (
              getValueByColumnCode({ tableData, columnCode: 'financial_info_resident_entities', rowIndex: 2 }) || '-'
            );
          },
        },
      ],
      [
        'ECONOMIC_TAX_3',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return (
              getValueByColumnCode({ tableData, columnCode: 'financial_info_cash_basis_income_tax', rowIndex: 2 }) ||
              '-'
            );
          },
        },
      ],
      [
        'ECONOMIC_TAXPE_3',
        {
          text: async () => {
            const tableData = builder.getTableStore('SORTED_TAX_TABLE');
            if (!tableData?.length) {
              return '-';
            }
            return getValueFromDivision({
              tableData,
              numeratorCode: 'financial_info_cash_basis_income_tax',
              denominatorCode: 'financial_info_number_employees',
              rowIndex: 2,
            });
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 16 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const prevYear = await builder.getYear(-1);
            const year = await builder.getYear();

            const prevTotalTax = await builder
              .createUTRTableBuilder('gri/2020/207-4/b', 16)
              .periodOffset(-1)
              .sum(['financial_info_cash_basis_income_tax']);
            const totalTax = await builder
              .createUTRTableBuilder('gri/2020/207-4/b', 16)
              .sum(['financial_info_cash_basis_income_tax']);

            return {
              series: [
                {
                  label: 'Total tax contribution',
                },
              ],
              categories: [
                {
                  label: String(prevYear),
                  values: [prevTotalTax],
                },
                {
                  label: String(year),
                  values: [totalTax],
                },
              ],
            };
          },
        },
      ],
    ],
  },
];
