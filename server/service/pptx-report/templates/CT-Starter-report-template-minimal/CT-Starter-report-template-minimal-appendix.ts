/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

const MAX_ROWS = 12;
const GRI_MAX_ROWS = 6;
const OTHER_MAX_ROWS = 4;

export const getPPTXConfigAppendix = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Appendix
    slideId: 35,
  },
  {
    // Appendix 1: GRI index
    slideId: 36,
    skip: async () => builder.getAppendixTable('gri2021') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('gri2021');
      if (appendixTable && appendixTable.length > GRI_MAX_ROWS) {
        return Math.ceil(appendixTable.length / GRI_MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('gri2021');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * GRI_MAX_ROWS;
            return [
              { values: ['GRI Material Topics 2021', 'GRI Code(s)', 'Slide number'] },
              ...appendixTable.slice(offset, offset + GRI_MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 2: SDG target alignment
    slideId: 37,
    skip: async () => builder.getAppendixTable('sdg') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('sdg');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('sdg');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [
              { values: ['SDG', 'SDG targets', 'Slide number'] },
              ...appendixTable.slice(offset, offset + MAX_ROWS),
            ];
          },
        },
      ],
    ],
  },
  {
    // Appendix 3: SASB index
    slideId: 38,
    skip: async () => builder.getAppendixTable('sasb') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('sasb');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('sasb');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [{ values: ['SASB', 'Slide number'] }, ...appendixTable.slice(offset, offset + MAX_ROWS)];
          },
        },
      ],
    ],
  },
  {
    // Appendix 4: TCFD index
    slideId: 39,
    skip: async () => builder.getAppendixTable('tcfd_standard') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('tcfd_standard');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        'APPENDIX_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('tcfd_standard');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * MAX_ROWS;
            return [{ values: ['TCFD', 'Slide number'] }, ...appendixTable.slice(offset, offset + MAX_ROWS)];
          },
        },
      ],
    ],
  },
  {
    // Appendix 5: Others
    slideId: 40,
    skip: async () => builder.getAppendixTable('tcfd_standard') === null,
    slideRepeat: async () => {
      const appendixTable = builder.getAppendixTable('tcfd_standard');
      if (appendixTable && appendixTable.length > MAX_ROWS) {
        return Math.ceil(appendixTable.length / MAX_ROWS);
      }
      return 1;
    },
    tableReplacements: [
      [
        // un_gc
        'APPENDIX_1_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('un_gc');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * OTHER_MAX_ROWS;
            return [
              { values: ['UN Global Compact', 'Slide number'] },
              ...appendixTable.slice(offset, offset + OTHER_MAX_ROWS),
            ];
          },
        },
      ],
      [
        // unctad
        'APPENDIX_2_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('unctad');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * OTHER_MAX_ROWS;
            return [{ values: ['UNCTAD', 'Slide number'] }, ...appendixTable.slice(offset, offset + OTHER_MAX_ROWS)];
          },
        },
      ],
      [
        // cdp
        'APPENDIX_3_TABLE',
        {
          rows: async ({ repeatNum = 1 }) => {
            const appendixTable = builder.getAppendixTable('cdp');
            if (!appendixTable) {
              return [];
            }
            const offset = (repeatNum - 1) * OTHER_MAX_ROWS;
            return [{ values: ['CDP', 'Slide number'] }, ...appendixTable.slice(offset, offset + OTHER_MAX_ROWS)];
          },
        },
      ],
    ],
  },
];
