/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigCover = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Title
    slideId: 1,
    imageReplacements: [['COMPANY_LOGO', { imageUrl: () => builder.getCompanyLogoUrl() }]],
  },
  {
    // Disclaimer
    slideId: 34,
  },
];
