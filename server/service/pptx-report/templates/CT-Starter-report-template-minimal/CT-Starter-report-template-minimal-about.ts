/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigAbout = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // About
    slideId: 3,
  },
  {
    // About the company
    slideId: 4,
    textReplacements: [
      [
        'ABOUT_THE_COMPANY',
        {
          text: async () => {
            const company = await builder.getCompanyInfo();
            let prompt = `Write about ${company.name}`;
            if (company.industry) {
              prompt += `The company is a ${company.industry} company.`;
            }
            if (company.missionStatement) {
              prompt += `The company's mission is to ${company.missionStatement}.`;
            }
            if (company.geoLocation) {
              prompt += `The company is located in ${company.geoLocation}.`;
            }
            if (company.description) {
              prompt += `The company's description is: ${company.description}.`;
            }
            return builder.createAIBuilder().ask(prompt).and(``).max(200).exec();
          },
        },
      ],
    ],
    imageReplacements: [['COMPANY_LOGO', { imageUrl: () => builder.getCompanyLogoUrl() }]],
  },
  {
    // About this report 1
    slideId: 5,
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => 'Reporting period',
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const start = await builder.getReportingPeriodStart();
            const end = await builder.getReportingPeriodEnd();
            const year = await builder.getYear();
            return (
              `The Report covers the financial period ended ${end} (“FY${year}”) and highlights the activities on the company's ` +
              `business operations commencing from ${start} to ${end} unless otherwise stated. For selected performance indicators ` +
              `that have been historically tracked, we have included data from the past three years.`
            );
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => 'Report feedback',
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const companyName = await builder.getCompanyName();
            return (
              `${companyName} welcomes feedback on our Sustainability Report. Kindly contact us about any feedback you might have. ` +
              `Full contact details are available on our website.`
            );
          },
        },
      ],
    ],
  },
  {
    // About this report 2
    slideId: 6,
    appendix: async () => ['gri/2020/102-25/b'],
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => 'Conflicts of Interest',
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const crossBoardMembership = await builder
              .createUTRTableBuilder('gri/2020/102-25/b')
              .getAsString('cross_board');
            const crossShareholding = await builder
              .createUTRTableBuilder('gri/2020/102-25/b')
              .getAsString('cross_shareholding');
            const controllingShareHolder = await builder
              .createUTRTableBuilder('gri/2020/102-25/b')
              .getAsString('existence_of_controlling_shareholder');
            const relatedPartyDisclosures = await builder
              .createUTRTableBuilder('gri/2020/102-25/b')
              .getAsString('related_party_disclosure');
            const description = await builder.createUTRTableBuilder('gri/2020/102-25/b').getAsString('description');

            return builder
              .createAIBuilder()
              .ask([
                `As a company, here is our conflict of interest information:`,
                `Cross-board Membership: ${crossBoardMembership || 'Not Reported'}`,
                `Cross-shareholding With Suppliers and Stakeholders: ${crossShareholding || 'Not Reported'}`,
                `Existence of Controlling Shareholder: ${controllingShareHolder || 'Not Reported'}`,
                `Related Party Disclosures: ${relatedPartyDisclosures || 'Not Reported'}`,
                ...(description ? [`Additional Information: ${description}`] : []),
              ])
              .addFurtherExplanation('gri/2020/102-25/b')
              .narrative()
              .and('Explain the significance of each component if data is available.')
              .and('Focus on corporate governance implications.')
              .and('Be concise and factual.')
              .max(150)
              .exec();
          },
        },
      ],
    ],
  },
];
