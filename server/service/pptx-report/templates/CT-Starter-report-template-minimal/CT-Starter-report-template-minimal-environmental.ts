/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { isDefined } from '../../../../util/type';
import { sum } from '../../utils';
import { displayValueWithSuffix } from '../CT-report-template-minimal/utils';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS = {
  18: {
    utrs: [{ utrCode: 'gri/2020/305-1/a' }, { utrCode: 'gri/2020/305-2/a' }, { utrCode: 'gri/2020/305-3/a' }],
    previousSurveyIncluded: false,
  },
  19: {
    utrs: [{ utrCode: 'gri/2020/305-1/a' }, { utrCode: 'gri/2020/305-2/a' }],
  },
  20: {
    utrs: [{ utrCode: 'gri/2020/305-3/a' }],
  },
  21: {
    utrs: [{ utrCode: 'gri/2020/305-7/a' }],
    previousSurveyIncluded: false,
  },
  22: {
    utrs: [{ utrCode: 'gri/2020/302-1/e' }, { utrCode: 'gri/2020/302-3/a' }],
  },
  23: {
    utrs: [{ utrCode: 'gri/2020/303-5/a' }],
  },
  24: {
    utrs: [{ utrCode: 'gri/2020/306-3/a', columnCodes: ['hazardous_waste', 'non_hazardous'] }],
  },
};

export const getPPTXConfigEnvironmental = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Environmental
    slideId: 17,
  },
  {
    // Emissions total 1
    slideId: 18,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[18].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_TITLE',
        {
          text: async () => {
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a', 18).getAsNumber();
            if (isDefined(scope3)) {
              return 'Scope 1 & 2 greenhouse gas emissions';
            }
            return 'Scope 1, 2 & 3 greenhouse gas emissions';
          },
        },
      ],
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const scope1Answer = await builder.createUTRBuilder('gri/2020/305-1/a', 18).getInputSimpleNumericAnswer();
            const scope2Answer = await builder.createUTRBuilder('gri/2020/305-2/a', 18).getInputSimpleNumericAnswer();
            const scope3Answer = await builder.createUTRBuilder('gri/2020/305-3/a', 18).getInputSimpleNumericAnswer();

            let breakdown = '';
            if (isDefined(scope3Answer.value)) {
              breakdown = `Scope 1 has ${displayValueWithSuffix(scope1Answer)} and Scope 2 has ${displayValueWithSuffix(
                scope1Answer
              )}`;
            } else {
              breakdown = `Scope 1 has ${displayValueWithSuffix(scope1Answer)}, Scope 2 has ${displayValueWithSuffix(
                scope2Answer
              )} and Scope 3 has ${displayValueWithSuffix(scope3Answer)}`;
            }

            return builder
              .createAIBuilder()
              .ask([`As a company I have greenhouse gas emissions which is broken down into ${breakdown}.`])
              .bePositive()
              .max(150)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART',
        {
          chartData: async () => {
            const categories = [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a', 18).fallback(0.01).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a', 18).fallback(0.01).getAsNumber()],
              },
            ];
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a', 18).getAsNumber();
            if (scope3 !== null) {
              categories.push({
                label: 'Scope 3',
                values: [scope3],
              });
            }

            return {
              series: [
                {
                  label: String(await builder.getYear()),
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  {
    // Emissions total 2
    slideId: 19,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[19].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a', 19).getAsNumber();
            const locationScope2 = await builder.createUTRBuilder('gri/2020/305-2/a', 19).getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                `As a company, my emissions breakdown is:`,
                `Scope 1 emissions: ${scope1 ? `${scope1}mt2` : 'Not Reported'}`,
                `Scope 2 location-based emissions: ${locationScope2 ? `${locationScope2}mt2` : 'Not Reported'}`,
                '',
              ])
              .narrative()
              .firstPerson()
              .and(
                `Use references to the data supplied, explaining the figures and what the figures mean - ` +
                  `expanding on what location-based mean in this context if you have data for that, and explaining ` +
                  `what market-based is, if you have data for that.`
              )
              .max(120)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_SCOPE_1_2',
        {
          text: async () => {
            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a', 19).periodOffset(-1).getAsNumber();
            const scope2 = await builder.createUTRBuilder('gri/2020/305-2/a', 19).periodOffset(-1).getAsNumber();
            const sum = (scope1 ?? 0) + (scope2 ?? 0);
            builder.setKeyStore('PREV_YEAR_SCOPE_1_2', sum);
            return sum;
          },
        },
      ],
      [
        'YEAR_SCOPE_1_2',
        {
          text: async () => {
            const scope1 = await builder.createUTRBuilder('gri/2020/305-1/a', 19).getAsNumber();
            const scope2 = await builder.createUTRBuilder('gri/2020/305-2/a', 19).getAsNumber();
            const sum = (scope1 ?? 0) + (scope2 ?? 0);
            builder.setKeyStore('YEAR_SCOPE_1_2', sum);
            return sum;
          },
        },
      ],
      [
        'CHANGE_SCOPE_1_2',
        {
          text: async () => {
            const valueA = builder.getKeyStore('PREV_YEAR_SCOPE_1_2');
            const valueB = builder.getKeyStore('YEAR_SCOPE_1_2');
            return builder.getChangeText(Number(valueA), Number(valueB));
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE_CHART',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 19, format: 'full' }),
        },
      ],
      [
        'UNIT_NUMBERSCALE_TABLE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 19 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'DONUT_CHART_1',
        {
          chartData: async () => ({
            series: [
              {
                label: String(await builder.getYear()),
              },
            ],
            categories: [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a', 19).fallback(0).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a', 19).fallback(0).getAsNumber()],
              },
            ],
          }),
        },
      ],
      [
        'DONUT_CHART_2',
        {
          chartData: async () => ({
            series: [
              {
                label: String(await builder.getYear()),
              },
            ],
            categories: [
              {
                label: 'Scope 1',
                values: [await builder.createUTRBuilder('gri/2020/305-1/a', 19).fallback(0).getAsNumber()],
              },
              {
                label: 'Scope 2',
                values: [await builder.createUTRBuilder('gri/2020/305-2/a', 19).fallback(0).getAsNumber()],
              },
            ],
          }),
        },
      ],
    ],
  },
  {
    // Emissions total 3
    slideId: 20,
    skip: async () => {
      const hasCurrentYear = await builder.createUTRBuilder('gri/2020/305-3/a', 20).hasValue();
      if (hasCurrentYear) {
        return false;
      }
      return !(await builder.createUTRBuilder('gri/2020/305-3/a', 20).periodOffset(-1).hasValue());
    },
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[20].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const scope3 = await builder.createUTRBuilder('gri/2020/305-3/a', 20).getAsNumber();

            return builder
              .createAIBuilder()
              .ask([
                scope3
                  ? `As a company my Scope 3 emissions are ${scope3}`
                  : `As a company I have not reported my Scope 3 emissions.`,
              ])
              .addTarget('gri/2020/305-3/a')
              .addFurtherExplanation('gri/2020/305-3/a')
              .narrative()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and(
                'There is no need to explain if you don’t have any corresponding data, just note that there was no data available.'
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_SCOPE_3',
        { text: async () => builder.createUTRBuilder('gri/2020/305-3/a', 20).periodOffset(-1).getAsNumber() },
      ],
      ['YEAR_SCOPE_3', { text: async () => builder.createUTRBuilder('gri/2020/305-3/a', 20).getAsNumber() }],
      [
        'CHANGE_SCOPE_3',
        {
          text: async () =>
            builder.getUTRValueChange('gri/2020/305-3/a', { slideId: 20, reportOffsetFrom: 0, reportOffsetTo: -1 }),
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 20 }),
        },
      ],
    ],
  },
  {
    // New slide: Significant Air Emissions
    slideId: 21,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[21].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const breakdown = await builder.createUTRBuilder('gri/2020/305-7/a', 21).getAsNumber();
            return builder
              .createAIBuilder()
              .ask([`As a company my other significant air emissions can be broken down as ${breakdown}`])
              .and('Explain significant air emissions.')
              .and("Don't say what you can't do.")
              .narrative()
              .bePositive()
              .and('Reference the data supplied, explaining the figures and what the figures mean.')
              .and('Focus on data insights. Include year-on-year comparisons where available.')
              .and(
                'There is no need to explain if you dont have any corresponding data, just note that there was no data available.'
              )
              .max(150)
              .exec();
          },
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 21, format: 'full' }),
        },
      ],
    ],
    chartReplacements: [
      [
        'HORIZONTAL_CHART',
        {
          chartData: async () => {
            const data = await builder.createUTRBuilder('gri/2020/305-7/a', 21).getComplexValueListData();
            const categories = [
              {
                label: 'NOx (Oxides of Nitrogen)',
                values: [data.nox ?? ''],
              },

              {
                label: 'SOx (Sulfur oxides)',
                values: [data.sox ?? ''],
              },
              {
                label: 'Persistent organic pollutants (POP)',
                values: [data.pop ?? ''],
              },
              {
                label: 'Volatile organic compounds (VOC)',
                values: [data.voc ?? ''],
              },
              {
                label: 'Hazardous air pollutants (HAP)',
                values: [data.hap ?? ''],
              },
              {
                label: 'Particulate matter (PM)',
                values: [data.pm ?? ''],
              },
              {
                label: 'Other',
                values: [data.other_standard_air_categories ?? ''],
              },
            ];
            return {
              series: [
                {
                  label: String(await builder.getYear()),
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  {
    // Consumption - Energy
    slideId: 22,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[22].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const energy = await builder.createUTRBuilder('gri/2020/302-1/e', 22).getAsNumber();
            const energyIntensity = await builder.createUTRBuilder('gri/2020/302-3/a', 22).getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                energy && energyIntensity
                  ? `As a company I have a total energy consumption of ${energy} and an energy consumption intensity of ${energyIntensity}.`
                  : `As a company I have not reported my total energy consumption.`,
              ])
              .addTarget('gri/2020/302-1/e')
              .addTarget('gri/2020/302-3/a')
              .addFurtherExplanation('gri/2020/302-1/e')
              .addFurtherExplanation('gri/2020/302-3/a')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_ENERGY',
        {
          text: async () =>
            builder.createUTRBuilder('gri/2020/302-1/e', 22).periodOffset(-1).maxDecimals(2).getAsNumber(),
        },
      ],
      [
        'YEAR_TOTAL_ENERGY',
        { text: async () => builder.createUTRBuilder('gri/2020/302-1/e', 22).maxDecimals(2).getAsNumber() },
      ],
      ['CHANGE_TOTAL_ENERGY', { text: async () => builder.getUTRValueChange('gri/2020/302-1/e', { slideId: 22 }) }],
      [
        'PREV_YEAR_ENERGY_INT',
        {
          text: async () =>
            builder.createUTRBuilder('gri/2020/302-3/a', 22).periodOffset(-1).maxDecimals(2).getAsNumber(),
        },
      ],
      [
        'YEAR_ENERGY_INT',
        { text: async () => builder.createUTRBuilder('gri/2020/302-3/a', 22).maxDecimals(2).getAsNumber() },
      ],
      ['CHANGE_ENERGY_INT', { text: async () => builder.getUTRValueChange('gri/2020/302-3/a', { slideId: 22 }) }],
      [
        'UNIT_NUMBERSCALE_CHART',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 22, format: 'full' }),
        },
      ],
      [
        'UNIT_NUMBERSCALE_TABLE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 22 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const year = await builder.getYear();
            const prevYear = await builder.getYear(-1);
            const prev = await builder.createUTRBuilder('gri/2020/302-1/e', 22).periodOffset(-1).getRawValue();
            const current = await builder.createUTRBuilder('gri/2020/302-1/e', 22).getRawValue();
            return {
              series: [
                {
                  label: 'Total energy consumption',
                },
              ],
              categories: [
                {
                  label: String(prevYear),
                  values: [prev ?? ''],
                },
                {
                  label: String(year),
                  values: [current ?? ''],
                },
              ],
            };
          },
        },
      ],
    ],
  },
  {
    // Consumption - Water
    slideId: 23,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[23].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const water = await builder.createUTRBuilder('gri/2020/303-5/a', 23).getAsNumber();
            return builder
              .createAIBuilder()
              .ask([
                water
                  ? `As a company I have a total water consumption of ${water}.`
                  : `As a company I have not reported my total water consumption.`,
              ])
              .addTarget('gri/2020/303-5/a')
              .addFurtherExplanation('gri/2020/303-5/a')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_TOTAL_WATER',
        { text: async () => builder.createUTRBuilder('gri/2020/303-5/a', 23).periodOffset(-1).getAsNumber() },
      ],
      ['YEAR_TOTAL_WATER', { text: async () => builder.createUTRBuilder('gri/2020/303-5/a', 23).getAsNumber() }],
      ['CHANGE_TOTAL_WATER', { text: async () => builder.getUTRValueChange('gri/2020/303-5/a', { slideId: 23 }) }],
      [
        'UNIT_NUMBERSCALE_CHART',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 23, format: 'full' }),
        },
      ],
      [
        'UNIT_NUMBERSCALE_TABLE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 23 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Total water consumption',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [(await builder.createUTRBuilder('gri/2020/303-5/a', 23).periodOffset(-1).getRawValue()) ?? ''],
              },
              {
                label: String(await builder.getYear()),
                values: [(await builder.createUTRBuilder('gri/2020/303-5/a', 23).getRawValue()) ?? ''],
              },
            ],
          }),
        },
      ],
    ],
  },
  {
    // Consumption - Waste
    slideId: 24,
    appendix: async () => CT_STARTER_ENVIRONMENTAL_SLIDE_UTRS[24].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const hazardous = await builder.createUTRTableBuilder('gri/2020/306-3/a', 24).sum(['hazardous_waste']);
            const nonHazardous = await builder.createUTRTableBuilder('gri/2020/306-3/a', 24).sum(['non_hazardous']);
            const totalWaste = sum(hazardous, nonHazardous);
            builder.setKeyStore('YEAR_WASTE_HAZ', hazardous);
            builder.setKeyStore('YEAR_WASTE_NON_HAZ', nonHazardous);
            builder.setKeyStore('YEAR_TOTAL_WASTE', totalWaste);

            const prevHazardous = await builder
              .createUTRTableBuilder('gri/2020/306-3/a', 24)
              .periodOffset(-1)
              .sum(['hazardous_waste']);
            const prevNonHazardous = await builder
              .createUTRTableBuilder('gri/2020/306-3/a', 24)
              .periodOffset(-1)
              .sum(['non_hazardous']);
            const prevTotalWaste = sum(prevHazardous, prevNonHazardous);
            builder.setKeyStore('PREV_YEAR_WASTE_HAZ', prevHazardous);
            builder.setKeyStore('PREV_YEAR_WASTE_NON_HAZ', prevNonHazardous);
            builder.setKeyStore('PREV_YEAR_TOTAL_WASTE', prevTotalWaste);

            return builder
              .createAIBuilder()
              .ask(
                totalWaste
                  ? [
                      `As a company I have a total waste generation of ${totalWaste} which is broken down into the following categories:`,
                      `Hazardous (${hazardous ?? 0}mt) and Non-hazardous (${nonHazardous ?? 0}mt).`,
                    ]
                  : 'As a company I have not reported my total waste generation.'
              )
              .addTarget('gri/2020/306-3/a', 'hazardous_waste')
              .addTarget('gri/2020/306-3/a', 'non_hazardous')
              .addFurtherExplanation('gri/2020/306-3/a')
              .addFurtherExplanation('gri/2020/306-3/a')
              .narrative()
              .max(150)
              .exec();
          },
        },
      ],
      ['PREV_YEAR_TOTAL_WASTE', { text: async () => builder.getKeyStore('PREV_YEAR_TOTAL_WASTE') ?? '-' }],
      ['YEAR_TOTAL_WASTE', { text: async () => builder.getKeyStore('YEAR_TOTAL_WASTE') ?? '-' }],
      [
        'CHANGE_TOTAL_WASTE',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_TOTAL_WASTE')),
              Number(builder.getKeyStore('YEAR_TOTAL_WASTE'))
            ),
        },
      ],
      ['PREV_YEAR_WASTE_HAZ', { text: async () => builder.getKeyStore('PREV_YEAR_WASTE_HAZ') ?? '-' }],
      ['YEAR_WASTE_HAZ', { text: async () => builder.getKeyStore('YEAR_WASTE_HAZ') ?? '-' }],
      [
        'CHANGE_WASTE_HAZ',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_WASTE_HAZ')),
              Number(builder.getKeyStore('YEAR_WASTE_HAZ'))
            ),
        },
      ],
      ['PREV_YEAR_WASTE_NON_HAZ', { text: async () => builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ') ?? '-' }],
      ['YEAR_WASTE_NON_HAZ', { text: async () => builder.getKeyStore('YEAR_WASTE_NON_HAZ') ?? '-' }],
      [
        'CHANGE_WASTE_NON_HAZ',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ')),
              Number(builder.getKeyStore('YEAR_WASTE_NON_HAZ'))
            ),
        },
      ],
      [
        'UNIT_NUMBERSCALE',
        {
          text: async () => builder.getUnitAndNumberScale({ slideId: 24 }),
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Hazardous waste',
              },
              {
                label: 'Non-Hazardous waste',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [
                  builder.getKeyStore('PREV_YEAR_WASTE_HAZ') ?? '',
                  builder.getKeyStore('PREV_YEAR_WASTE_NON_HAZ') ?? '',
                ],
              },
              {
                label: String(await builder.getYear()),
                values: [builder.getKeyStore('YEAR_WASTE_HAZ') ?? '', builder.getKeyStore('YEAR_WASTE_NON_HAZ') ?? ''],
              },
            ],
          }),
        },
      ],
    ],
  },
];
