/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { type PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { type PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';
import { CHART_POSITIONS, CHART_WITH_TABLE_POSITIONS, TABLE_POSITIONS } from '../builders/constants';
import { PPTXDisplayFormat, SlideSection } from '../builders/types';

export const getPPTXConfigEnvironmentalAI = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Environmental
    slideId: 17,
  },
  ...builder.getSlidesRecommendation(SlideSection.Environmental).map(
    (slide): PPTXTemplateLayoutItem => ({
      slideId: 41,
      appendix: async () => slide.utrs.map(({ utrCode }) => utrCode),
      textReplacements: [
        [
          'HEADING',
          {
            text: async () => slide.category,
          },
        ],
        [
          'COLUMN_1_TITLE',
          {
            text: async () => slide.heading,
          },
        ],
        [
          'COLUMN_1_BODY',
          {
            text: async () => {
              let aiBuilder = builder
                .createAIBuilder()
                .ask([
                  `Based on the provided JSON data structure representing an environmental topic (or category), write a descriptive paragraph.
                  Your paragraph should articulate the topic's primary message and how the data is visually presented (chart, table, or a combination).
                  If a chart is included, specify its type and the main insight it aims to convey.
                  If a table is present, briefly describe the data it organizes.
                  The paragraph should synthesize these elements to explain what an audience would understand or observe from this topic.
                  Here is the JSON data: ${JSON.stringify(slide)}
                  `,
                ])
                .and('Use straightforward business language.')
                .and('Be concise and factual.');

              slide.utrs.forEach(({ utrCode }) => {
                aiBuilder = aiBuilder.addFurtherExplanation(utrCode).addTarget(utrCode);
              });

              return aiBuilder
                .and("Don't say what you can't do.")
                .bePositive()
                .and('Reference the data supplied, explaining the figures and what the figures mean.')
                .and('Focus on data insights. Include year-on-year comparisons where available.')
                .and(
                  "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
                )
                .max(150)
                .exec();
            },
          },
        ],
      ],
      chartCreation: slide.chart
        ? {
            ...slide.chart,
            options: slide.displayFormat === PPTXDisplayFormat.Chart ? CHART_POSITIONS : CHART_WITH_TABLE_POSITIONS,
          }
        : undefined,

      tableReplacements: [
        [
          'TABLE',
          {
            rows: async () => {
              if (!slide.table) {
                return null;
              }
              const { columns, rows } = slide.table;
              return [{ values: columns }, ...rows.map((row) => ({ values: row }))];
            },
            options: slide.displayFormat === PPTXDisplayFormat.Table ? TABLE_POSITIONS : undefined,
          },
        ],
      ],
    })
  ),
];
