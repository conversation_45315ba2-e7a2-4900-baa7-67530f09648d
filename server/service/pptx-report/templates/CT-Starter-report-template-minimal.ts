/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { findChildByCode, getGroup } from '@g17eco/core';
import { getAiService } from '../../../service/ai/service';
import { wwgLogger } from '../../../service/wwgLogger';
import { DateFormat, customDateFormat } from '../../../util/date';
import { PPTXColorScheme, PPTXTemplateContext, PPTXTemplateScheme } from '../types';
import { PPTXTemplateBuilder } from './builders/PPTXTemplateBuilder';
import { getPPTXConfigSDGs } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-sdgs';
import { getPPTXConfigAbout } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-about';
import { getPPTXConfigCover } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-cover';
import { PPTXTemplateConfig } from './PPTXTemplateInterface';
import { PPTXTemplateSurveyCacheManager } from './PPTXTemplateSurveyCacheManager';
import { getPPTXConfigEnvironmental } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-environmental';
import { getPPTXConfigSocial } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-social';
import { getPPTXConfigGovernance } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-governance';
import { getPPTXConfigAppendix } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-appendix';
import { getPPTXConfigEconomic } from './CT-Starter-report-template-minimal/CT-Starter-report-template-minimal-economic';
import { SurveyAnswerGenerator } from './builders/SurveyAnswerGenerator';
import { CT_STARTER_SLIDE_UTRS } from './builders/constants';
import { AISlideRecommendationBuilder } from './builders/AISlideRecommendationBuilder';
import { AIModelType, getAIModelFactory } from '../../ai/AIModelFactory';
import { getPPTXConfigEnvironmentalAI } from './CT-Starter-report-template-minimal/CT-Starter-AI-report-template-minimal-environmental';

const getTemplateFile = ({
  templateScheme,
  colorScheme,
  useAISlideSuggestions = false,
}: {
  templateScheme: string | PPTXTemplateScheme;
  colorScheme: string | PPTXColorScheme;
  useAISlideSuggestions?: boolean;
}): string => {
  switch (templateScheme) {
    case PPTXTemplateScheme.Aspect:
      return `CTSTARTER-report-template-hexagons-${colorScheme}.pptx`
    case PPTXTemplateScheme.Blocks:
      return `CTSTARTER-report-template-blocks-${colorScheme}.pptx`;
    case PPTXTemplateScheme.Default:
    default:
      return useAISlideSuggestions
        ? `CTSTARTER-AI-report-template-minimalist-${colorScheme}.pptx`
        : `CTSTARTER-report-template-minimalist-${colorScheme}.pptx`;
  }
};

export const getCTStarterConfig = async (context: PPTXTemplateContext): Promise<PPTXTemplateConfig> => {
  const {
    templateScheme = PPTXTemplateScheme.Default,
    colorScheme = PPTXColorScheme.Ocean,
    surveyId,
    initiativeId,
    debug,
    useAISlideSuggestions,
  } = context;
  const repositoryManager = new PPTXTemplateSurveyCacheManager(surveyId, initiativeId);
  const surveyAnswerGenerator = new SurveyAnswerGenerator(CT_STARTER_SLIDE_UTRS, repositoryManager);
  const slidesAnswerMap = await surveyAnswerGenerator.loadSlidesData();
  const slidesRecommendationMap = useAISlideSuggestions
    ? await new AISlideRecommendationBuilder(
        getAIModelFactory().getAiModel(AIModelType.ChatGPT),
        repositoryManager,
        surveyAnswerGenerator
      ).getSlideRecommendations()
    : undefined;

  const builder = new PPTXTemplateBuilder(
    wwgLogger,
    initiativeId,
    repositoryManager,
    getAiService(),
    Boolean(debug),
    slidesAnswerMap,
    slidesRecommendationMap,
  );

  return {
    templateFilename: getTemplateFile({ templateScheme, colorScheme, useAISlideSuggestions }),
    textReplacements: [
      ['YEAR', { text: async () => builder.getYear() }],
      ['PREV_YEAR', { text: async () => builder.getYear(-1) }],
      ['COMPANY_NAME', { text: async () => await builder.getCompanyName() }],
      ['REPORT_NAME', { text: async () => `Sustainability Report ${await builder.getYear()}` }],
      ['LONG_DATE', { text: async () => customDateFormat(new Date(), DateFormat.MonthDayYear) }],
    ],
    masterSlides: [
      {
        slideId: 1,
        // Only global replacements
      },
      {
        slideId: 2,
        // Only global replacements
      },
    ],
    appendixCallback: async ({ utrCodes, slideNum }) => {
      const simpleTypes = ['sasb', 'gri2021', 'tcfd_standard', 'un_gc', 'unctad', 'cdp'];
      for (const type of simpleTypes) {
        const appendixRow: (string | number)[] = [];
        switch (type) {
          case 'gri2021': {
            const tags: Set<string> = new Set();
            for (const utrCode of utrCodes) {
              const typeTags = await builder.createUTRBuilder(utrCode).getTypeTags(type);
              if (typeTags) {
                typeTags.forEach((code) => tags.add(code));
              }
            }

            if (tags.size === 0) {
              continue;
            }

            const griGroup = getGroup('standards', 'gri2021');
            const griTopics = new Set<string>();
            if (griGroup) {
              for (const code of tags) {
                const topic = findChildByCode(griGroup, code);
                if (topic) {
                  griTopics.add(topic.name);
                }
              }
            }
            appendixRow.push(Array.from(griTopics).sort().join(', '));
            appendixRow.push(Array.from(tags).sort().join(', '));
            break;
          }
          case 'tcfd_standard':
          case 'sasb':
          case 'un_gc':
          case 'unctad':
          case 'cdp':
          default: {
            const codes: Set<string> = new Set();
            for (const utrCode of utrCodes) {
              const typeCode = await builder.createUTRBuilder(utrCode).getTypeCode(type);
              if (typeCode) {
                codes.add(typeCode);
              }
            }

            if (codes.size === 0) {
              continue;
            }

            appendixRow.push(Array.from(codes).sort().join(', '));
            break;
          }
        }
        appendixRow.push(slideNum);
        builder.addAppendixEntry(type, appendixRow);
      }

      // SDGs
      const sdgGoals: Set<string> = new Set();
      const sdgTargets: Set<string> = new Set();
      for (const utrCode of utrCodes) {
        const sdgs = await builder.createUTRBuilder(utrCode).getTags('sdg');
        if (sdgs) {
          sdgs.forEach((sdg) => {
            if (sdg.includes('.')) {
              sdgGoals.add(`Goal ${sdg.split('.')[0]}`);
              sdgTargets.add(`Target ${sdg}`);
            } else {
              sdgGoals.add(`Goal ${sdg}`);
            }
          });
        }
      }

      if (sdgGoals.size > 0 && sdgTargets.size > 0) {
        builder.addAppendixEntry('sdg', [
          Array.from(sdgGoals).sort().join(', '),
          Array.from(sdgTargets).sort().join(', '),
          slideNum,
        ]);
      }
    },
    slides: [
      ...getPPTXConfigCover(builder),
      ...getPPTXConfigAbout(builder),
      ...getPPTXConfigSDGs(builder),
      ...getPPTXConfigEconomic(builder),
      ...(useAISlideSuggestions ? getPPTXConfigEnvironmentalAI(builder) : getPPTXConfigEnvironmental(builder)),
      ...getPPTXConfigSocial(builder),
      ...getPPTXConfigGovernance(builder),
      ...getPPTXConfigAppendix(builder),
    ],
  };
};
