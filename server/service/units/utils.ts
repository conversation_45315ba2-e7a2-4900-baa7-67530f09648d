import { ALLOWED_SIZES, NumberScale, SINGLE_NUMBER_SCALE_VALUES } from './unitTypes';

export const checkMatchedUnit = (uniqueUnits: Set<string | undefined>) => {
  return ALLOWED_SIZES.includes(uniqueUnits.size) || Array.from(uniqueUnits).every((s) => !s);
};

export const checkMatchedNumberScale = (uniqueNumberScales: Set<string | undefined>) => {
  return (
    ALLOWED_SIZES.includes(uniqueNumberScales.size) ||
    Array.from(uniqueNumberScales).every((s) => SINGLE_NUMBER_SCALE_VALUES.includes(s))
  );
};

export const isCorrectNumberScaleCode = (numberScaleCode: unknown): numberScaleCode is string => {
  if (!numberScaleCode || typeof numberScaleCode !== 'string') {
    return false;
  }
  return Object.values(NumberScale).some((item) => item === numberScaleCode);
};
