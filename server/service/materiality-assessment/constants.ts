import { MaterialityAssessmentScope } from './types';
import { MaterialitySizeMultipliers } from './assessments/MaterialityLookupCalculator';
import { AssessmentType } from '../../types/materiality-assessment';

export const topicLengthMap: { [key in MaterialityAssessmentScope]: number } = {
  [MaterialityAssessmentScope.Startup]: 15,
  [MaterialityAssessmentScope.Solopreneur]: 15,
  [MaterialityAssessmentScope.Micro]: 15,
  [MaterialityAssessmentScope.SME]: 20,
  [MaterialityAssessmentScope.MidCap]: 20,
  [MaterialityAssessmentScope.MNC]: 25,
  [MaterialityAssessmentScope.Large]: 25,
};

export const FINANCIAL_ASSESSMENT_WEIGHTS: MaterialitySizeMultipliers = {
  getSectorScoresTop10: 0.14,
  getSectorScoresRemainder: 0.06,
  getEngagementScores: 0.2,
  getOrgStructureScores: 0.1,
  getSizeScores: 0.3,
  getOperationTimeScores: 0.04,
  getLocationScoresHq: 0.0368,
  getLocationScoresOperations: 0.032,
  getLocationScoresCustomer: 0.024,
  getLocationScoresSuppliers: 0.0232,
  getLocationScoresStakeholders: 0.02,
  getLocationScoresFinance: 0.024,
};

export const IMPACT_ASSESSMENT_WEIGHTS: MaterialitySizeMultipliers = {
  getSectorScoresTop10: 0.14,
  getSectorScoresRemainder: 0.06,
  getEngagementScores: 0.15,
  getOrgStructureScores: 0.05,
  getSizeScores: 0.1,
  getOperationTimeScores: 0.05,
  getLocationScoresHq: 0.1,
  getLocationScoresOperations: 0.15,
  getLocationScoresCustomer: 0.05,
  getLocationScoresSuppliers: 0.1,
  getLocationScoresStakeholders: 0.03,
  getLocationScoresFinance: 0.02,
};

export const assessmentTypeLabelMap: Record<string, string> = {
  [AssessmentType.FinancialMateriality]: 'Financial Materiality',
  [AssessmentType.DoubleMateriality]: 'Double Materiality',
};

export const metricGroupAssessmentTypeLabelMap: Record<AssessmentType, string> = {
  [AssessmentType.FinancialMateriality]: 'Financial materiality assessment',
  [AssessmentType.DoubleMateriality]: 'Double materiality assessment',
};
