import axios from "axios";
import config from "../../config";

export class WebsocketService {
  static async getChecks() {

    const websocketUrl = config.websocket.url;
    if (!websocketUrl) {
      return {
        name: 'Websocket',
        status: 'Not enabled',
      }
    }

    const checks = await axios.get(`http://${websocketUrl}/checks`, {
      timeout: 2000,
    });
    return checks.data as object;
  }
}
