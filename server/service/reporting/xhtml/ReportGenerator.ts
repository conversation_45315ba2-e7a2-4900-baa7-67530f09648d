import { type LoggerInterface, wwgLogger } from '../../wwgLogger';
import { getXhtmlGenerator } from '../xhtml/XHTMLGenerator';
import type { ReportSection, XbrlTable, XbrlTableData } from '../xhtml/types';
import { e4DebugExtensions } from '../xhtml/e4-debug-extensions';
import { getCsvName } from '../../assurance/csvContext';
import { XbrlTracker } from '../XbrlTracker';
import { customDateFormat, DateFormat } from '../../../util/date';
import type { GeneratorParameters, ReportDefinition } from '../types';
import type { ReportDocumentType } from '../../../models/reportDocument';

interface GenerateParams extends Pick<GeneratorParameters, 'initiative' | 'mapping' | 'survey'> {
  reportType: ReportDocumentType;
  sections: ReportSection[];
  preview?: boolean;
}

interface SectionData extends Pick<GeneratorParameters, 'initiative' | 'mapping' | 'utrvData'> {
  tracker: XbrlTracker;
}

interface GenerateTableDataParams extends Pick<SectionData, 'mapping' | 'utrvData' | 'tracker'> {
  utrCode: string;
  facts: string[];
  getData: (factName: string, fallback?: string) => string | number;
  definitions: ReportDefinition;
}

export class ReportGenerator {
  constructor(private logger: LoggerInterface, private xhtmlGenerator: ReturnType<typeof getXhtmlGenerator>) {}

  public async generate(params: GenerateParams) {
    const { initiative, survey, preview, sections, reportType } = params;
    this.logger.info(`Generating ${reportType} for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();

    const xmlString = await this.xhtmlGenerator.render({
      initiative,
      sections,
      effectiveDate: new Date(survey.effectiveDate),
      customScripts: preview
        ? e4DebugExtensions({
            facts: tracker.getDebugFacts(),
          })
        : '',
    });

    const filename = getCsvName({
      _id: survey._id,
      initiative,
      survey,
      type: reportType,
    });

    return {
      filename: `${filename}${preview ? '-preview' : ''}.xhtml`,
      xmlString,
      tracker,
    };
  }

  private generateTableData = (params: GenerateTableDataParams): XbrlTable => {
    const { utrvData, getData, utrCode, facts, tracker, definitions } = params;

    const utrvs = utrvData.filter((utrv) => utrv.universalTracker.code === utrCode);
    const dates = utrvs.map((utrv) => customDateFormat(utrv.effectiveDate, DateFormat.Year));

    const tableColumns = [
      {
        id: 'waste-water-resource-type',
        name: '',
        accessor: 'columnName',
      },
      ...dates.map((date) => ({
        id: date,
        name: date,
        accessor: date,
      })),
    ];

    if (utrvs.length === 0) {
      return {
        type: 'table',
        columns: [],
        data: [],
      };
    }

    const data = facts.map((fact) => {
      return {
        columnName: definitions[fact]?.label ?? fact,
        ...dates.reduce((acc, date) => {
          // @TODO this will only be one date for now
          const value = getData(fact);
          acc[date] = tracker.addFactNonFraction(
            {
              unitRef: tracker.addUnitRef('m3', 'u-101'),
              name: fact,
              children: [{ type: 'inline', content: value }],
            },
            value
          );
          return acc;
        }, {} as XbrlTableData),
      } as XbrlTableData;
    });

    return {
      type: 'table',
      columns: tableColumns,
      data,
    };
  };
}

let instance: ReportGenerator;
export const getReportGenerator = () => {
  if (!instance) {
    instance = new ReportGenerator(wwgLogger, getXhtmlGenerator());
  }
  return instance;
};
