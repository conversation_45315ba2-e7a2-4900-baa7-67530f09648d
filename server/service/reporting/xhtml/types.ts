/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { InitiativePlain } from '../../../models/initiative';
import type { UtrvData, XBRLMapping } from '../types';
import type { XbrlTracker } from '../XbrlTracker';

export interface Header {
  title: string;
  id: string;
}

export interface SectionOption {
  header: Header;
  children: ChildrenNode[];
  footer?: string;
}

export interface SectionResult {
  header: SectionOption['header'];
  pages: string[];
}

type NonNumericFormat = 'ixt4:fixed-true';

export type FactId = `fact-${number}`;
export type ContextId = `c-${number}`;
export type UnitId = `u-${number}`;

export interface NonNumericFact {
  tag?: 'ix:nonNumeric';
  name: string;
  id: FactId;
  contextRef: string;
  escape?: 'true' | 'false' | boolean;
  format?: NonNumericFormat;
  lang?: string;
  children: ChildrenNode[];
}

export interface NonFractionFact {
  tag: 'ix:nonFraction';
  name: string;
  format?: string;
  id: FactId;
  contextRef: ContextId;
  children: ChildrenNode[];
  unitRef?: UnitId;
  decimals?: number;
  scale?: number | string;
}

export type Fact = NonNumericFact | NonFractionFact;

type TextTag = 'span' | 'p';

export type FactTag = { type: 'fact' } & Fact;
type PlainText = { type: 'text'; tag?: TextTag; content: string | number };
type InlineText = { type: 'inline'; content: string | number };
type Group = { type: 'group'; tag?: TextTag; children: ChildrenNode[] };
type HTMLNode = { type: 'html'; content: string };

interface ColumnSetup<T = any> {
  id: string;
  name: string;
  accessor: string | ((utr: T) => string | number | undefined | FactTag);
}

export type XbrlTableData = Record<string, FactTag | string | undefined>;
export type XbrlTable<T = XbrlTableData> = {
  type: 'table';
  columns: ColumnSetup<T>[];
  data: T[];
};

export type ChildrenNode = PlainText | InlineText | FactTag | XbrlTable | Group | HTMLNode;

export interface ReportSection {
  header: Pick<SectionOption['header'], 'title'>;
  children: ChildrenNode[];
}

export interface SectionData {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  mapping: XBRLMapping;
  utrCodeToUtrvMap: Map<string, UtrvData>;
  tracker: XbrlTracker;
}

type NonUnitKey = `${string}:${string}`;

export type FactsMap<T extends NonUnitKey = NonUnitKey> = Record<FactId, DebugFact<T> | undefined>;

export interface DebugFact<T extends NonUnitKey = NonUnitKey> {
  /**
   * Value of the fact
   *
   *
   * Inline Html with encoded tags
   * "\u003Cdiv class=\u0022defaultParagraph Normal\u0022
   * xmlns=\u0022http://www.w3.org/1999/xhtml\u0022\u003E\u003Cspan
   * id=\u0022ID_528844629\u0022\u003ENulla ac augue at mauris gravida ultrices.
   * \u003Cspan id=\u0022ID_1440257468\u0022\u003EPell ..."
   *
   * ----- OR -----
   * "v": "true"
   */
  v: string;
  a: FactAnnotation<T>;
  /**
   * Assume this is Format
   * @example  "f": "ixt:fixed-true"
   */
  f?: string;

  /** Decimal? **/
  d?: number;
}

/**
 * @example
 *  "a": {
 *    "c": "esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory",
 *    "e": "e:efrag",
 *    "esrs:IdentifierOfImpactRiskAndOpportunityTypedAxis": "1",
 *    "p": "2024-01-01/2025-01-01"
 *  }
 */

type FactAnnotation<T extends NonUnitKey = NonUnitKey> = Record<T, string> & {
  /** Reference to concept? Concept Name */
  c: string;
  /**
   * Always seems to be organization entity e:efrag, but based on viewer code it's identifier
   *     identifier() {
   *         return this.report.qname(this.f.a.e);
   *     }
   *     based on type it can be one of the schemas
   *
   *  const schemes = {
   *   "http://standards.iso.org/iso/17442": { "name": "LEI", "url": "https://search.gleif.org/#/record/%s" },
   *   "http://www.sec.gov/CIK": { "name": "CIK", "url": "https://www.sec.gov/cgi-bin/browse-edgar?CIK=%s"},
   *   "http://www.companieshouse.gov.uk/": { "name": "UK CRN", "url": "https://beta.companieshouse.gov.uk/company/%08d"},
   *   "https://www.minfin.gov.ua": { "name": "EDRPOU" },
   *  };
   */
  e: string;
  /** Period - Date range? */
  p: string;
  /** Unit?  "u": "iso4217:EUR", */
  u?: string;
}