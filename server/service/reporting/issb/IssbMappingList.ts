/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { TagMappingItem } from '../types';

// Example from IFRS S1 Risk Management and IFRS S1 Metrics and Targets
export const getDefaultIFRSMappingList = () => {
  const list: TagMappingItem[] = [
    {
      factName: 'ifrs-sds:OtherDisclosuresAboutRiskManagementExplanatory',
      utrCode: 'issb/2024/s1/rskmng-3',
    },
    {
      factName:
        'ifrs-sds:DisclosureOfWhetherAndHowEntityUsesScenarioAnalysisToInformItsIdentificationOfRisksExplanatory',
      utrCode: 'issb/2024/s1/rskmng-1',
      valueListCode: 'rskmng-1-2',
    },
    {
      factName:
        'ifrs-sds:DisclosureOfWhetherAndHowEntityChangedProcessesItUsesToIdentifyAssessPrioritiseAndMonitorRisksForRiskManagementPurposesComparedWithPriorReportingPeriodExplanatory',
      utrCode: 'issb/2024/s1/rskmng-1',
      valueListCode: 'rskmng-1-6',
    },
    {
      factName: 'ifrs-sds:DisclosureOfProcessesEntityUsesToIdentifyAssessPrioritiseAndMonitorOpportunitiesExplanatory',
      utrCode: 'issb/2024/s1/rskmng-2',
    },
    {
      factName: 'ifrs-sds:DisclosureOfSourcesFromWhichMetricWasDrawnExplanatory',
      utrCode: 'issb/2024/s1/met-trgt-2',
    },
    {
      factName: 'ifrs-sds:DisclosureOfMetricUsedToSetTargetAndToMonitorProgressExplanatory',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-1',
    },
    {
      factName: 'ifrs-sds:SpecificQuantitativeOrQualitativeTargetEntityHasSetOrIsRequiredToMeet',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-2',
    },
    {
      factName: 'ifrs-sds:PeriodOverWhichTargetApplies',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-3',
    },
    {
      factName: 'ifrs-sds:BasePeriodFromWhichProgressForTargetIsMeasured',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-4',
    },
    {
      factName: 'ifrs-sds:DisclosureOfMilestonesAndInterimTargetsForMetricExplanatory',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-5',
    },
    {
      factName:
        'ifrs-sds:DisclosureOfPerformanceAgainstTargetAndAnalysisOfTrendsOrChangesInEntitysPerformanceExplanatory',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-6',
    },
    {
      factName: 'ifrs-sds:DisclosureOfRevisionsToTargetExplanatory',
      utrCode: 'issb/2024/s1/met-trgt-3',
      valueListCode: 'met-trgt-3-7',
    },
  ];
  return list;
};
