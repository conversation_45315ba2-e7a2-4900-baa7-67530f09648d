import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { createListNode } from '../../lexical/utils';

export function buildStrategy(): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 3. Strategy'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This section outlines the company\'s strategic approach to addressing sustainability-related risks and opportunities.'
    )
  );

  const sectionNodes = [
    {
      section: '3.1. Sustainability-Related Risks and Opportunities',
      description:
        'A summary of the most significant risks and opportunities identified through the materiality assessment.',
      keyDisclosures: [
        'A clear description of each material risk and opportunity (e.g., climate-related wildfires, diversity, supplier payment practices).',
        'The time horizon over which each risk or opportunity could reasonably be expected to have an effect (short, medium, long term).',
      ],
    },
    {
      section: '3.2. Impacts on Business Model and Value Chain',
      description:
        'An analysis of how sustainability-related issues affect the company\'s business model and value chain.',
      keyDisclosures: [
        'Description of the key business activities and their geographical locations.',
        'An overview of the upstream and downstream value chain, including key suppliers, customers, and other relationships.',
        'Explanation of how risks and opportunities are concentrated in specific parts of the value chain.',
      ],
    },
    {
      section: '3.3. Climate Resilience and Scenario Analysis',
      description: 'The company\'s resilience to climate-related risks, assessed through scenario analysis.',
      keyDisclosures: [
        'Description of the climate-related scenarios used.',
        'The key assumptions, inputs, and time horizons for the analysis.',
        'A qualitative and quantitative assessment of the potential impacts on the business model and strategy under each scenario.',
        'Identification of significant areas of uncertainty in the analysis.',
      ],
    },
    {
      section: '3.4. Transition Plan',
      description: 'The company\'s plan to transition to a lower-carbon economy.',
      keyDisclosures: [
        'Mitigation and adaptation actions the company is taking to address climate-related risks (e.g., investment in R&D, supplier diversification).',
        'How the transition plan is funded and integrated into the company\'s overall strategy.',
      ],
    },
    {
      section: '3.5. Financial Effects of Risks and Opportunities',
      description: 'The current and anticipated financial effects of material sustainability-related risks and opportunities.',
      keyDisclosures: [
        'Quantitative information: Disclosed as a single amount or a range, detailing the impact on financial position, performance, and cash flows.',
        'Qualitative information: Provided when quantitative data is not available, explaining why and describing the expected qualitative impacts.',
        'Disclosure of the underlying assumptions and methodologies used in the calculations.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    return [subsectionHeading, subsectionDescription, keyDisclosuresList];
  });

  return [sectionHeading, sectionDescription, ...sectionNodes];
}
