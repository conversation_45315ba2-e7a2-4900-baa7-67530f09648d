import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { createListNode } from '../../lexical/utils';

export function buildGovernance(): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 2. Governance'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This section details the governance processes, controls, and procedures an entity uses to monitor and manage sustainability-related risks and opportunities.'
    )
  );

  const sectionNodes = [
    {
      section: '2.1. Board Oversight',
      description: 'The role of the board of directors in overseeing sustainability-related issues.',
      keyDisclosures: [
        'Identification of the board committee(s) responsible for oversight.',
        'Description of the board\'s responsibilities, as defined in its terms of reference.',
        'Frequency of board discussions on sustainability matters.',
        'How the board integrates sustainability into the overall corporate strategy.',
      ],
    },
    {
      section: '2.2. Management\'s Role',
      description: 'Clear identification of the reporting entity and the consolidation boundary.',
      keyDisclosures: [
        'Name of the reporting entity.',
        'Description of the entities included in the sustainability report (e.g., subsidiaries, joint ventures).',
        'Explanation of how the reporting boundary aligns with the financial statements.',
      ],
    },
    {
      section: '2.3. Skills and Competencies',
      description:
        'Disclosure of the skills and competencies required to oversee sustainability-related risks and opportunities.',
      keyDisclosures: [
        'How the board ensures it has the appropriate skills (e.g., through training, use of external advisors).',
        'Description of any third-party experts engaged to support the board.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    return [subsectionHeading, subsectionDescription, keyDisclosuresList];
  });

  return [sectionHeading, sectionDescription, ...sectionNodes];
}
