import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { createListNode } from '../../lexical/utils';

export function buildRequirementsAndFoundation(): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 1. General Requirements and Report Foundation'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This initial section establishes the context for the report, addressing the overarching principles of IFRS S1.'
    )
  );

  const sectionNodes = [
    {
      section: '1.1 Statement of Compliance',
      description: 'An explicit and unreserved statement of compliance with IFRS Sustainability Disclosure Standards.',
      keyDisclosures: [
        'Declaration of conformity.',
        'Reporting period covered.',
        'Reference to the corresponding financial statements.',
      ],
    },
    {
      section: '1.2. Reporting Entity',
      description: 'Clear identification of the reporting entity and the consolidation boundary.',
      keyDisclosures: [
        'Name of the reporting entity.',
        'Description of the entities included in the sustainability report (e.g., subsidiaries, joint ventures).',
        'Explanation of how the reporting boundary aligns with the financial statements.',
      ],
    },
    {
      section: '1.3. Basis for Preparation',
      description: 'An overview of the methodologies and frameworks applied.',
      keyDisclosures: [
        'Disclosure of any transitional reliefs used (e.g., relief from providing comparative data in the first year).',
        'Identification of the sources of guidance used to identify sustainability-related risks and opportunities (e.g., SASB Standards, industry practice).',
        'Description of the currency used for quantitative disclosures.',
      ],
    },
    {
      section: '1.4. Materiality Assessment',
      description:
        'A detailed explanation of the process used to identify material sustainability-related risks and opportunities.',
      keyDisclosures: [
        'Description of the two-step process: identifying potential risks/opportunities and then assessing their materiality.',
        'Explanation of how the company defines "material" in the context of its prospects.',
        'Involvement of key governance bodies (e.g., sustainability steering committee, board) in the validation of material topics.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    return [subsectionHeading, subsectionDescription, keyDisclosuresList];
  });

  return [sectionHeading, sectionDescription, ...sectionNodes];
}
