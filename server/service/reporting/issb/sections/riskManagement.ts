import { $createHeadingNode } from '@lexical/rich-text';
import { $createLineBreakNode, $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { buildXBRLNodes, createListNode } from '../../lexical/utils';
import type { SectionData } from '../../xhtml/types';
import { $createIxbrlNode } from '../../lexical/nodes/IXBRLNode';
import { getFactLabel, getStringData } from '../../utils';
import type { TagMappingItem } from '../../types';

/**
 * @todo: Filtering the tags from the mapping list to only include the ones relevant to this section
 */
export function buildRiskManagement(sectionData: SectionData): LexicalNode[] {
  const { mapping, utrCodeToUtrvMap, tracker } = sectionData;

  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 4. Risk Management'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This section describes the processes the company uses to identify, assess, prioritize, and monitor sustainability-related risks and opportunities.'
    )
  );

  const ixbrlNode = $createIxbrlNode({
    tag: 'ix:nonNumeric',
    name: 'ifrs-sds:OtherDisclosuresAboutRiskManagementExplanatory',
    contextRef: tracker.getContextId(),
    factId: tracker.getFactId(),
  });

  ixbrlNode.append(
    $createTextNode(
      getStringData({
        factName: 'ifrs-sds:OtherDisclosuresAboutRiskManagementExplanatory',
        utrCodeToUtrvMap,
        mapping,
      })
    )
  );

  const sectionNodes = [
    {
      section: '4.1. Identification and Assessment Process',
      description: 'The process used to identify sustainability-related risks and opportunities.',
      keyDisclosures: [
        'The inputs to the process (e.g., data sources, scope of operations).',
        'How the nature, likelihood, and magnitude of risks are assessed.',
        'How climate-related scenario analysis informs the identification of risks.',
      ],
      relatedTags: [
        'ifrs-sds:DisclosureOfWhetherAndHowEntityUsesScenarioAnalysisToInformItsIdentificationOfRisksExplanatory',
      ],
    },
    {
      section: '4.2. Management and Monitoring Process',
      description: 'The processes for managing and monitoring identified risks.',
      keyDisclosures: [
        'How risks are prioritized relative to other types of risk.',
        'How risks are monitored over time.',
        'Description of any changes to the risk management process from the previous reporting period.',
      ],
      relatedTags: [
        'ifrs-sds:DisclosureOfProcessesEntityUsesToIdentifyAssessPrioritiseAndMonitorOpportunitiesExplanatory',
        'ifrs-sds:DisclosureOfWhetherAndHowEntityChangedProcessesItUsesToIdentifyAssessPrioritiseAndMonitorRisksForRiskManagementPurposesComparedWithPriorReportingPeriodExplanatory',
      ],
    },
    {
      section: '4.3. Integration with Overall Risk Management',
      description:
        'How the risk management process for sustainability-related issues is integrated with the company\'s overall enterprise risk management framework.',
      keyDisclosures: [
        'Explanation of how sustainability risks are incorporated into the overall risk register.',
        'The role of the board risk committee in overseeing sustainability-related risks.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures, relatedTags }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    const relatedIxbrlNodes = relatedTags
      ? buildXBRLNodes({
          items: Object.values(mapping).filter(
            (item): item is TagMappingItem => !!item && relatedTags.includes(item.factName)
          ),
          mapping,
          utrCodeToUtrvMap,
          tracker,
        }).map((node) => {
          const paragraph = $createParagraphNode();
          paragraph.append(
            $createTextNode(`${getFactLabel(node.factName)}`),
            $createLineBreakNode(),
            $createTextNode('Value:  '),
            node.ixbrlNode
          );
          return paragraph;
        })
      : [];
    return [subsectionHeading, subsectionDescription, keyDisclosuresList, ...relatedIxbrlNodes];
  });

  return [sectionHeading, sectionDescription, ixbrlNode, ...sectionNodes];
}
