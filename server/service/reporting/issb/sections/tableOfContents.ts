import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { $createListItemNode, $createListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';

export function buildTableOfContents(): LexicalNode[] {
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode('📑 Table of Contents'));

  const list = $createListNode('number');

  const items = [
    'General Requirements and Report Foundation',
    'Governance',
    'Strategy',
    'Risk Management',
    'Metrics and Targets',
  ];

  items.forEach((itemText) => {
    const listItem = $createListItemNode();
    const paragraph = $createParagraphNode();
    paragraph.append($createTextNode(itemText));
    listItem.append(paragraph);
    list.append(listItem);
  });

  return [heading, list];
}