export interface IFRSDefinitionRow {
  'Concept name': string; // Technical name
  'Preferred label': string;
  'Standard label': string; // Label en
  Type: string; // Type name short
  References: string;
  'Documentation label': string;
  'Type name short': string;
  'Guidance label': string;
  'Sub-metric code': string;
  'Measurement guidance label': string;
}

export interface IFRSReferencesDefinitionRow {
  'Concept prefix/name': string;
  'ref:Note': string;
  'ref:Name': string;
  'ref:Number': string;
  'ref:Paragraph': string;
  'ref:Subparagraph': string;
  'ref:Clause': string;
  'ref:Section': string;
  'ref:Subsection': string;
  'Reference type': string;
}
