/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import ContextError from '../../error/ContextError';
import { ReportDocumentType } from '../../models/reportDocument';
import { getCsrdLexicalStateGenerator } from './csrd/CsrdLexicalStateGenerator';
import { CSRDDefinitions } from './csrd/CsrdDefinitions';
import type { ReportDefinition } from './types';
import { ISSBDefinitions } from './issb/IssbDefinitions';
import { getIssbLexicalStateGenerator } from './issb/IssbLexicalStateGenerator';

export class ReportingFactory {
  public getLexicalStateGenerator(reportType: ReportDocumentType) {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return getCsrdLexicalStateGenerator();
      case ReportDocumentType.ISSB:
        return getIssbLexicalStateGenerator();
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }

  public getReportDefinition(reportType: ReportDocumentType): ReportDefinition {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return CSRDDefinitions;
      case ReportDocumentType.ISSB:
        return ISSBDefinitions;
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }
}

let instance: ReportingFactory;
export const getReportingFactory = () => {
  if (!instance) {
    instance = new ReportingFactory();
  }
  return instance;
};
