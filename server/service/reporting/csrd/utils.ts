import { $createLineBreakNode, $createParagraphNode, $createTextNode } from 'lexical';
import { type IXBRLNode } from '../lexical/nodes/IXBRLNode';
import type { SectionData } from '../xhtml/types';
import type { TagMappingItem, XBRLMapping } from '../types';
import { getFactLabel, getTagDefinition } from '../utils';
import { buildXBRLNodes } from '../lexical/utils';

export const MAJOR_SECTION = {
  BP: {
    code: 'BP',
    description: 'Basis for Preparation',
  },
  SBM: {
    code: 'SBM',
    description: 'Strategy, Business Model, and Materiality',
  },
  E1: {
    code: 'E1',
    description: 'Climate Change',
  },
  E2: {
    code: 'E2',
    description: 'Pollution',
  },
  E3: {
    code: 'E3',
    description: 'Water and Marine Resources',
  },
  E4: {
    code: 'E4',
    description: 'Biodiversity and Ecosystems',
  },
  E5: {
    code: 'E5',
    description: 'Resource Use and Circular Economy',
  },
  S1: {
    code: 'S1',
    description: 'Own Workforce',
  },
  S2: {
    code: 'S2',
    description: 'Workers in the Value Chain',
  },
  S3: {
    code: 'S3',
    description: 'Affected Communities',
  },
  S4: {
    code: 'S4',
    description: 'Consumers and End-users',
  },
  G1: {
    code: 'G1',
    description: 'Business Conduct',
  },
};

export const getFactSectionAndDataPoint = (tag: string) => {
  const def = getTagDefinition(tag);
  if (!def?.references) {
    return;
  }

  return def.references.reduce(
    (acc, ref) => {
      const sectionEntry = ref.find(([key]) => key === 'Section');
      const dataPointEntry = ref.find(([key]) => key === 'DatapointId');
      if (sectionEntry) {
        acc.section = sectionEntry[1];
      }
      if (dataPointEntry) {
        acc.dataPointId = dataPointEntry[1];
      }
      return acc;
    },
    { section: undefined as string | undefined, dataPointId: undefined as string | undefined }
  );
};

export const getMajorSection = (sectionCode: string | undefined): string | undefined => {
  if (!sectionCode) {
    return;
  }

  const match = sectionCode.match(/^(BP|SBM|E1|E2|E3|E4|E5|S1|S2|S3|S4|G1)(-|$)/);
  return match ? match[1] : undefined;
};

/**
 * Returns an array of MappingItem from the mapping whose section matches the given majorSection,
 * sorted by section and dataPointId order.
 */
export const getOrderedMappingItems = ({
  mapping,
  majorSection,
}: {
  mapping: XBRLMapping;
  majorSection: string;
}) => {
  // Collect items with their section/dataPointId
  const itemsWithTags = Object.values(mapping)
    .filter((item) => !!item)
    .map((item) => {
      const tag = getFactSectionAndDataPoint(item.factName);
      return { item, tag };
    })
    .filter(({ tag }) => {
      const major = getMajorSection(tag?.section);
      return major === majorSection;
    });

  // Sort using the same logic as sortTagsBySectionAndDataPoint
  itemsWithTags.sort((a, b) => {
    const aMajor = getMajorSection(a.tag?.section);
    const bMajor = getMajorSection(b.tag?.section);

    if (aMajor === undefined && bMajor !== undefined) return 1;
    if (aMajor !== undefined && bMajor === undefined) return -1;
    if (aMajor !== undefined && bMajor !== undefined) {
      if (aMajor < bMajor) return -1;
      if (aMajor > bMajor) return 1;
    }

    if (a.tag?.dataPointId === undefined && b.tag?.dataPointId !== undefined) return 1;
    if (a.tag?.dataPointId !== undefined && b.tag?.dataPointId === undefined) return -1;
    if (a.tag?.dataPointId !== undefined && b.tag?.dataPointId !== undefined) {
      if (a.tag.dataPointId < b.tag.dataPointId) return -1;
      if (a.tag.dataPointId > b.tag.dataPointId) return 1;
    }

    return 0;
  });

  return itemsWithTags.map(({ item }) => item);
};

type BuildIxbrlNodesFromMappingParams = Pick<SectionData, 'mapping' | 'utrCodeToUtrvMap' | 'tracker'> & {
  majorSection: string;
};

/**
 * Build an array of ixbrlNodes for a given majorSection from mapping, utrvData, and tracker.
 */
export const buildIxbrlNodesFromMapping = ({
  mapping,
  utrCodeToUtrvMap,
  tracker,
  majorSection,
}: BuildIxbrlNodesFromMappingParams): { factName: TagMappingItem['factName']; ixbrlNode: IXBRLNode }[] => {
  const orderedItems = getOrderedMappingItems({ mapping, majorSection });
  return buildXBRLNodes({ items: orderedItems, mapping, utrCodeToUtrvMap, tracker });
};

export const getContextualIxbrlNodes = (params: BuildIxbrlNodesFromMappingParams) => {
  const builtNodes = buildIxbrlNodesFromMapping(params);

  return builtNodes.map((node) => {
    const paragraph = $createParagraphNode();
    paragraph.append(
      $createTextNode(`${getFactLabel(node.factName)}`),
      $createLineBreakNode(),
      $createTextNode('Value:  '),
      node.ixbrlNode
    );
    return paragraph;
  });
};
