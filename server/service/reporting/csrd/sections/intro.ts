/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { $createParagraphNode, $createTextNode } from 'lexical';
import { $createHeadingNode } from '@lexical/rich-text';
import { type LexicalNode } from 'lexical/LexicalNode';
import { type SectionData } from '../../xhtml/types';

export function buildIntro(_sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('📘 Cross-Cutting Standards'));

  const paragraph = $createParagraphNode();
  paragraph.append(
    $createTextNode(
      'This report is prepared in accordance with the European Sustainability Reporting Standards (ESRS) to fulfill the requirements set forth by the Corporate Sustainability Reporting Directive (CSRD). It encompasses our organization\'s approach to sustainability, including our strategies, governance, and performance metrics across environmental, social, and governance (ESG) domains.'
    )
  );

  return [heading, paragraph];
}
