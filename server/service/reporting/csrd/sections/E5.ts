/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { SectionData } from '../../xhtml/types';
import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { getContextualIxbrlNodes, MAJOR_SECTION } from '../utils';

export function buildE5Section(sectionData: SectionData) {
  const nodes = [];

  const titleNode = $createHeadingNode('h2');
  titleNode.append($createTextNode('🌿 Environmental Standards (E-Series)'));
  nodes.push(titleNode);

  const subtitleNode = $createHeadingNode('h3');
  subtitleNode.append($createTextNode('ESRS E5 – Resource Use and Circular Economy'));
  nodes.push(subtitleNode);

  const introNode = $createParagraphNode();
  introNode.append(
    $createTextNode(
      'This section covers disclosures related to resource usage and circular economy principles, including material consumption, waste management, and resource efficiency.'
    )
  );
  nodes.push(introNode);

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E5.code,
  });

  return [...nodes, ...contextualIxbrlNodes];
}
