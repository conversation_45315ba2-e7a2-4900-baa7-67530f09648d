/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { SectionData } from '../../xhtml/types';
import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { getContextualIxbrlNodes, MAJOR_SECTION } from '../utils';

export function buildG1Section(sectionData: SectionData) {
  const nodes = [];

  const titleNode = $createHeadingNode('h2');
  titleNode.append($createTextNode('🏛️ Governance Standard (G-Series)'));
  nodes.push(titleNode);

  const subtitleNode = $createHeadingNode('h3');
  subtitleNode.append($createTextNode('ESRS G1 – Business Conduct'));
  nodes.push(subtitleNode);

  const introNode = $createParagraphNode();
  introNode.append(
    $createTextNode(
      'This section covers disclosures related to the entity\'s business conduct, including corporate governance, business ethics, anti-corruption practices, and transparency.'
    )
  );
  nodes.push(introNode);

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.G1.code,
  });

  return [...nodes, ...contextualIxbrlNodes];
}
