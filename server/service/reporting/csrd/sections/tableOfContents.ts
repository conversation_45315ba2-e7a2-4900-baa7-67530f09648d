/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { $createListItemNode, $createListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';
import type { SectionData } from '../../xhtml/types';

export function buildTableOfContents(_sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode('📑 Table of Contents'));

  const list = $createListNode('number');

  const items = [
    '1. General Information',
    '2. Environmental Information',
    '3. Social Information',
    '4. Governance Information',
  ];

  items.forEach((itemText) => {
    const listItem = $createListItemNode();
    const paragraph = $createParagraphNode();
    paragraph.append($createTextNode(itemText));
    listItem.append(paragraph);
    list.append(listItem);
  });

  return [heading, list];
}
