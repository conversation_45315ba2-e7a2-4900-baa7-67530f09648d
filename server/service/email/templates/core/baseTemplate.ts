/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import config from '../../../../config';
import { getBaseDomain, UrlMapper } from '../../../url/UrlMapper';
import { Link } from '../htmlGenerator';
import { tryGetContext } from '../../../../middleware/audit/contextMiddleware';
import { BrandingTemplate, DomainConfig } from '../../../organization/domainConfig';
import { AppConfig } from '../../../app/AppConfig';

const assetsCDN = config.assets.cdn;

const imageMap = {
  sdgsImage: assetsCDN + '/images/sustainable-development-goals-300.png',
};

const socialMedia = {
  instagram: assetsCDN + '/images/social-media/instagram-logo-24px.png',
  facebook: assetsCDN + '/images/social-media/facebook-logo-16-px.png',
  twitter: assetsCDN + '/images/social-media/twitter-logo-16-px.png',
  linkedin: assetsCDN + '/images/social-media/linkedin-logo-16-px.png',
};

interface EmailArticle {
  logo?: string;
  title?: string;
  content?: string;
  enabled: boolean;
}

const defaultArticle: EmailArticle = {
//  logo: assetsCDN + '/images/article1-thumb.jpg',
  title: 'World Wide Generation.',
  content: 'Uniting the world as one ecosystem, with one focus and one mission: delivering a global resolution to poverty, inequality and climate change by 2030.',
  enabled: false,
};

const defaultProgress = {
  clientProfile: assetsCDN + '/images/emails/client_avatar.png',
  client80Percent: assetsCDN + '/images/emails/80-percest-text.png',
  clientProgressBackground: assetsCDN + '/images/emails/avatar-bg-image.jpg',
  enabled: false,
};

const contactInfo = {
  contactUrl: `mailto:${config.email.supportEmail}`,
  contactTitle: 'Contact Us',
  marketingTitle: 'worldwidegeneration.co',
  marketingHostname: config.email.marketingSiteHostname,
  memberLoginTitle: 'Member Login',
};

export interface BaseTemplateData {
  logoUrl?: string;
  link?: Link,
  domain: string | undefined;
  domainConfig: DomainConfig | undefined;
  appConfig: AppConfig | undefined;
  preFooter?: string;
  topContentHeader?: string;
  topContent: string;
  user: {
    firstName?: string,
    surname?: string
  },
  heroImage?: string;
  article?: EmailArticle,
  [key: string]: any;
}

const getLogo = (data: BaseTemplateData): string => {
  if (data.logoUrl) {
    return data.logoUrl;
  }

  if (data.appConfig?.whitelabel?.emailLogo) {
    return data.appConfig.whitelabel.emailLogo
  }

  const domainConfig = data.domainConfig;
  if (domainConfig) {
    return domainConfig.logoUrl
  }
  return config.assets.defaultLogo;
}

/* tslint:disable:max-line-length */
const htmlHead = () => `
<head>

  <!-- Define Charset -->
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <!-- Responsive Meta Tag -->
  <meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0;">


  <title>World Wide Generation</title>
  <style type="text/css">

    body{
      width: 100%;
      background-color: #fff;
      margin:0;
      padding:0;
      -webkit-font-smoothing: antialiased;
      -webkit-text-size-adjust:none;
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      mso-margin-top-alt:0px; mso-margin-bottom-alt:0px; mso-padding-alt: 0px 0px 0px 0px;
      font-family: sailec, sans-serif;
    }

    html{
      width: 100%;
    }

    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }

    .avatar {
      border-radius: 50%;
    }

    .text-secondary {
      color: #70707a;
    }

    .text-table {
      color: #2971e5;
    }

    a {
      color: #70707a;
    }

    p, ol, ul {
      line-height: 1.5;
    }

    table.table-striped tr:nth-child(even) {
      background-color: #f1f1f1;
    }

    table.table-striped thead {
        background: #f5f5f0;
    }

    table.table-striped th {
        color: #333333;
        padding: 5px;
    }

    table.table-striped td {
     border-bottom: 1px solid #ddd;
     padding: 5px;
    }

    table.table-striped th {
     padding: 5px;
    }


    /* ----------- responsivity ----------- */

    @media only screen and (max-width: 640px){

      *[class=image600] img{width:100% !important; height:auto !important}
      *[class=padding_top] {padding-top:5px !important}
      *[class=padding_right_25] {padding-right:15px !important;}
      *[class=padding_right_26] {padding-right:15px !important; vertical-align:top !important}
      *[class=padding_right_27] {padding-right:15px !important; vertical-align:top !important}
      *[class=padding_l] {padding-left:30px !important}
      *[class=padding_00] {padding-left:15px !important;}
      *[class=padding000] {padding-left:0px !important; padding-right:0px !important;}
      *[class=padding_001] {padding-left:15px !important; vertical-align:top !important}
      *[class=container600] {width: 100% !important}
      *[class=container193] {width: 170px !important}
      *[class=min_width] {min-width: 100% !important}
      *[class=font24] {Font-size:20px !important; line-height:26px !important; padding-left:0px !important;}
      *[class=font_s] {Font-size:14px !important; line-height:20px !important;}
      *[class=hide] {display:none !important;}
      *[class=center] {text-align: center !important;}
      *[class=font144] {padding-left:0px !important;}

      *[class=padding222] {padding-top: 30px !important;}
      *[class=padding_0] {padding-left:60px !important; padding-right:0px !important;}
      *[class=padding_012] {padding-left:70px !important; padding-right:0px !important;}
      *[class=image133] img{width:100px !important; height:auto !important}
      *[class=font18] {Font-size:16px !important; line-height:24px !important; padding-left:0px !important;}
    }


  </style>
</head>
`;

const bodyHeader = (data: BaseTemplateData) => `
    <table class="container600" align="center" border="0" width="600" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
      <tbody><tr>
        <td align="left" style="padding: 20px 45px;">
          <img src="${getLogo(data)}" style="display: block;color:#70707a" height="100" border="0" alt="Image">
        </td>
      </tr>
      </tbody>
    </table>
`;

const defaultTopContent = `<br><br>`;

const getTopContentHeader = (data: BaseTemplateData) => {
  if (data.topContentHeader) {
    return data.topContentHeader;
  }

  const { firstName, surname } = data.user;
  if (!firstName && !surname) {
    return ''
  }

  return `
  <tr>
      <td align = "left" style = "font-family: sailec, sans-serif; font-size: 18px; line-height: 30px; font-style: normal; color: #70707a; letter-spacing: -.3px; padding: 50px 0 0;" >
      Dear ${data.user.firstName || data.user.surname},
      <br/>
    </td>
  </tr>
  `;
};

const contentTop = (data: BaseTemplateData): string => {
  const content = data.topContent || defaultTopContent;
  return `
  <table class="container600" align="center" border="0" width="480" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
    <tbody>
      ${getTopContentHeader(data)}
    <tr>
      <td align="left" style="font-family: sailec, sans-serif; font-size: 18px; line-height: 27px; font-weight: normal; font-style: normal; color: #70707a; letter-spacing: -.3px; padding: 10px 0 10px;">
        ${content}
      </td>
    </tr>
    </tbody>
    </table>
  `;
};

const defaultBottomContent = '<br>';
const contentBottom = (data: BaseTemplateData): string => {
  const content = data.bottomContent || defaultBottomContent;
  return `
    <table class="container600" align="center" border="0" width="480" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
      <tbody>
        ${content}
      </tbody>
    </table>
  `;
};

export function renderContentBottom() {
  return (`
    <p><span style = 'color: #2971e5' > Thank you </span></p>
  `);
}

export function renderSocialMedia(data: BaseTemplateData) {
  return `
    <table class="container600" align="center" border="0" width="297" cellpadding="0" cellspacing="0" bgcolor="#f5f9fc" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; max-width: 297px;">
    <tbody><tr>
      <td align="center" style="padding-left: 60px;">&nbsp;</td>
      <td align="center" style="padding-right: 60px;">
        <a href="https://www.linkedin.com/company/world-wide-generation-ltd/" style="color: #00e1be; text-decoration: none;" target="_blank"><img src="${socialMedia.linkedin}" style="display: block;" width="20" height="20" border="0" alt="linkedin"></a>
      </td>
      <td align="center">
        <a href="https://twitter.com/World_Wide_Gen?lang=en" style="color: #00e1be; text-decoration: none;" target="_blank"><img src="${socialMedia.twitter}" style="display: block;" width="20" height="17" border="0" alt="twitter"></a>
      </td>
      <td align="center" style="padding-right: 60px;">&nbsp;</td>
    </tr>
    </tbody>
  </table>
  `;
}

function renderFooter(data: BaseTemplateData) {
  const policyUrl = UrlMapper.relativeUrl('/legal-privacy-policy/', data.domain);
  const baseUrl = getBaseDomain(data.domain);

  return `
   <table class="container600" align="center" border="0" width="400" cellpadding="0" cellspacing="0" bgcolor="#f5f9fc" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
     <tbody>
     <tr>
        <td align="center" style="font-family: sailec, sans-serif; font-size: 12px; line-height: 25px; font-weight: normal; font-style: normal; color: #70707a; padding: 27px 0 40px;">
        <p> To find out how we handle your data, please see our
          <a href="${policyUrl}" style="color:#70707a" target="_blank">Privacy Policy</a><br>
        </p>
          <a href="${contactInfo.marketingHostname}" style="color:#70707a" target="_blank">${contactInfo.marketingTitle}</a> |
          <a href="${baseUrl}" style="color:#70707a" target="_blank">G17.eco</a>
        </td>
     </tr>
     </tbody>
   </table>
  `;
}

function renderArticle(data: BaseTemplateData) {

  const currentArticle = data.article || {};
  const articleData = {...defaultArticle, ...currentArticle};

  if (!articleData.enabled) {
    return '';
  }

  return `
  <table class="container600" align="center" border="0" width="600" cellpadding="0" cellspacing="0" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
    <tbody><tr>
      <td align="center" style="padding: 30px 15px 27px;">
        <table class="container600" align="center" border="0" width="542" cellpadding="0" cellspacing="0" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
          <tbody><tr>

            <td class="padding_00" align="left" valign="top" style="padding-left: 5px;" width="450">
              <table align="left" border="0" width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                <tbody><tr>
                  <td class="font18" align="center" style="font-family: sailec, sans-serif; font-size: 18px; line-height: 22px; font-weight: bold; font-style: normal; color: #363843; letter-spacing: -.3px; padding: 9px 0 0;">
                    ${articleData.title}
                  </td>
                </tr>

                <tr>
                  <td class="hide" align="center" style="font-family: sailec, sans-serif; font-size: 14px; line-height: 22px; font-weight: normal; font-style: normal; color: #70707a; letter-spacing: -.2px; padding: 6px 0 0;">
                    ${articleData.content}
                  </td>
                </tr>
                </tbody></table>
            </td>
          </tr>
          </tbody></table>
      </td>
    </tr>
    </tbody>
  </table>
 `;
}

function renderProgressImages(data: BaseTemplateData) {

  const currentProgress = data.progress || {};
  const progressData = {...defaultProgress, ...currentProgress};

  if (!progressData.enabled) {
    return '';
  }

  return `
     <tr>
      <td align="center" style="padding: 82px 15px 0;">
        <table class="container600" align="center" border="0" width="480" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; max-width: 480px;">
          <tbody>
          <tr>
            <td background="${progressData.clientProgressBackground}" style="background-size: 100% 100%;" bgcolor="#FFFFFF" width="480" height="103" valign="top">
              <!--[if gte mso 9]>
              <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:480px;height:103px;">
                <v:fill type="tile" src="${progressData.clientProgressBackground}" color="#FFFFFF" />
                <v:textbox inset="0,0,0,0">
              <![endif]-->
              <div>
                <table align="center" border="0" width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                  <tbody><tr>
                    <td class="padding_0" align="center" style="padding-left: 257px; padding-right: 120px;">
                      <img src="${progressData.clientProfile}" style="display: block;" width="103" height="103" border="0" alt="Image">
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
              <!--[if gte mso 9]>
              </v:textbox>
              </v:rect>
              <![endif]-->
            </td>
          </tr>
          </tbody>
        </table>
      </td>
    </tr>
    <tr>
      <td align="center" style="padding: 0 15px;">
        <table class="container600" align="center" border="0" width="480" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
          <tbody><tr>
            <td class="padding_0" align="center" style="padding-left: 150px; padding-top: 16px;">
              <img src="${progressData.client80Percent}" style="display: block;" width="64" height="19" border="0" alt="Image">
            </td>
          </tr>
          </tbody></table>
      </td>
    </tr>
`;
}

function renderCTA(data: BaseTemplateData) {

  if (!data.link || data.link.type !== 'button') {
    return `<tr><td align="center" style="padding: 15px;">&nbsp;</td></tr>`;
  }

  const ctaButton = data.link.text || 'View Tracker';
  const bgColour = '#2971e5'; // || '#4ed5da';
  const ctaLogin = contactInfo.memberLoginTitle;
  const loginUrl = getBaseDomain(data.domain);

  return `
  <tr>
    <td align="center" style="padding-bottom: 45px; padding-top:60px">
      <a href="${data.link.url}" style="font-family: sailec, sans-serif;font-size: 20px;line-height: 20px;font-weight: normal;font-style: normal;border-radius: 8px;cursor: pointer;text-decoration: none;color: #FFFFFF;height: 100%;padding: 20px 10px;background-color: ${bgColour};width: 340px;display: inline-block;" target="_blank">
        ${ctaButton}
      </a>
    </td>
    <tr>
      <td align="center" style="font-family: sailec, sans-serif; font-size: 18px; line-height: 27px; font-weight: normal; font-style: normal; color: #70707a; letter-spacing: -.3px; padding-bottom: 60px; padding-top: 20px; ">
        <a href="${contactInfo.contactUrl}" style="color:#70707a; padding-right: 10px;" target="_blank"><u>${contactInfo.contactTitle}</u></a>
        <a href="${loginUrl}" style="color:#70707a; padding-left: 10px;" target="_blank"><u>${ctaLogin}</u></a>
      </td>
    </tr>
</tr>
  `;
}

function heroImage(data: BaseTemplateData) {

  if (data.heroImage === undefined || data.heroImage === '') {
    return '';
  }

  return `
  <tr>
    <td class="image600" align="center" style="padding: 15px 0 0;">
      <img src="${data.heroImage || imageMap.sdgsImage}" style="display: block;" width="600" border="0" alt="Image">
    </td>
  </tr>
  `;
}

function renderMainContent(data: BaseTemplateData): string {

  return `
     <table class="container600" align="center" border="0" width="100%" cellpadding="0" cellspacing="0" bgcolor="#F5F9FC" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
  <tbody><tr>
    <td align="center" class="min_width" style="min-width: 600px;" bgcolor="#F5F9FC">
      <table class="container600" align="center" border="0" width="600" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
        <tbody>
        ${heroImage(data)}
        <tr>
          <td align="center" style="padding: 0 15px;">
            ${contentTop(data)}
          </td>
        </tr>

        ${renderProgressImages(data)}
        ${contentBottom(data)}
        ${renderCTA(data)}
        </tbody>
        </table>
    </td>
  </tr>

  <tr><td height="16"></td></tr>
  <tr>
    <td align="center" class="min_width" style="min-width: 600px;" bgcolor="#F5F9FC">
      ${renderArticle(data)}
    </td>
  </tr>
  </tbody>
</table>
`;
}

function renderPreFooter(data: BaseTemplateData) {
  if (!data.preFooter) {
    return '';
  }

  return `
  <tr>
    <td align="center" style="font-family: sailec, sans-serif; font-size: 12px; line-height: 25px; font-weight: normal; font-style: normal; color: #70707a; padding: 27px 0 10px;">
      ${data.preFooter}
    </td>
  </tr>
  `


}

const getDomainData = (baseData: BaseTemplateData) => {
  // domain config is not reliable, email template should be based on your app config
  // otherwise sgx users not from Singapore will receive wrong templates
  if (baseData.appConfig) {
    return { domainConfig: undefined, domain: undefined };
  }

  if (baseData.domainConfig && baseData.domain) {
    // Have all information, no need to get from context
    return { domainConfig: baseData.domainConfig, domain: baseData.domain };
  }

  // Expanded data if we don't have it passed in
  const context = tryGetContext();
  const domainConfig = baseData.domainConfig ?? context?.domainConfig;
  const domain = baseData.domain ?? context?.origin;
  return { domainConfig, domain };
};

export function getBrandingTemplate(data: { appConfig?: AppConfig, domainConfig?: DomainConfig }) {

  if (data.appConfig?.whitelabel?.brandingTemplate) {
    return data.appConfig.whitelabel.brandingTemplate;
  }

  const domainConfig = data.domainConfig ?? tryGetContext()?.domainConfig;
  return domainConfig?.brandingTemplate ?? BrandingTemplate.G17Eco;
}

/**
 * Expand data with Context in single place
 */
const getExpandedContextData = (baseData: BaseTemplateData) => {
  const { domainConfig, domain } = getDomainData(baseData);
  const data: BaseTemplateData = {
    ...baseData,
    domainConfig: domainConfig,
    domain: domain,
    branding: getBrandingTemplate(baseData),
  }
  return data;
};

const baseTemplate = (baseData: BaseTemplateData) => {
  const data = getExpandedContextData(baseData);

  return `
 ${htmlHead()}
<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" style="width: 100%; -webkit-font-smoothing: antialiased; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; mso-margin-top-alt: 0px; mso-margin-bottom-alt: 0px; mso-padding-alt: 0px 0px 0px 0px; font-family: sailec, sans-serif; background: #f5f9fc; margin: 0; padding: 0;" bgcolor="#f5f9fc">
  <table border="0" width="100%" cellpadding="0" cellspacing="0" bgcolor="f5f9fc">
    <tbody><tr><td height="40"></td></tr>
    <tr>
      <td align="center" bgcolor="#f5f9fc" class="min_width" style="min-width: 600px;">
       ${bodyHeader(data)}
      </td>
    </tr>
    <tr>
      <td align="center">
        ${renderMainContent(data)}
      </td>
    </tr>
    <tr>
      <td align="center" bgcolor="#f5f9fc" class="min_width" style="min-width: 600px;">
        <table class="container600" align="center" border="0" width="600" cellpadding="0" cellspacing="0" bgcolor="#f5f9fc" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
          <tbody><tr>
            <td align="center" style="padding: 39px 0 0;">
              ${renderSocialMedia(data)}
            </td>
          </tr>
          ${renderPreFooter(data)}
          <tr>
            <td align="center" style="padding: 0 15px;">
               ${renderFooter(data)}
            </td>
          </tr>
          </tbody>
        </table>
      </td>
    </tr>
    </tbody>
  </table>
  <div style="display: none; white-space: nowrap; font: 15px courier;">
    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
  </div>
</body>
`;
};

export default baseTemplate;
