/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { NotApplicableTypes } from '../../models/universalTrackerValue';
import { ActionList } from '../utr/constants';
import { getStandardName } from '../utr/standards';
import { ColumnType, TableColumn, UtrValueType } from '../../models/public/universalTrackerType';
import { customDateFormat, DateFormat, isSame } from '../../util/date';
import { Option, ValueList as ValueListPlain } from '../../models/public/valueList';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { BaseUtr, ReportUtrv } from './reportTypes';
import { SupportedMeasureUnits, UnitConfig } from '../units/unitTypes';
import { SurveyModelPlain } from '../../models/survey';
import { getTagTextByUtrId } from '../../util/metric-group';
import { generateMapping } from './dataResolverUtil';
import { PACK, QUESTION, SURVEY } from '../../util/terminology';
import { InitiativeWithMatchedSurveyFilters, RecordRow } from './constants';
import { RowData, TableData } from '../../models/public/universalTrackerValueType';
import { NUMERIC_VALUE_LIST_TOTAL_CODE } from '../../models/universalTracker';
import { SurveyFilter } from '../../models/customReport';
import { getInitiativeReportHeader } from './utils';


export interface CustomReportData extends BaseUtr {
  maxRows?: number;
  utrvs: ReportUtrv[];
  unitConfig: UnitConfig | undefined;
}

type ExportColumn = Pick<TableColumn, 'code' | 'type' | 'name' | 'unit' | 'unitType' | 'numberScale' | 'listId'>;

interface CurrentRow extends CustomReportData {
  _currentRow: {
    findByGroupBy?: (row: RowData[]) => boolean;
    valueListMap: Map<string, Option[]>;
    column?: ExportColumn;
    tableRowIndex?: number;
  };
}

interface ColumnSetup<T = CurrentRow> {
  id: string;
  name: string;
  accessor: (utr: T) => string | number | undefined,
}

export interface ConvertDataParams {
  data: CustomReportData[];
  initiatives: InitiativeWithMatchedSurveyFilters[];
  valueLists: ValueListPlain[];
  surveyFilters: SurveyFilter[];
  unitConfig: UnitConfig | undefined;
  utrTagMap: Map<string, string[]>;
}

interface ConvertSurveyDataParams {
  data: CustomReportData[];
  surveys: Pick<SurveyModelPlain, '_id' | 'name' | 'effectiveDate' | 'unitConfig'>[];
  valueLists: ValueListPlain[];
  utrTagMap: Map<string, string[]>;
}

interface ExtractValueParams {
  utr: CurrentRow,
  utrv: ReportUtrv;
  unitConfig: UnitConfig | undefined;
}

interface ProcessUtrParams {
  combinedColumns: ColumnSetup[];
  utr: CustomReportData;
  valueListMap: Map<string, Option[]>;
}

export class CustomReportGenerator {
  private valueListNumericTotal = NUMERIC_VALUE_LIST_TOTAL_CODE;

  constructor(
    private logger: LoggerInterface,
  ) {
  }

  /**
   * "Standard": "Custom Metric",
   * "Unique Identifier": "627e322f10ee260007f66",
   * "Value Type": "table",
   * "Value Chain": "Composite",
   * "Question": "Please state the Power Supply Company with which your office has a contract",
   * "Sub-question": "Enter the name of the power supply company below",
   * "Row": "",
   * "Unit": "",
   * "Subsidiary 1": "",
   * "Subsidiary 2": ""
   */

  public getSurveyColumnSetup(): ColumnSetup[] {
    return [
      {
        id: 'standard',
        name: `${SURVEY.CAPITALIZED_ADJECTIVE} ${PACK.SINGULAR}`,
        accessor: (data) => getStandardName(data.type),
      },
      {
        id: 'code',
        name: 'Code',
        accessor: (data) => data.typeCode,
      },
      {
        id: 'mapping',
        name: 'Mapping',
        accessor: generateMapping,
      },
      {
        id: 'valueType',
        name: `${QUESTION.CAPITALIZED_SINGULAR} Type`,
        accessor: (data) => data.valueType,
      },
      {
        id: 'name',
        name: `${QUESTION.CAPITALIZED_SINGULAR} Title`,
        accessor: (data) => data.name,
      },
      {
        id: 'valueLabel',
        name: QUESTION.CAPITALIZED_SINGULAR,
        accessor: (data) => data.valueLabel,
      },
      {
        id: 'columnLabel',
        name: `Sub-${QUESTION.SINGULAR}`,
        accessor: (data) => data._currentRow.column?.name,
      },
      {
        id: 'tableRowIndex',
        name: 'Row',
        accessor: (data) => {
          const index = data._currentRow.tableRowIndex;
          return index === undefined ? '' : `Row ${index + 1} answer`;
        },
      },
      {
        id: 'unit',
        name: 'Unit',
        accessor: (data) => {
          const unitType = data.unitType || data._currentRow.column?.unitType;
          if (unitType === SupportedMeasureUnits.currency) {
            return '';
          }

          return data.unit || data._currentRow.column?.unit || '';
        },
      },
      {
        id: 'numberScale',
        name: 'Number Scale',
        accessor: (data) => data.numberScale || data._currentRow.column?.numberScale || '',
      },
    ];
  }

  public getTagColumn(utrTagMap: Map<string, string[]>): ColumnSetup[] {
    return [
      {
        id: 'tag',
        name: 'Tag',
        accessor: (utr) => {
          return getTagTextByUtrId(utrTagMap, utr._id);
        },
      },
    ];
  }

  private getValueListOptions(utr: BaseUtr): Option[] | undefined {
    const valueList = utr.valueValidation?.valueList;
    return valueList?.list ?? valueList?.custom
  }

  public async convertData({ data, initiatives, valueLists, utrTagMap, unitConfig, surveyFilters }: ConvertDataParams) {
    const valueListMap = new Map(valueLists.map((list) => [String(list._id), list.options]));

    data.forEach(utr => {
      const vl = utr.valueValidation?.valueList;
      // Expand valueList
      if (vl?.listId && !vl.list) {
        vl.list = valueListMap.get(String(vl.listId));
      }
    });

    const columns = this.getSurveyColumnSetup();

    const initiativeColumns = this.getInitiativeColumns({
      initiatives,
      surveyIncluded: surveyFilters.length > 1,
      unitConfig,
    });

    const combinedColumns = columns.concat(this.getTagColumn(utrTagMap)).concat(initiativeColumns);

    // Generate data array
    const outputData: RecordRow[] = [];

    data.forEach((utr) => {
      const records = this.processUtr({
        combinedColumns: combinedColumns,
        utr,
        valueListMap,
      });

      outputData.push(...records);
    })

    return {
      columns: combinedColumns,
      headers: combinedColumns.map(h => h.name),
      records: outputData,
    }
  }

  private getInitiativeColumns({
    surveyIncluded,
    initiatives,
    unitConfig,
  }: Pick<ConvertDataParams, 'initiatives' | 'unitConfig'> & { surveyIncluded: boolean }) {
    // Expand columns with subsidiary columns
    return initiatives
      .map((initiative) => {
        const id = initiative._id.toHexString();
        return initiative.matchedSurveyFilters.map(({ effectiveDate, period, type }) => {
          return {
            id: id,
            name: getInitiativeReportHeader({
              name: initiative.name,
              effectiveDate,
              period,
              type,
              surveyIncluded,
            }),
            accessor: (utr: CurrentRow) => {
              const utrv = utr.utrvs.find(
                (v) =>
                  v.initiativeId.toHexString() === id &&
                  isSame(v.effectiveDate, effectiveDate, 'day') &&
                  v.surveyPeriod === period &&
                  v.surveyType === type
              );
              if (!utrv) {
                return undefined;
              }
              return this.extractValue({ utr, utrv, unitConfig });
            },
          };
        });
      })
      .flat();
  }

  public async convertSurveyData({ data, surveys, valueLists, utrTagMap }: ConvertSurveyDataParams) {

    const valueListMap = new Map(valueLists.map(list => [String(list._id), list.options]))

    data.forEach(utr => {
      const vl = utr.valueValidation?.valueList;
      // Expand valueList
      if (vl?.listId && !vl.list) {
        vl.list = valueListMap.get(String(vl.listId));
      }
    });

    const columns = this.getSurveyColumnSetup();

    // Expand columns with survey columns
    const extraColumns: ColumnSetup[] = surveys.map(survey => {
      const id = survey._id.toHexString();
      return {
        id,
        name: customDateFormat(survey.effectiveDate, DateFormat.YearMonth),
        accessor: (utr) => {
          // from $project were we added surveyId, so we can do this now here
          const utrv = utr.utrvs.find((v) => v.surveyId.toHexString() === id);
          if (!utrv) {
            return undefined;
          }
          return this.extractValue({ utr, utrv, unitConfig: survey.unitConfig });
        },
      };
    })

    const combinedColumns = columns.concat(this.getTagColumn(utrTagMap)).concat(extraColumns);

    // Generate data array
    const outputData: RecordRow[] = [];

    data.forEach(utr => {
      const records = this.processUtr({
        combinedColumns: combinedColumns,
        utr,
        valueListMap,
      });

      outputData.push(...records);
    })

    return {
      columns: combinedColumns,
      headers: combinedColumns.map(h => h.name),
      records: outputData,
    }
  }

  private processUtr(params: ProcessUtrParams): RecordRow[] {

    const { utr, combinedColumns, valueListMap } = params;
    if (utr.valueType === UtrValueType.Table) {
      return this.processTable(params);
    }

    if ([UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(utr.valueType as UtrValueType)) {
      return this.processComplexValueList(params);
    }

    // All the single row values
    const row = combinedColumns.map(({ accessor }) => {
      return accessor({
        ...utr,
        _currentRow: { valueListMap }
      })
    });

    return [row];
  }

  private processTable({ utr, valueListMap, combinedColumns }: ProcessUtrParams) {
    const outputRows: RecordRow[] = [];
    const tableColumns = utr.valueValidation?.table?.columns;

    if (!tableColumns) {
      this.logger.error(new Error(`UTR ${utr._id} valueType=${utr.valueType} is missing table configuration`));
      return [];
    }

    const groupColumns = utr.valueValidation?.table?.aggregation?.columns.map(c => c.code);
    const getRowKey = (row: RowData[]) => row.filter(col => groupColumns?.includes(col.code)).map(c => c.value).join('-');
    const getTableKeys = (table?: TableData): string[] => table?.map(row => getRowKey(row)) || [];

    const combinedGroupKeys = Array.from(
        new Set(utr.utrvs.reduce<string[]>((keys, utrv) => {
        if (utrv.valueData?.table) {
          return [
            ...keys,
            ...getTableKeys(utrv.valueData?.table),
          ];
        }
        return keys
        }, []
      ))
    );

    const getMaxRows = () => {
      if (groupColumns) {
        // We need to potentially output a different number of rows than expected, so use this
        return combinedGroupKeys.length;
      }
      // NOTE: I changed this because it looked odd before, as it would output as many rows as maxRows,
      // which most UTRs won't have if they are != maxRows = 1. Now I just set it to the max table row count, is this correct?
      if (utr.maxRows) {
        return utr.maxRows;
      }
      // Find the max rows of any utrv
      return utr.utrvs.reduce((max, utrv) => Math.max(max, utrv.valueData?.table?.length || 0), 0);
    }

    const maxRows = getMaxRows();

    for (let i = 0; i < maxRows; i++) {
      const currentGroupKey = groupColumns ? combinedGroupKeys[i] : undefined;
      const findByGroupBy = currentGroupKey ? (row: RowData[]) => getRowKey(row) === currentGroupKey : undefined;

      tableColumns.forEach((c) => {
        // Represent a row for single utrv table column for each row
        const outputColumns = combinedColumns.map(({ accessor }) => {
          return accessor({
            ...utr,
            _currentRow: {
              findByGroupBy,
              valueListMap,
              column: c,
              tableRowIndex: i,
            },
          });
        });

        outputRows.push(outputColumns);
      });
    }
    return outputRows;
  }

  private extractValue(params: ExtractValueParams): string | number | undefined {

    const { utr, utrv } = params

    const naType = this.getNaType(utrv);
    if (naType) {
      return this.naType(naType);
    }

    switch (utr.valueType) {
      case UtrValueType.Text:
      case UtrValueType.Date:
        return utrv.valueData?.data;
      case UtrValueType.ValueList:
      case UtrValueType.ValueListMulti:
        return this.resolveValueListMulti(utrv, utr);
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList:
        return this.resolveComplexValueList(params)
      case UtrValueType.Table:
        return this.resolveTable(params)
      case UtrValueType.Number:
      case UtrValueType.Sample:
      case UtrValueType.Percentage:
      default:
        return this.isNumericWithCurrency(utr, utrv.value) ? `${utrv?.valueData?.input?.unit || utr.unit || ''} ${utrv.value}` : utrv.value;
    }

  }

  private isNumericWithCurrency(utr: Pick<BaseUtr, 'unitType' | 'unit'>, value: number | string | undefined): value is number {
    return Boolean(utr.unitType === SupportedMeasureUnits.currency && utr.unit && typeof value === 'number');
  }

  /**
   * A copy of frontend getNaType logic
   */
  private getNaType({ status, value, valueData }: ReportUtrv): string {

    // Created items can't have a value set, so can't be N/A
    if (status === ActionList.Created) {
      return '';
    }

    // Value is set, can't be N/A
    if (value !== undefined) {
      return '';
    }

    // value is empty and there is no valueData, must be N/A (legacy utrvs)
    if (!valueData) {
      // This seems to be wrong on frontend, fixed by returning NA rather than '';
      // but I guess never happen, as valueData is always defined
      return NotApplicableTypes.NA;
    }

    //This property is a dead give-away, but is not guaranteed
    const { notApplicableType, table, data } = valueData;

    if (notApplicableType) {
      return notApplicableType;
    }

    // ValueData table/data are both empty
    const emptyTable = table === undefined || table.length === 0;
    return data === undefined && emptyTable ? NotApplicableTypes.NA : '';
  }

  private resolveValueListMulti(utrv: ReportUtrv, utr: BaseUtr) {
    const value = utrv.valueData?.data;
    if (!value) {
      return undefined;
    }
    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return undefined;
    }
    return this.resolveOptions(value, options);
  }

  private resolveOptions(value: string[] | string, options: Option[]) {
    const valueArray = Array.isArray(value) ? value : [value]
    return valueArray.reduce<string[]>((a, code) => {
      const option = options.find(o => o.code === code);
      return option ? [...a, option.name] : a;
    }, []).join(', ');
  }

  private naType(notApplicableType: string) {
    if (notApplicableType === NotApplicableTypes.NR) {
      return 'NR';
    }
    return 'NA';
  }

  private resolveComplexValueList({ utrv, utr }: ExtractValueParams) {

    const column = utr._currentRow.column;
    const data = utrv.valueData?.data;


    if (column && typeof data === 'object') {

      // Handle special case of total
      if (column.code === this.valueListNumericTotal) {
        const values: number[] = Object.values(data);
        if (values.length > 0) {
          return values.reduce((a, value) => !isNaN(value) ? a + Number(value) : a, 0)
        }
        return undefined;
      }

      return data[column.code]
    }
  }

  private resolveRowValue({ utrv, utr }: Pick<ExtractValueParams, 'utrv' | 'utr'>) {
    const { column, tableRowIndex = 0, findByGroupBy } = utr._currentRow;
    const table = utrv.valueData?.table;

    if (findByGroupBy) {
      return table?.find(findByGroupBy)?.find((col) => col.code === column?.code)?.value;
    }

    return table?.[tableRowIndex]?.find((col) => col.code === column?.code)?.value;
  }

  private resolveTable({ utrv, utr, unitConfig }: ExtractValueParams) {
    const { column, valueListMap } = utr._currentRow;
    if (column && Array.isArray(utrv.valueData?.table)) {
      const value = this.resolveRowValue({ utrv, utr });

      // Need pass through 0 here as well
      if (value === undefined || value === '') {
        return value;
      }

      // Text can use values from valueList, (ValueList was just Text + listId)
      if (
        column.listId &&
        [ColumnType.ValueListMulti, ColumnType.ValueList, ColumnType.Text].includes(column.type as ColumnType)
      ) {
        const options = valueListMap.get(column.listId.toHexString()) ?? [];
        return this.resolveOptions(value, options);
      }

      if (ColumnType.Number === column.type && column.unitType === SupportedMeasureUnits.currency) {
        return `${utr.unitConfig?.currency || unitConfig?.currency || column.unit || ''} ${value}`;
      }

      return value;
    }
  }

  private processComplexValueList(params: ProcessUtrParams): RecordRow[] {
    const { utr, combinedColumns, valueListMap } = params;

    let options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return [];
    }

    if (utr.valueType === UtrValueType.NumericValueList) {
      options = [...options, { code: this.valueListNumericTotal, name: 'Total' }]
    }

    return options.map((c) => {
      // Represent a row for single utrv value list option
      return combinedColumns.map(({ accessor }) => {
        return accessor({
          ...utr,
          _currentRow: {
            valueListMap,
            column: { ...c, type: ColumnType.Text },
          }
        })
      });
    })
  }
}

let instance: CustomReportGenerator;
export const getCustomReportGenerator = () => {
  if (!instance) {
    instance = new CustomReportGenerator(
      wwgLogger,
    );
  }
  return instance;
}
