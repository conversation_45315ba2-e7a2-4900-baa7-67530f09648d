import { isAxiosError } from 'axios';
import ContextError, { type ErrorDetails } from '../../../error/ContextError';
import config from '../../../config';

/**
 * Creates a ContextError from an Axios error with all details in context
 */
export function createGreenlyApiError(error: unknown): ContextError {
  if (!isAxiosError(error)) {
    return new ContextError('[GREENLY] API Error', { cause: error });
  }

  const { response, config: axiosConfig } = error;
  const context: ErrorDetails = {
    url: axiosConfig?.url,
    method: axiosConfig?.method,
    requestData: config.isProduction ? undefined : axiosConfig?.data,
    cause: config.isProduction ? undefined : error,
  };

  if (response) {
    context.status = response.status;
    // Include response body for debugging
    if (response.data) {
      context.responseData = response.data;
    }
  }

  return new ContextError('[GREENLY] API Error', context);
}

/**
 * Response interceptor for Axios to handle API errors
 */
export function greenlyResponseInterceptor(error: unknown) {
  if (isAxiosError(error) && error.response) {
    throw createGreenlyApiError(error);
  }
  return Promise.reject(error);
}

/**
 * Request interceptor error handler for Axios
 */
export function greenlyRequestErrorHandler(error: unknown) {
  if (isAxiosError(error)) {
    throw createGreenlyApiError(error);
  }
  return Promise.reject(error);
}
