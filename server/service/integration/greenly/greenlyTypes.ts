/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import type { DataPeriods } from '../../../types/constants';
import type { ObjectId } from 'bson';

/**
 * Greenly API Setup for a company
 */

interface SetupContextValue {
  code: string,
  name: string,
  value?: string | number | boolean,
  unit?: string
}

type Address = {
  street: string;
  city: string;
  state: string
  country: string;
  postalCode: string;
};

interface FacilityCreate {
  name: string;
  address: Address
  floorSpace: number;
  floorSpaceUnit: 'sqm' | 'sqft';
  ownerType: 'owner' | 'lessor' | 'lessee';
}

export type SetupCompany = {
  initiativeId: string;
  parentInitiativeId: string | undefined;
  name: string;
  logo: string | undefined;
  industryLevel3: string | undefined;
  description?: string;
  countryCode: string | undefined;

  language: 'en' | 'fr';
  /**
   * Year of the company "profile" to create. Each year it needs a new profile. "2024", "2025", etc.
   */
  year: string;

  validityStatus: ValidityStatus;

  type: 'freemium' | 'standalone';

  /**
   * External ID of the company on the Greenly platform.
   */
  externalId?: string;
}

export interface GreenlyCreateData {

  user: {
    _id: ObjectId;
    email: string
    jobTitle?: string;
    firstName: string;
    surname: string;
  }

  rootInitiative: SetupCompany;
  initiativeTree?: SetupCompany[];

  additionalContext: SetupContextValue[];

  facilities?: FacilityCreate[]
}

/**
 * Greenly API response for a reglementaryEntryEmissions
 *   {
 *         "category": "5.4",
 *         "description": {
 *             "fr": "Investissements",
 *             "en": "Investments"
 *         },
 *         "emissionsInKgCO2": 0,
 *         "kgCO2": 0,
 *         "kgCH4fossilAsCO2e": 0,
 *         "kgCH4biogenicAsCO2e": 0,
 *         "kgN2OAsCO2e": 0,
 *         "kgOtherAsCO2e": 0,
 *         "kgCO2Biogenic": 0,
 *         "frenchOfficialCategory": 5,
 *         }
 *
 */
export interface EntryEmissions {
  category: string
  description: Description
  emissionsInKgCO2: number
  kgCO2: number
  kgCH4fossilAsCO2e: number
  kgCH4biogenicAsCO2e: number
  kgN2OAsCO2e: number
  kgOtherAsCO2e: number
  kgCO2Biogenic: number
  frenchOfficialCategory: number
  scope: number
}

interface Description {
  fr?: string
  en: string
}


export interface ListResponse<T = unknown> {
  data: T[]
  totalCount: number
  totalNbPages: number
  order: Order[]
  limit: number
  offset: number
  language: string
}

export interface ExportableActivity {
  id: string
  ownerCompanyId: string
  ownerProfileId: string
  companyId: string
  profileId: string
  activityId: string
  language: string
  subScope: string
  marketBased: string
  companyName: string
  industry: string
  ownershipPercentage: number
  fiscalYear: string
  temporalScopeStart?: string
  temporalScopeEnd?: string
  parentBusinessCategory: string
  businessCategory: string
  activityName: string
  section: string
  subType?: string
  engineType?: string
  trainType: any
  flightType?: string
  travelClass?: string
  computedDistance?: number
  locationCountryId?: string
  activityType: string
  source: string
  specificModuleId?: string
  moduleCategory: string
  regulatoryMethodology: string
  revenueM: number
  employeesNumber: number
  scope: string
  subScopeName: string
  buildingId?: string
  siteName?: string
  buildingSurfaceAreaDoesuserhavetheinfo?: boolean | null;
  buildingSurfaceAreaValue?: number
  buildingSurfaceAreaUnit?: string
  buildingEmployees?: number
  isCrossEntity: boolean
  crossEntityFilter: string
  purchaseCategoryId: string
  isGenericPurchaseCategory: boolean
  entity?: string
  entityCountry?: string
  businessUnit?: string
  shouldBeExcluded: boolean
  quantity: number
  quantityForSubScope: number
  unit: string
  entityCreatedAt: string
  versionCreatedAt: string
  granularType: string
  firstFiscalMonth: string
  emissionsInKGCO2e: number
  emissionsIntCO2e: number
  emissionsIntCO2ePerEmployee: number
  emissionsIntCO2ePerRevenueM: number
  emissionFactorName: string
  emissionFactorId: string
  emissionFactorVersion: number
  emissionFactorNameAndUnit: string
  emissionFactorValue: number
  emissionFactorInputUnit: string
  emissionFactorOutputUnit: string
  emissionFactorReferentialType: string
  emissionFactorReferentialYear?: string
  emissionFactorGeographicalValidity?: string
  emissionFactorReferentialDescription?: string
  emissionFactorReferentialCategory?: string
  emissionFactorMethodology: string
  percentEmissionsInScope: number
  quarter?: string
  supplierName?: string
  amortizationDurationInYears: any
  acquisitionDate?: string
  activityAndServicesEmissions: number
  parentBusinessCategoryEnum: string
}

interface Order {
  field: string
  direction: string
}


export interface SurveyEmissionData {
  emissionData: EntryEmissions[];
  effectiveDate: Date
  period: DataPeriods;
}

interface CompanyIdentifier {
  /**
   * We should always use operatorId
   * Other options:
   * siret, siren, vat, duns, ein, liccn, tps, tvq, qst, gst, cbs, crn, uen, lei, isin, eori, ticker-symbol,
   * uei, houjin-bangou, vkn, tpe, cuit, cnpj, cusip, cui, cuin, uscc, ubn, cro, umsatzsteuer-id,
   * abn, roc, psrn, brn, npwp, cin, fei, bn, krs, mwst, nzbn, rfc, usdot, cvr, rut, nit, nif, cif,
   * kvk, mersis, gemi, uid, rbe, frn, org-nr, operatorId
   */
  type:
  |'operatorId'
  | 'cif'
  | 'siret'
  | 'siren'
  | 'vat'
  | 'duns'
  | 'ein'
  | 'liccn'
  | 'tps'
  | 'tvq'
  | 'qst'
  | 'gst'
  | 'cbs'
  | 'crn'
  | 'uen'
  | 'lei'
  | 'isin'
  | 'eori'
  | 'ticker-symbol'
  | 'uei'
  | 'houjin-bangou'
  | 'vkn'
  | 'tpe'
  | 'cuit'
  | 'cnpj'
  | 'cusip'
  | 'cui'
  | 'cuin'
  | 'uscc'
  | 'ubn'
  | 'cro'
  | 'umsatzsteuer-id'
  | 'abn'
  | 'roc'
  | 'psrn'
  | 'brn'
  | 'npwp'
  | 'cin'
  | 'fei'
  | 'bn'
  | 'krs'
  | 'mwst'
  | 'nzbn'
  | 'rfc'
  | 'usdot'
  | 'cvr'
  | 'rut'
  | 'nit'
  | 'nif'
  | 'kvk'
  | 'mersis'
  | 'gemi'
  | 'uid'
  | 'rbe'
  | 'frn'
  | 'org-nr';
  value: string;
}

export enum GreenlyRegulatoryMethodology {
  GHGProtocol = 'GHGProtocol',
  BEGESv5 = 'BEGESv5',
  BEGESv4 = 'BEGESv4',
}

export enum OwnershipType {
  INVESTMENT = 'INVESTMENT',
  PARTNERSHIP = 'PARTNERSHIP',
  SUBSIDIARY = 'SUBSIDIARY',
}

export type ValidityStatus = 'DEMO' | 'TEST' | 'CLIENT';

interface OwnedCompany {
  id: string;
  createdAt: string;
  lastUpdatedAt: string;
  ownedByCompanyId: string;
  ownedCompanyId: string;
  ownershipPercentage: number;
  ownershipType: OwnershipType;
}

export interface GreenlyCompany {
  id: string
  createdAt: string
  lastUpdatedAt: string
  industry?: string
  companyName: string
  companyLanguage: string
  /**
   * ISO 3166-1 alpha-2
   */
  countryId: string
  type: 'freemium' | 'standalone'
  companyLogoUrl?: string
  settings: Settings
  identifier?: CompanyIdentifier
  organisationBoundaries?: string
  stringifiedFirstFiscalMonth: string
  fiscalYearIsYearOfFirstMonth: boolean
  defaultRegulatoryMethodology: GreenlyRegulatoryMethodology;
  parentIndustry?: string
  hasAcceptedToShareCarbonData: boolean
  hasAccessToSupplierQuestionnaire: boolean
  companyAccountOwnerId?: string
  invitedByCompanyId?: string
  invitedByUserId?: string
  matchedSupplierId: string
  isParentCompany: boolean
  isChildCompany: boolean
  groupCompanyId: string
  dashboardIntegrationId: unknown;
  validityStatus: ValidityStatus
  managedByExternalConsultant: boolean
  mfaEnabled: boolean
  likelyLanguage: string
  firstFiscalMonth: string
  profiles: Profile[]
  ownedCompanyHasCompanyList: OwnedCompany[]
  groupCompany: GroupCompany
}

/**
 * CreateGreenlyCompany
 *
  {
    "companyName": "Brian New OWN CT - Staging",
    "companyLogoUrl": "https://cdn.g17.eco/initiative/647d487fd05f2f18046b73d0.jpeg?8FmnffH6NoCfw0oxzGYLyQ==",
    "companyLanguage": "en"
    "year": "2024",
    "validityStatus": "DEMO",
    "type": "standalone",
    "industry": "ExtractionAndTransformationOfEnergyResources",
    "organisationBoundaries": "Description of the organisation boundaries",
    "identifier": { "value": "647d487fd05f2f18046b73d0", "type": "operatorId" },
    "headOfficeCountry": "CH",
    "firstFiscalMonth": "JANUARY",
    "defaultRegulatoryMethodology": "GHGProtocol",
    "countryId": "FR",

    // Don't need these
    "managedByExternalConsultant": false,
    "hasAcceptedToShareCarbonData": true,
    "fiscalYearIsYearOfFirstMonth": true,
  }
 */
export type CreateGreenlyCompany = Pick<GreenlyCompany,
'companyName' |
'companyLogoUrl' |
'companyLanguage' |
'validityStatus' |
'identifier' |
'type' |
'industry' |
'organisationBoundaries' |
'fiscalYearIsYearOfFirstMonth' |
'firstFiscalMonth' |
'defaultRegulatoryMethodology' |
'parentIndustry'
> & {
  /**
   * Year of the company "profile" to create. Each year it needs a new profile.
   */
  year: string;
  /** ISO 3166-1 alpha-2 */
  headOfficeCountry?: string;
  /** ISO 3166-1 alpha-2 */
  countryId?: string;
};

export type GreenlyCompanyMinimal = Pick<GreenlyCompany, 'id' | 'companyName' | 'identifier'>;

export interface Settings {
  notifications: Notifications
}

export interface Notifications {
  enableTaskNotifications: boolean
}

export interface Profile {
  id: string
  createdAt: string
  lastUpdatedAt: string
  companyId: string
  year: string
  startDate: string
  endDate: string
  revenue: number
  revenueCurrency: string
  employeesNumber: number
  isActive: boolean
  regulatoryMethodology: string
  hasInvolvedSuppliersInPercent: number
  suppliersContactFileURL: any
  supplierImportFromExpenseDataUrl: any
  supplierImportFromManualInputUrl: any
  supplierExpenseDataImportUrlFr: any
  supplierManualImportUrlFr: any
  hasAskedToScrapSuppliers: boolean
  willDuplicateSuppliersLater: boolean
  reportIsReady: boolean
  reportPrecisionInPercent: number
  isFrozen: boolean
  hasCommunicatedInternally: boolean
  hasCommunicatedInternallyOnResults: boolean
  hasDisclosedEmissions: boolean
  hasActivatedClimateTraining: boolean
  hasPublishedHisReportOnRegulatoryAgency: boolean
  hasContributedOnScopeOneAndTwoInPercent: number
  hasContributedOnScopeThreeInPercent: number
  actionsPlanProgressionInPercent: number
  hasPurchasedFresqueDuClimatTraining: boolean
  hasNoModules: any
  offsetAmountInTons: number
  offsetAmountInEuros: number
  createdFromDuplication: boolean
  questionnaireVersion: string
  skipCSRQuestionsOfEmployeeQuestionnaire: boolean
  reductionIsAbsolute: boolean
  reductionInVolumePerYear: number
  hasObjectivesInScopeThree: boolean
  hasCommunicatedClimateObjectives: boolean
  satisfactionSurveyInfo: any[]
  unaccountedActivitySubTypes: UnaccountedActivitySubTypes
  companyVertical?: string
  isPaymentOverdue: boolean
  electricityReferentials: any
  clientScoreVersion: string
  supplierTransactionIdentificationGoalMetric: any
  accountingInternalLogs: any
  useMaterializedExportableActivity: any
  sbtiSector: any
  sbtiTargetYear: any
  sbtiSubmissionYear: any
  products: any[]
}

export interface UnaccountedActivitySubTypes {
  employeeActivities: string[]
}

export interface GroupCompany {
  id: string
  createdAt: string
  lastUpdatedAt: string
  industry: string
  companyName: string
  companyLanguage: string
  countryId: string
  type: string
  companyLogoUrl: string
  settings: Settings2
  identifier: any
  organisationBoundaries: string
  stringifiedFirstFiscalMonth: string
  fiscalYearIsYearOfFirstMonth: boolean
  defaultRegulatoryMethodology: string
  parentIndustry: string
  hasAcceptedToShareCarbonData: boolean
  hasAccessToSupplierQuestionnaire: boolean
  companyAccountOwnerId: any
  invitedByCompanyId: any
  invitedByUserId: any
  matchedSupplierId: string
  isParentCompany: boolean
  isChildCompany: boolean
  groupCompanyId: string
  dashboardIntegrationId: any
  validityStatus: string
  managedByExternalConsultant: boolean
  mfaEnabled: boolean
  name: string
  likelyLanguage: string
  firstFiscalMonth: string
}

export interface Settings2 {
  notifications: Notifications2
}

export interface Notifications2 {
  enableTaskNotifications: boolean
}


export interface ServiceAccountMeReponse {
  id: string
  createdAt: string
  lastUpdatedAt: string
  email: string
  firstName: string
  lastName: string
  phoneNumber: any
  jobTitle: string
  dateOfConsentTermsAndConditions: string
  password: any
  role: string
  supplierInvitationId: any
  meetingLinks: any
  lastConnectedAt: any
  lastSeenAt: any
  lastRepliedAt: any
  lastContactedAt: any
  lastEmailOpenedAt: any
  lastEmailClickedAt: any
  name: string
  isGreenlyPro: boolean
  companies: GreenlyCompany[]
}

export interface OwnedCompaniesPayload {
  ownedCompanies: {
    ownershipType: OwnershipType;
    /**
     * 0-100
     */
    ownershipPercentage: number;
    /**
     * Greenly company ID (UUID)
     */
    ownedCompanyId: string;
  }[];
}



export enum GreenlyUserRole {
  ClimateExpert = 'ClimateExpert',
  ExternalOps = 'ExternalOps',
  ClientExternalOps = 'ClientExternalOps',
  ProjectManager = 'ProjectManager',
  SensitiveDataContributor = 'SensitiveDataContributor',
  Contributor = 'Contributor',
  Auditor = 'Auditor',
}

/**
 *   {
    "meetingLinks": {
      "csrd": "string",
      "default": "string"
    },
    "phoneNumber": "string",
    "role": "ClimateExpert",
    "supplierInvitationId": "788bbbFE-3EFf-4a63-9cf2-EFF2ECeDcE61",
    "jobTitle": "string",
    "lastName": "string",
    "firstName": "string",
    "companyIds": [
      "8BeD0ABc-2Ac7-4A36-B0ca-BF264F5EceEc"
    ],
    "email": "string"
  }
 */
export interface CreateUser {
  email: string;
  role: GreenlyUserRole;
  companyIds: string[];

  // Optional fields
  firstName?: string;
  lastName?: string;
  jobTitle?: string;

  // Other fields that are not used for now
  meetingLinks?: {
    csrd?: string;
    default?: string;
  };
  phoneNumber?: string;
  supplierInvitationId?: string;
}

export interface GreenlyUser extends CreateUser {
  id: string;
  createdAt: string;
  lastUpdatedAt: string;
}

export type OwnedCompanyResponse = {
  idsOfCompanyAdded: string[];
  numberOfCompanyAdded: number;
};
