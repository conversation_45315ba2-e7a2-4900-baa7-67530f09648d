import ContextError from '../../../error/ContextError';
import { type GreenlyCompany, type OwnedCompaniesPayload, OwnershipType, type SetupCompany, type ValidityStatus } from './greenlyTypes';


type MinimalInitiative = Pick<SetupCompany, 'initiativeId' | 'name' | 'parentInitiativeId' | 'externalId'>;
type MinimalGreenlyCompany = Pick<GreenlyCompany, 'id' | 'identifier' | 'companyName'>;

export interface MapOwnedCompaniesParams {
  // Platform root initiative
  rootInitiative: MinimalInitiative;
  // Initiative tree of children companies
  initiativeTree?: MinimalInitiative[];

  // Greenly company
  greenlyCompany: MinimalGreenlyCompany;
  // Children companies
  childrenCompanies: MinimalGreenlyCompany[];
}

interface BatchedOwnedCompanies extends OwnedCompaniesPayload {
  greenlyCompany: Pick<GreenlyCompany, 'id' | 'companyName'>;
}

/**
 * Create batches of owned companies to be created in one API call
 * Create a tree structure first and navigate breadth first to create batches of parent direct children
 * Then navigate to the next level and create batches of parent direct children
 * Continue until all companies are batched
 * Then create the owned companies in the API call
 *
 * NOTE: InitiativeTree can contain more children than the childrenCompanies array,
 * as only newly created companies are included in the childrenCompanies array.
 * Therefore we navigate the tree and create batches, but some might be skipped if there are no children create on the Greenly side.
 */
export function createGroupedBatches(data: MapOwnedCompaniesParams): BatchedOwnedCompanies[] {
  const { greenlyCompany, childrenCompanies, rootInitiative, initiativeTree } = data;

  if (!initiativeTree) {
    return []; // Nothing we can do here
  }

  const batches: BatchedOwnedCompanies[] = [];

  // Create a map of initiative IDs to Greenly companies for quick lookup
  const greenlyCompanyMap = new Map(
    [greenlyCompany, ...childrenCompanies]
      .filter(company => company.identifier?.value)
      .map(company => [company.identifier!.value, company])
  );

  // Create a map of initiative IDs to their children for tree traversal
  const initiativeChildrenMap = new Map<string, MinimalInitiative[]>();

  // Build the tree structure, start from root and add all children
  initiativeTree.forEach(initiative => {
    // Either parent or must be root
    const parentId = initiative.parentInitiativeId;
    if (!parentId) {
      return; // Must be root, skip
    }

    const parentGroup = initiativeChildrenMap.get(parentId);
    if (!parentGroup) {
      initiativeChildrenMap.set(parentId, [initiative]);
      return;
    }

    parentGroup.push(initiative);
  });

  // Queue for breadth-first traversal
  const queue: { initiative: MinimalInitiative; level: number }[] = [
    { initiative: rootInitiative, level: 0 }
  ];

  // Process each level
  while (queue.length > 0) {
    const { initiative, level } = queue.shift()!;
    const children = initiativeChildrenMap.get(initiative.initiativeId) || [];

    // Find all Greenly companies that correspond to this level's initiatives
    const greenlyLevelCompanies = children.reduce((acc, child) => {
      const childGreenlyCompany = greenlyCompanyMap.get(child.initiativeId);
      if (childGreenlyCompany) {
        // If the child is not the same as the greenly company, add it to the batch
        acc.push(childGreenlyCompany);
      }
      return acc;
    }, [] as MinimalGreenlyCompany[]);

    if (greenlyLevelCompanies.length > 0) {

      // If we do not have parent company, look it up from the initiative tree
      let greenlyParentCompany = greenlyCompanyMap.get(initiative.initiativeId);
      if (!greenlyParentCompany && initiative.externalId) {
        // Otherwise we ca-reuse the externalId already associated with initiative.
        greenlyParentCompany = { id: initiative.externalId, companyName: initiative.name };
      }

      if (!greenlyParentCompany) {
        throw new ContextError(`Parent Greenly company not found for initiative ${initiative.initiativeId}`, {
          initiativeName: initiative.name,
          initiativeId: initiative.initiativeId,
          greenlyCompanyId: greenlyCompany.id,
          debugMessage: 'No company to associated greenly children companies to'
        });
      }

      // Create a batch for this level
      batches.push({
        greenlyCompany: { id: greenlyParentCompany.id, companyName: greenlyParentCompany.companyName },
        ownedCompanies: greenlyLevelCompanies.map(company => ({
          ownedCompanyId: company.id,
          ownershipType: OwnershipType.SUBSIDIARY,
          ownershipPercentage: 100 // Default to 100% ownership
        }))
      });
    }

    // Add children to queue for next level
    children.forEach(child => {
      queue.push({ initiative: child, level: level + 1 });
    });
  }

  return batches;
}


export function ensureValidityStatus(status: string): ValidityStatus {
  if (['DEMO', 'TEST', 'CLIENT'].includes(status)) {
    return status as ValidityStatus;
  }

  throw new ContextError(`Invalid Greenly validity status: ${status}`, {
    debugMessage: 'Validity status must be one of: DEMO, TEST, CLIENT'
  });
}
