import { ObjectId } from 'bson';
import { HistoricalUtrs, UtrvFilter } from '../../../models/insightDashboard';
import { SurveyType } from '../../../models/survey';
import { DateRange } from '../../../util/date';
import { DataPeriods } from '../constants';
import { InitiativeRepository } from '../../../repository/InitiativeRepository';
import UniversalTrackerValueHistoryService from '../UniversalTrackerValueHistoryService';
import { InitiativePlain } from '../../../models/initiative';

export interface UtrvFilters {
  isCompletedData: boolean;
  assuredOnly: boolean;
  isPublicOnly: boolean;
  showBaselines: boolean;
  showTargets: boolean;
  period: DataPeriods | undefined;
  dateRange: DateRange | undefined;
  surveyType: SurveyType | undefined;
  status?: UtrvFilter;
}

export class UniversalTrackerHistoricalDataManager {
  public async getUtrsHistoricalData({
    initiativeId,
    utrIds,
    utrvFilters,
  }: {
    initiativeId: ObjectId | string;
    utrIds: string[];
    utrvFilters: UtrvFilters;
  }): Promise<HistoricalUtrs[]> {
    const {
      isCompletedData,
      assuredOnly,
      isPublicOnly,
      showBaselines,
      showTargets,
      period,
      dateRange,
      surveyType,
      status,
    } = utrvFilters;

    const utrService = await UniversalTrackerValueHistoryService.create({
      initiativeId: initiativeId.toString(),
      utrIds,
      isCompletedData,
      assuredOnly,
      isPublicOnly,
      period,
      dateRange,
      surveyType,
      status,
    });

    // Start from UTR, in case we not finding, returning empty utrvs without utr does not make sense
    const utrsData = await utrService.getHistoricalUtrData({
      isCompletedData,
      assuredOnly,
      showTargets,
      showBaselines,
    });

    return utrsData;
  }

  private async getValidInitiativesMap({
    initiativeIds,
    initiativeId,
  }: {
    initiativeIds: ObjectId[];
    initiativeId: ObjectId | string;
  }) {
    const children = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { _id: 1, name: 1 });
    const initiativeIdsSet = new Set(initiativeIds.map((id) => id.toString()));
    return children.reduce((acc, initiative) => {
      const idString = initiative._id.toString();
      if (initiativeIdsSet.has(idString)) {
        acc.set(idString, initiative);
      }
      return acc;
    }, new Map<string, InitiativePlain>());
  }

  public async getSubsidiariesUtrsData({
    initiativeId,
    initiativeIds,
    utrIds,
    utrvFilters,
  }: {
    initiativeIds: ObjectId[];
    initiativeId: ObjectId | string;
    utrIds: string[];
    utrvFilters: UtrvFilters;
  }): Promise<HistoricalUtrs[]> {
    if (initiativeIds.length === 0) {
      return [];
    }
    // If dashboard is inherited, then the initiativeIds must be filtered
    // to only include the current initiative and children
    const validInitiativesMap = await this.getValidInitiativesMap({
      initiativeIds,
      initiativeId,
    });

    const { isCompletedData, assuredOnly, isPublicOnly, showBaselines, showTargets, period, dateRange, surveyType } =
      utrvFilters;

    // Create a map with utrId as key, historical data as value
    const historicalDataMap = new Map<string, HistoricalUtrs>();

    for (const [initiativeId, initiative] of validInitiativesMap) {
      const utrService = await UniversalTrackerValueHistoryService.create({
        initiativeId,
        utrIds,
        isCompletedData,
        assuredOnly,
        isPublicOnly,
        period,
        dateRange,
        surveyType,
      });
      const utrsData = await utrService.getHistoricalUtrData({
        isCompletedData,
        assuredOnly,
        showTargets,
        showBaselines,
      });

      for (const utrData of utrsData) {
        const { utr, utrvs } = utrData;
        const key = utr._id.toString();
        const data = historicalDataMap.get(key);
        const mappedUtrvs = utrvs.map((utrv) => ({ ...utrv, initiative }));
        if (data) {
          data.utrvs.push(...mappedUtrvs);
          continue;
        }
        historicalDataMap.set(key, { utr, utrvs: mappedUtrvs });
      }
    }

    return Array.from(historicalDataMap.values());
  }
}

let instance: UniversalTrackerHistoricalDataManager;
export const getUniversalTrackerHistoricalDataManager = () => {
  if (!instance) {
    instance = new UniversalTrackerHistoricalDataManager();
  }

  return instance;
};
