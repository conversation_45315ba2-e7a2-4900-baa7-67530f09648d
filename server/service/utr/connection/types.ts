import { Calculation, CalculationGroupPlain, CalculationGroupValueType } from '../../../models/calculationGroup';
import { NumberScale } from '../../../service/units/unitTypes';
import { UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import { ObjectId } from 'bson';
import { UniversalTrackerPlain } from '../../../models/universalTracker';
import { GeneratedUtr } from '../../../service/integration/IntegrationProvider';
import { IntegrationProvider } from '../../../service/integration/IntegrationProvider';
import { SurveyModelPlain } from '../../../models/survey';

export type CalculationVariableGroup = {
  name: string;
  colour?: string;
  link?: string;
  icon?: string;
};

export type UnitNumberScale = { unit?: string; numberScale: string };
export const DEFAULT_UNIT_NUMBER_SCALE: UnitNumberScale = { numberScale: NumberScale.Single };
export type VariablesData = Record<string, { value: number; utrvId?: ObjectId } & UnitNumberScale>;
export type NumericData = {
  value: number;
  variables: VariablesData;
};
export type NumericCalculation = Calculation &
  /** unit and numberScale only exist in numeric valueType */
  UnitNumberScale & {
    data: (NumericData & { surveyId?: string })[];
    /** group is an extra information for integrated utrs */
    group?: CalculationVariableGroup;
  };
export type NumericCalculationGroup = Omit<CalculationGroupPlain, 'calculations' | 'valueType'> & {
  calculations: NumericCalculation[];
  valueType: CalculationGroupValueType.Numeric;
};

export type TextData = {
  value: string;
  variables: Record<string, { value: string; utrvId?: ObjectId }>;
};
export type TextCalculation = Calculation & {
  data: (TextData & { surveyId?: string })[];
  /** group is an extra information for integrated utrs */
  group?: CalculationVariableGroup;
};
export type TextCalculationGroup = Omit<CalculationGroupPlain, 'calculations' | 'valueType'> & {
  calculations: TextCalculation[];
  valueType: CalculationGroupValueType.Text;
};
export type PopulatedCalculationGroup = NumericCalculationGroup | TextCalculationGroup;

export type CalculationUtrv = { _id?: ObjectId } & Pick<UniversalTrackerValuePlain, 'valueType' | 'valueData'> &
  Partial<Pick<UniversalTrackerValuePlain, 'value'>>;
export type Utrv = CalculationUtrv & Pick<UniversalTrackerValuePlain, 'universalTrackerId' | 'compositeData'>;

export type UtrCodeUtrvMap = Map<string, Utrv>;
export type UtrCodeCalculationUtrvMap = Map<string, CalculationUtrv>;
export type UtrCodeUtrMap = Map<
  string,
  Pick<Utr | IntegrationUtr, 'valueType' | 'unit' | 'numberScale' | 'valueValidation'>
>;

export type Utr = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'code'
  | 'valueValidation'
  | 'type'
  | 'ownerId'
  | 'valueType'
  | 'unit'
  | 'numberScale'
  | 'name'
  | 'alternatives'
>;
export type IntegrationUtr = GeneratedUtr & { provider?: Pick<IntegrationProvider, 'shortName' | 'logo' | 'icon'> };
export type Survey = Pick<SurveyModelPlain, '_id' | 'period' | 'effectiveDate' | 'initiativeId' | 'name' | 'scope'>;
