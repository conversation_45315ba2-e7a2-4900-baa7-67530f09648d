import { MetricGroupMin } from '../../../repository/MetricGroupRepository';
import {
  Calculation,
  CalculationGroupPlain,
  CalculationGroupValueType,
  CalculationType,
} from '../../../models/calculationGroup';
import { UtrValueType } from '../../../models/public/universalTrackerType';
import { MetricUnitManager } from '../../../service/units/MetricUnitManager';
import { tryCalculation } from '../../../rules/calculation/formula';
import {
  NumericData,
  CalculationUtrv,
  CalculationVariableGroup,
  DEFAULT_UNIT_NUMBER_SCALE,
  IntegrationUtr,
  NumericCalculation,
  NumericCalculationGroup,
  TextCalculation,
  TextCalculationGroup,
  TextData,
  UnitNumberScale,
  Utr,
  UtrCodeCalculationUtrvMap,
  UtrCodeUtrMap,
  UtrCodeUtrvMap,
  VariablesData,
} from './types';

export interface ConnectionDataResolverParams {
  calculationGroups: CalculationGroupPlain[];
  utrCodeToUtrMap: UtrCodeUtrMap;
  integrationUtrCodeUtrvMap: UtrCodeCalculationUtrvMap;
  surveyIdToUtrCodeUtrvMap: Map<string, UtrCodeUtrvMap>;
  utrCodeToGroupMap: Map<string, MetricGroupMin>;
}

export class ConnectionDataResolver {
  private calculationGroups: CalculationGroupPlain[] = [];
  private utrCodeToUtrMap: UtrCodeUtrMap = new Map();
  private integrationUtrCodeUtrvMap: UtrCodeCalculationUtrvMap = new Map();
  private surveyIdToUtrCodeUtrvMap: Map<string, UtrCodeUtrvMap> = new Map();
  private utrCodeToGroupMap: Map<string, MetricGroupMin> = new Map();
  private surveyIds: string[] = [];

  constructor(params: ConnectionDataResolverParams) {
    this.calculationGroups = params.calculationGroups;
    this.utrCodeToUtrMap = params.utrCodeToUtrMap;
    this.integrationUtrCodeUtrvMap = params.integrationUtrCodeUtrvMap;
    this.surveyIdToUtrCodeUtrvMap = params.surveyIdToUtrCodeUtrvMap;
    this.utrCodeToGroupMap = params.utrCodeToGroupMap;
  }

  public getSurveyIds() {
    return this.surveyIds;
  }

  public populateConnectionsData() {
    return this.calculationGroups.reduce((acc, group) => {
      const groupValueType = group.valueType ?? CalculationGroupValueType.Numeric;

      if (groupValueType === CalculationGroupValueType.Text) {
        acc.push(this.populateTextConnection(group));
      }

      if (groupValueType === CalculationGroupValueType.Numeric) {
        acc.push(this.populateNumericConnection(group));
      }
      return acc;
    }, [] as (NumericCalculationGroup | TextCalculationGroup)[]);
  }

  private populateNumericConnection(group: CalculationGroupPlain): NumericCalculationGroup {
    return {
      ...group,
      valueType: CalculationGroupValueType.Numeric,
      calculations: group.calculations.map((calculation) => {
        const calculationUnitNumberScale = this.getCalculationUnitNumberScale(calculation);
        const isIntegrated = Object.values(calculation.variables).some((variable) => Boolean(variable.integrationCode));

        return {
          ...calculation,
          // Haven't supported for mix integration and survey variables yet so data is from survey or integration.
          data: isIntegrated
            ? this.getCalculationIntegratedData({
                calculation,
                unitNumberScale: calculationUnitNumberScale,
              })
            : this.getCalculationNumericData({ calculation, unitNumberScale: calculationUnitNumberScale }),
          ...calculationUnitNumberScale,
          group: this.resolveGroup(calculation),
        };
      }),
    };
  }

  private populateTextConnection(group: CalculationGroupPlain): TextCalculationGroup {
    return {
      ...group,
      valueType: CalculationGroupValueType.Text,
      calculations: group.calculations.map((calculation) => {
        return {
          ...calculation,
          data: this.getCalculationTextData(calculation),
          group: this.resolveGroup(calculation),
        };
      }),
    };
  }

  private getCalculationNumericData({
    calculation,
    unitNumberScale,
  }: {
    calculation: Calculation;
    unitNumberScale: UnitNumberScale;
  }) {
    return Array.from(this.surveyIdToUtrCodeUtrvMap).reduce((data, [surveyId, utrCodeUtrvMap]) => {
      const calculatedData = this.getCalculatedNumberData({
        calculation,
        utrCodeUtrvMap,
        unitNumberScale: unitNumberScale,
      });

      // Return calculatedData that have value and surveys that have at least one calculation only
      if (calculatedData?.value) {
        data.push({ ...calculatedData, surveyId });
        this.surveyIds.push(surveyId);
      }

      return data;
    }, [] as NumericCalculation['data']);
  }

  private getCalculatedNumberData({
    calculation,
    utrCodeUtrvMap,
    unitNumberScale,
  }: {
    calculation: Calculation;
    utrCodeUtrvMap: UtrCodeCalculationUtrvMap;
    unitNumberScale: UnitNumberScale;
  }): NumericData | undefined {
    switch (calculation.type) {
      case CalculationType.Direct: {
        const direct = calculation.direct;
        const variable = calculation.variables[direct];

        if (!variable) {
          return;
        }
        const utrv = utrCodeUtrvMap.get(variable.code);
        if (!utrv) {
          return;
        }

        const value = this.getNumericValue(utrv, variable.valueListCode);
        if (!value) {
          return;
        }

        return {
          value,
          variables: {
            [direct]: {
              value,
              utrvId: utrv._id,
              ...unitNumberScale,
            },
          },
        };
      }
      case CalculationType.Formula: {
        const variablesData = this.getVariablesData({
          variables: calculation.variables,
          utrCodeUtrvMap,
        });
        const variablesWithValues = Object.keys(variablesData).reduce((acc, variable) => {
          const variableData = variablesData[variable];
          acc[variable] = MetricUnitManager.convertUnitNumberScale({
            value: variableData.value,
            from: { unit: variableData.unit, numberScale: variableData.numberScale },
            to: { unit: unitNumberScale?.unit, numberScale: unitNumberScale.numberScale },
          });
          return acc;
        }, {} as { [key: string]: number });

        const value = tryCalculation({ formula: calculation.formula, variables: variablesWithValues });
        if (typeof value !== 'number') {
          return;
        }

        return { value, variables: variablesData };
      }
      case CalculationType.Stages: {
        // Not really understood how it works, to add logic later.
        return;
      }
      default: {
        return;
      }
    }
  }

  private getCalculationUnitNumberScale(calculation: Calculation) {
    switch (calculation.type) {
      case CalculationType.Direct: {
        const direct = calculation.direct;
        const variable = calculation.variables[direct];
        if (!variable) {
          return DEFAULT_UNIT_NUMBER_SCALE;
        }
        const utr = this.utrCodeToUtrMap.get(variable.code);
        if (!utr) {
          return DEFAULT_UNIT_NUMBER_SCALE;
        }
        return this.getDataPointUnitNumberScale({ utr, columnCode: variable.valueListCode });
      }
      case CalculationType.Formula: {
        // Simply use the unit and numberScale of first variable. Value of calculation will be calculated based on that.
        const firstVariable = Object.values(calculation.variables)[0];
        if (!firstVariable) {
          return DEFAULT_UNIT_NUMBER_SCALE;
        }

        const utr = this.utrCodeToUtrMap.get(firstVariable.code);
        if (!utr) {
          return DEFAULT_UNIT_NUMBER_SCALE;
        }
        return this.getDataPointUnitNumberScale({ utr, columnCode: firstVariable.valueListCode });
      }
      case CalculationType.Stages:
      default:
        return DEFAULT_UNIT_NUMBER_SCALE;
    }
  }

  public resolveGroup(calculation: Calculation): CalculationVariableGroup | undefined {
    if (calculation.type !== CalculationType.Direct) {
      return undefined;
    }

    const variable = calculation.variables[calculation.direct];
    if (!variable) {
      return undefined;
    }

    const group = this.utrCodeToGroupMap.get(variable.code);
    if (!group) {
      return undefined;
    }

    return {
      name: group.groupName,
      colour: group.groupData.colour,
      link: group.groupData.link,
      icon: group.groupData.icon,
    };
  }

  public getDataPointUnitNumberScale({
    utr,
    columnCode,
  }: {
    utr: Pick<Utr | IntegrationUtr, 'valueType' | 'unit' | 'numberScale' | 'valueValidation'>;
    columnCode?: string;
  }) {
    switch (utr.valueType) {
      case UtrValueType.Table: {
        const tableData = utr.valueValidation?.table;
        if (!columnCode || !tableData) {
          return DEFAULT_UNIT_NUMBER_SCALE;
        }

        const column = tableData.columns.find((col) => col.code === columnCode);
        return {
          unit: column?.unit,
          numberScale: column?.numberScale ?? DEFAULT_UNIT_NUMBER_SCALE.numberScale,
        };
      }
      case UtrValueType.NumericValueList:
      case UtrValueType.ValueList:
      case UtrValueType.Number:
      case UtrValueType.Percentage:
      default:
        return {
          unit: utr.unit,
          numberScale: utr.numberScale ?? DEFAULT_UNIT_NUMBER_SCALE.numberScale,
        };
    }
  }

  private getVariablesData({
    variables,
    utrCodeUtrvMap,
  }: {
    variables: Calculation['variables'];
    utrCodeUtrvMap: UtrCodeCalculationUtrvMap;
  }) {
    return Object.keys(variables).reduce((variablesData, variable) => {
      const { code, valueListCode } = variables[variable];
      const utrv = utrCodeUtrvMap.get(code);

      if (!utrv) {
        return variablesData;
      }

      const utr = this.utrCodeToUtrMap.get(code);
      if (!utr) {
        return variablesData;
      }

      const { unit, numberScale } = this.getDataPointUnitNumberScale({ utr, columnCode: valueListCode });
      variablesData[variable] = {
        value: this.getNumericValue(utrv, valueListCode),
        utrvId: utrv._id,
        unit,
        numberScale,
      };
      return variablesData;
    }, {} as VariablesData);
  }

  public getNumericValue(universalTrackerValue: CalculationUtrv, columnCode?: string) {
    switch (universalTrackerValue.valueType) {
      case UtrValueType.Table: {
        const tableData = universalTrackerValue.valueData?.table;
        // TODO: should handle the case when data is undefined rather then return 0
        if (!columnCode || !tableData) {
          return 0;
        }

        return tableData.reduce((sum, row) => {
          const columnData = row.find((col) => col.code === columnCode);
          return sum + Number(columnData?.value ?? 0);
        }, 0);
      }
      case UtrValueType.NumericValueList: {
        if (!columnCode || columnCode === 'total') {
          return Number(universalTrackerValue.value ?? 0);
        }
        return Number(universalTrackerValue.valueData?.data?.[columnCode] ?? 0);
      }
      case UtrValueType.ValueList:
      case UtrValueType.Number:
      case UtrValueType.Percentage:
      default:
        return Number(universalTrackerValue.value ?? 0);
    }
  }

  private getCalculationTextData(calculation: Calculation) {
    return Array.from(this.surveyIdToUtrCodeUtrvMap).reduce((data, [surveyId, utrCodeUtrvMap]) => {
      const textData = this.getTextData({
        calculation,
        utrCodeUtrvMap,
      });

      // Return calculatedData that have value and surveys that have at least one calculation only
      if (textData?.value) {
        data.push({ ...textData, surveyId });
        this.surveyIds.push(surveyId);
      }

      return data;
    }, [] as TextCalculation['data']);
  }

  public getTextData({
    calculation,
    utrCodeUtrvMap,
  }: {
    calculation: Calculation;
    utrCodeUtrvMap: UtrCodeCalculationUtrvMap;
  }): TextData | undefined {
    switch (calculation.type) {
      case CalculationType.Direct: {
        const direct = calculation.direct;
        const variable = calculation.variables[direct];

        if (!variable) {
          return;
        }
        const utrv = utrCodeUtrvMap.get(variable.code);
        if (!utrv) {
          return;
        }

        const value = this.getTextValue(utrv, variable.valueListCode);
        if (!value) {
          return;
        }

        return {
          value,
          variables: {
            [direct]: {
              value,
              utrvId: utrv._id,
            },
          },
        };
      }
      default: {
        return;
      }
    }
  }

  public getTextValue(utrv: CalculationUtrv, columnCode?: string): string {
    switch (utrv.valueType) {
      case UtrValueType.Table: {
        const tableData = utrv.valueData?.table;
        if (!columnCode || !tableData) {
          return '';
        }

        /** This concatenation works because UTR portal only support single row table for now
         * TODO: support connection of multi row table utrs
         */
        return tableData
          .reduce((acc, row) => {
            const columnData = row.find((col) => col.code === columnCode);
            if (columnData?.value) {
              acc.push(columnData.value);
            }
            return acc;
          }, [])
          .join(' ');
      }
      case UtrValueType.TextValueList: {
        if (!columnCode) {
          return '';
        }
        const columnData = (utrv.valueData?.data[columnCode] ?? '') as string;
        return columnData;
      }
      case UtrValueType.Text:
        return utrv.valueData?.data ?? utrv.valueData?.input?.data;
      default:
        return '';
    }
  }

  private getCalculationIntegratedData({
    calculation,
    unitNumberScale,
  }: {
    calculation: Calculation;
    unitNumberScale: UnitNumberScale;
  }) {
    const integratedData = this.getCalculatedNumberData({
      calculation,
      utrCodeUtrvMap: this.integrationUtrCodeUtrvMap,
      unitNumberScale,
    });

    return integratedData ? [integratedData] : [];
  }
}
