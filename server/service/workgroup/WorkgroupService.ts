import type { ObjectId } from 'bson';
import type { UserWithInfo, WorkgroupPlain } from '../../models/workgroup';
import { Permission, Workgroup } from '../../models/workgroup';
import type { RootInitiativeService } from '../organization/RootInitiativeService';
import { getRootInitiativeService } from '../organization/RootInitiativeService';
import type { UpdateWriteOpResult } from 'mongoose';
import { FeatureCode } from '@g17eco/core';
import type { FeatureUsageService } from '../organization/FeatureUsageService';
import { getFeatureUsageService } from '../organization/FeatureUsageService';
import UserError from '../../error/UserError';
import { type RootInitiativeData } from '../../repository/InitiativeRepository';

type CreateWorkgroupDto = Pick<WorkgroupPlain, 'name' | 'description' | 'icon' | 'color'>;
type CommonParams = { initiativeId: ObjectId; workgroupId: ObjectId; initiative: RootInitiativeData };

export class WorkgroupService {
  constructor(
    private model: typeof Workgroup,
    private rootInitiativeService: RootInitiativeService,
    private featureUsageService: FeatureUsageService
  ) {}

  async getWorkgroups({ initiativeId }: Pick<CommonParams, 'initiativeId'>) {
    const rootInitiative = await this.rootInitiativeService.getOrganizationById(initiativeId);
    return this.model
      .find({ initiativeId: rootInitiative._id })
      .sort({ created: 'desc' })
      .lean<WorkgroupPlain[]>()
      .exec();
  }

  async getWorkgroup({ workgroupId, initiativeId }: Pick<CommonParams, 'workgroupId' | 'initiativeId'>) {
    const [workgroup] = await this.model
      .aggregate<WorkgroupPlain<UserWithInfo>>([
        { $match: { _id: workgroupId, initiativeId } },
        {
          $lookup: {
            from: 'users',
            localField: 'users._id',
            foreignField: '_id',
            as: 'userDetails',
            pipeline: [{ $project: { email: 1, name: { $concat: ['$firstName', ' ', '$surname'] } } }],
          },
        },
        {
          $addFields: {
            users: {
              $map: {
                input: '$users',
                as: 'embeddedUser',
                in: {
                  $mergeObjects: [
                    '$$embeddedUser', // Keep original embedded user (including permissions)
                    {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$userDetails',
                            as: 'user',
                            cond: { $eq: ['$$user._id', '$$embeddedUser._id'] },
                          },
                        },
                        0,
                      ],
                    },
                  ],
                },
              },
            },
          },
        },
        { $project: { userDetails: 0 } }, // Remove temporary userDetails array
      ])
      .exec();

    return workgroup;
  }

  async createWorkgroup({
    data,
    initiative,
    creatorId,
  }: Pick<CommonParams, 'initiative'> & {
    data: CreateWorkgroupDto;
    creatorId: ObjectId;
  }) {
    await this.checkWorkgroupLimit(initiative);
    return this.model.create({ ...data, initiativeId: initiative._id, creatorId, users: [] });
  }

  async updateWorkgroup({
    initiativeId,
    workgroupId,
    data,
  }: Pick<CommonParams, 'initiativeId' | 'workgroupId'> & {
    data: Partial<CreateWorkgroupDto>;
  }) {
    return this.model.updateOne({ _id: workgroupId, initiativeId }, { $set: data }).exec();
  }

  async deleteWorkgroup({ workgroupId, initiativeId }: Pick<CommonParams, 'workgroupId' | 'initiativeId'>) {
    return this.model.deleteOne({ _id: workgroupId, initiativeId }).exec();
  }

  async duplicateWorkgroup({
    workgroupId,
    initiative,
    creatorId,
  }: Pick<CommonParams, 'workgroupId' | 'initiative'> & {
    creatorId: ObjectId;
  }) {
    await this.checkWorkgroupLimit(initiative);
    const { name, description, icon, color, users } = await this.model
      .findOne({ _id: workgroupId, initiativeId: initiative._id })
      .orFail()
      .lean<WorkgroupPlain>()
      .exec();

    return this.model.create({
      name: `Copy of ${name}`,
      description,
      icon,
      color,
      users,
      initiativeId: initiative._id,
      creatorId,
    });
  }

  async addUsersToWorkgroup({
    workgroupId,
    initiativeId,
    userIds,
  }: Pick<CommonParams, 'workgroupId' | 'initiativeId'> & { userIds: ObjectId[] }) {
    const workgroup = await this.model.findOne({ _id: workgroupId, initiativeId }).orFail().exec();

    const newUsers = userIds.filter((id) => !workgroup.users.some((u) => u._id.equals(id)));
    if (newUsers.length === 0) {
      return {
        acknowledged: true,
        modifiedCount: 0,
        matchedCount: 1,
        upsertedCount: 0,
        upsertedId: null,
      } satisfies UpdateWriteOpResult;
    }
    return this.model
      .updateOne(
        { _id: workgroupId, initiativeId },
        { $addToSet: { users: { $each: newUsers.map((id) => ({ _id: id, permissions: [Permission.User] })) } } }
      )
      .exec();
  }

  async removeUserFromWorkgroup({
    workgroupId,
    initiativeId,
    userId,
  }: Pick<CommonParams, 'workgroupId' | 'initiativeId'> & { userId: ObjectId }) {
    return this.model.updateOne({ _id: workgroupId, initiativeId }, { $pull: { users: { _id: userId } } }).exec();
  }

  private async checkWorkgroupLimit(initiative: CommonParams['initiative']) {
    const usage = await this.featureUsageService.getUsage({
      rootInitiative: initiative,
      featureCode: FeatureCode.Workgroups,
    });

    if (usage.currentUsage >= usage.limit) {
      throw new UserError(`You have reached the maximum number of workgroups (${usage.limit}) for your plan.`);
    }
  }
}

let instance: WorkgroupService;
export const getWorkgroupService = () => {
  if (!instance) {
    instance = new WorkgroupService(Workgroup, getRootInitiativeService(), getFeatureUsageService());
  }
  return instance;
};
