import { ObjectId } from 'bson';
import { SurveyModel as SurveyDocument, SurveyPermission, SurveyPermissionType } from '../../models/survey';
import { Workgroup, WorkgroupPlain } from '../../models/workgroup';
import { DataScopeAccess } from '../../models/dataShare';
import { getScopeUtrvsService } from '../survey/ScopeUtrvsService';
import {
  BlueprintContribution,
  BlueprintContributions,
  getBluePrintContribution,
} from '../survey/BlueprintContribution';
import { Blueprints } from '../../survey/blueprints';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { ScopeUtrv } from '../../util/scope-utrv';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import { KeysEnum } from '../../models/public/projectionUtils';
import { ToGetWorkgroupsSurvey } from '../../types/workgroup';

type WorkgroupProjection = Pick<WorkgroupPlain, '_id' | 'name' | 'icon' | 'color' | 'users'>;

const workgroupProjection: KeysEnum<WorkgroupProjection> = {
  _id: 1,
  name: 1,
  icon: 1,
  color: 1,
  users: 1,
};

export type SurveyWorkgroup = WorkgroupProjection & {
  permission: SurveyPermission;
  utrvIds: ObjectId[];
};

export class SurveyWorkgroupService {
  constructor(
    private logger: LoggerInterface,
    private utrvRepository: typeof UniversalTrackerValueRepository,
    private workgroupModel: typeof Workgroup,
    private scopeUtrvsService: ReturnType<typeof getScopeUtrvsService>,
    private blueprintContribution: BlueprintContribution
  ) {}

  /**
   * Get workgroups for a survey and map the utrv ids for each workgroup.
   * If utrvs are not provided, they will be fetched by survey.visibleUtrvs.
   * If workgroupIds are not provided, they will be fetched by survey.permissions.
   * If userIds are provided, only workgroups that contain the users will be returned.
   * These filters are to help get enough data for WorkgroupPermissions checks.
   */
  async getWorkgroups({
    survey,
    utrvs,
    workgroupIds,
    userIds,
  }: {
    survey: ToGetWorkgroupsSurvey;
    utrvs?: ScopeUtrv[];
    workgroupIds?: ObjectId[];
    userIds?: ObjectId[];
  }): Promise<SurveyWorkgroup[]> {
    const surveyWorkgroupIds = (survey.permissions ?? [])
      .filter((permission) => permission.type === SurveyPermissionType.Workgroup)
      .map((permission) => permission.modelId);
    const toFindWorkgroupIds = workgroupIds
      ? workgroupIds.filter((id) => surveyWorkgroupIds.some((surveyWorkgroupId) => surveyWorkgroupId.equals(id)))
      : surveyWorkgroupIds;

    if (!toFindWorkgroupIds.length) {
      return [];
    }

    const [workgroups, contribution, scopeUtrvs] = await Promise.all([
      this.workgroupModel
        .find(
          { _id: { $in: toFindWorkgroupIds }, ...(userIds ? { 'users._id': { $in: userIds } } : {}) },
          workgroupProjection
        )
        .lean<WorkgroupProjection[]>()
        .exec(),
      this.blueprintContribution.getContributions(survey.sourceName as Blueprints),
      utrvs ?? this.utrvRepository.getScopeUtrvs({ utrvIds: survey.visibleUtrvs }),
    ]);

    const workgroupsWithPermission = workgroups.reduce((workgroups, workgroup) => {
      const permission = survey.permissions?.find(
        (permission) => permission.type === SurveyPermissionType.Workgroup && permission.modelId.equals(workgroup._id)
      );

      if (permission) {
        workgroups.push({ ...workgroup, permission, utrvIds: [] });
      }
      return workgroups;
    }, [] as SurveyWorkgroup[]);

    if (workgroupsWithPermission.length > 5) {
      this.logger.error(
        new ContextError('Survey workgroups count is more than 5, can cause performance issues.', {
          initiativeId: survey.initiativeId,
          surveyId: survey._id,
          workgroupCount: workgroupsWithPermission.length,
        })
      );
    }

    return Promise.all(
      workgroupsWithPermission.map(async (workgroup) => {
        return {
          ...workgroup,
          utrvIds: await this.getUtrvIds({
            utrvs: scopeUtrvs,
            contribution,
            workgroup,
          }),
        };
      })
    );
  }

  private async getUtrvIds({
    utrvs,
    contribution,
    workgroup,
  }: {
    workgroup: SurveyWorkgroup;
    utrvs: ScopeUtrv[];
    contribution: BlueprintContributions;
  }): Promise<ObjectId[]> {
    const { scope, access } = workgroup.permission;
    if (access === DataScopeAccess.Full) {
      return utrvs.map((utrv) => utrv._id);
    }

    if (access === DataScopeAccess.None || !scope) {
      return [];
    }

    return (await this.scopeUtrvsService.filterUtrvsByScope({ utrvs, contribution, scope })).map((utrv) => utrv._id);
  }

  async delegateWorkgroups({
    survey,
    workgroupIds,
    permission,
  }: {
    survey: SurveyDocument;
    workgroupIds: ObjectId[];
    permission: Pick<SurveyPermission, 'access' | 'scope' | 'roles'>;
  }) {
    const newPermissions = workgroupIds.reduce<SurveyPermission[]>((permissions, workgroupId) => {
      // Allow delegate workgroup only 1 time.
      if (
        !survey.permissions?.some(
          (permission) => permission.type === SurveyPermissionType.Workgroup && permission.modelId.equals(workgroupId)
        )
      ) {
        permissions.push({ type: SurveyPermissionType.Workgroup, modelId: workgroupId, ...permission });
      }
      return permissions;
    }, []);

    if (survey.permissions) {
      survey.permissions.push(...newPermissions);
    } else {
      survey.permissions = newPermissions;
    }
    await survey.save();

    return survey.permissions;
  }

  async updateWorkgroup({
    survey,
    workgroupId,
    permission,
  }: {
    survey: SurveyDocument;
    workgroupId: ObjectId;
    permission: Pick<SurveyPermission, 'access' | 'scope' | 'roles'>;
  }) {
    if (!survey.permissions) {
      return [];
    }

    survey.permissions = survey.permissions.map((p) => {
      if (p.type === SurveyPermissionType.Workgroup && p.modelId.equals(workgroupId)) {
        return { ...p, ...permission };
      }
      return p;
    });
    await survey.save();

    return survey.permissions;
  }

  async removeWorkgroups({ survey, workgroupIds }: { survey: SurveyDocument; workgroupIds: ObjectId[] }) {
    if (!survey.permissions) {
      return;
    }

    survey.permissions = survey.permissions.filter(
      (permission) =>
        permission.type !== SurveyPermissionType.Workgroup || !workgroupIds.some((id) => id.equals(permission.modelId))
    );
    await survey.save();

    return survey.permissions;
  }
}

let instance: SurveyWorkgroupService;

export const getSurveyWorkgroupService = () => {
  if (!instance) {
    instance = new SurveyWorkgroupService(
      wwgLogger,
      UniversalTrackerValueRepository,
      Workgroup,
      getScopeUtrvsService(),
      getBluePrintContribution()
    );
  }
  return instance;
};
