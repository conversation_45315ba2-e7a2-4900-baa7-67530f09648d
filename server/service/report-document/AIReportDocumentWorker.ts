import ContextError from '../../error/ContextError';
import { JobType } from '../../models/backgroundJob';
import type { JobResult } from '../background-process/types';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import { getAIReportDocumentWorkflow, type AIReportDocumentWorkflow } from './AIReportDocumentWorkflow';


export class AIReportDocumentWorker {
  constructor(private logger: LoggerInterface, protected workflow: AIReportDocumentWorkflow) {}

  canHandle(jobType: JobType): boolean {
    return jobType === JobType.AIReportDocument;
  }

  public async process(jobId: string, options: { retry: boolean }): Promise<JobResult> {
    this.logger.info(`Start processing ${JobType.AIReportDocument}, jobId: ${jobId || '-'}`);
    const job = jobId ? await this.workflow.findJob(jobId) : await this.workflow.findPendingJob();

    if (!this.workflow.isAIReportDocumentJob(job)) {
      throw new ContextError(`Received invalid job ${job._id} type ${job.type}`, {
        jobId: job._id,
        type: job.type,
        created: job.created,
      });
    }

    // Progress job to the next stage.
    return this.workflow.progressJob(job, { ...options, continueOnError: true });
  }
}

let instance: AIReportDocumentWorker;

export function getAIReportDocumentWorker(): AIReportDocumentWorker {
  if (!instance) {
    instance = new AIReportDocumentWorker(wwgLogger, getAIReportDocumentWorkflow());
  }
  return instance;
}
