import ContextError from '../../error/ContextError';
import BackgroundJob, {
  JobType,
  TaskStatus,
  TaskType,
  type BackgroundJobModel,
  type CreateJob,
} from '../../models/backgroundJob';
import type { CreateReportDocument } from '../../models/reportDocument';
import { BackgroundBaseWorkflow, type TaskResult } from '../background-process/BackgroundBaseWorkflow';
import { getBackgroundJobService, type BackgroundJobService } from '../background-process/BackgroundJobService';
import { createLogEntry } from '../jobs';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import type {
  AIReportDocumentJobModel,
  AIReportDocumentTask,
  GenerateReportLexicalStateTask,
  GenerateReportOutlineTask,
} from './types';

export class AIReportDocumentWorkflow extends BackgroundBaseWorkflow<AIReportDocumentJobModel> {
  protected jobType = JobType.AIReportDocument;

  constructor(
    protected logger: LoggerInterface,
    private backgroundJobModel: typeof BackgroundJob,
    private bgJobService: BackgroundJobService
  ) {
    super();
  }

  public async upsertJob(data: CreateReportDocument): Promise<AIReportDocumentJobModel> {
    const { initiativeId, createdBy } = data;
    const existingJob = await this.findProcessingJob(initiativeId);
    if (existingJob) {
      this.logger.info(`${this.jobType} job already exists for initiativeId: ${initiativeId}`, {
        existingJobId: existingJob._id,
      });
      return existingJob;
    }

    this.logger.info(`Creating ${this.jobType} job for initiativeId: ${initiativeId}`);

    const createData: CreateJob = {
      type: this.jobType,
      name: 'Generating Report Document',
      initiativeId,
      userId: createdBy,
      /** @todo: Implement GenerateReportOutlineTask and GenerateReportLexicalStateTask */
      tasks: [],
      logs: [createLogEntry(`${this.jobType} job created for initiative ${initiativeId}`)],
    };

    const job = (await this.backgroundJobModel.create(createData)) as AIReportDocumentJobModel;
    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          cause: e,
        })
      );
    });

    return job;
  }

  public isAIReportDocumentJob(job: BackgroundJobModel): job is AIReportDocumentJobModel {
    return job.type === this.jobType;
  }

  private async generateReportOutline(job: AIReportDocumentJobModel, task: GenerateReportOutlineTask) {
    await this.startTask(job, task);

    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.markModified('tasks');
    return job.save();
  }

  private async generateReportLexicalState(job: AIReportDocumentJobModel, task: GenerateReportLexicalStateTask) {
    await this.startTask(job, task);

    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.markModified('tasks');
    return job.save();
  }

  public async processTask(
    job: AIReportDocumentJobModel,
    task: AIReportDocumentTask
  ): Promise<TaskResult<AIReportDocumentJobModel>> {
    switch (task.type) {
      case TaskType.GenerateReportOutline:
        return {
          job: await this.generateReportOutline(job, task),
          // Allow to go to next job straight away in the same process
          executeNextTask: true,
        };
      case TaskType.GenerateReportLexicalState:
        return {
          job: await this.generateReportLexicalState(job, task),
          executeNextTask: true,
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: AIReportDocumentWorkflow;
export const getAIReportDocumentWorkflow = () => {
  if (!instance) {
    instance = new AIReportDocumentWorkflow(wwgLogger, BackgroundJob, getBackgroundJobService());
  }
  return instance;
};
