import type { ObjectId } from 'bson';
import type { BackgroundJobPlain, JobType, Task, TaskType } from '../../models/backgroundJob';
import type { HydratedDocument } from 'mongoose';
import type { ReportDocumentStatus } from '../../models/reportDocument';
import type { SerializedEditorState } from 'lexical';

export type GenerateReportOutlineTask = Task<{}, TaskType.GenerateReportOutline>;
export type GenerateReportLexicalStateTask = Task<{}, TaskType.GenerateReportLexicalState>;

export type AIReportDocumentTask = GenerateReportOutlineTask | GenerateReportLexicalStateTask;

export type AIReportDocumentJobPlain = Omit<BackgroundJobPlain<AIReportDocumentTask[]>, 'initiativeId'> & {
  type: JobType.AIReportDocument;
  initiativeId: ObjectId;
};

export type AIReportDocumentJobModel = HydratedDocument<AIReportDocumentJobPlain>;

export type InitializeLexicalState = {
  status: ReportDocumentStatus;
  lexicalState?: SerializedEditorState;
}