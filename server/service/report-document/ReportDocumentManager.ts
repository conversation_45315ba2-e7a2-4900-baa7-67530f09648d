/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import ReportDocument, {
  ReportDocumentStatus,
  ReportDocumentTemplate,
  type CreateReportDocument,
  type ReportDocumentPlain,
} from '../../models/reportDocument';
import { ObjectId } from 'bson';
import { getReportingFactory } from '../reporting/ReportingFactory';
import type { UserPlain } from '../../models/user';
import type { SerializedEditorState } from 'lexical';
import { getAIReportDocumentWorkflow } from './AIReportDocumentWorkflow';
import type { InitializeLexicalState } from './types';

export class ReportDocumentManager {
  constructor(
    private readonly logger: LoggerInterface,
    private reportFactory: ReturnType<typeof getReportingFactory>,
    private reportWorkflow: ReturnType<typeof getAIReportDocumentWorkflow>
  ) {}

  public async create(createData: CreateReportDocument) {
    const document = new ReportDocument(createData);
    return document.save();
  }

  public async initializeLexicalStateFromTemplate({
    reportId,
    user,
  }: {
    reportId: string;
    user: UserPlain;
  }): Promise<InitializeLexicalState> {
    const report = await ReportDocument.findById(reportId).orFail().exec();
    if (report.status !== ReportDocumentStatus.Pending) {
      return {
        status: report.status,
      };
    }
    switch (report.config?.template) {
      case ReportDocumentTemplate.Simple: {
        /**
         * Generate the initial lexical from simple template
         */
        const generator = this.reportFactory.getLexicalStateGenerator(report.type);
        const templateLexicalState = await generator.getTemplate({
          initiativeId: report.initiativeId,
          user,
        });
        report.status = ReportDocumentStatus.Generated;
        await report.save();
        return {
          status: report.status,
          lexicalState: templateLexicalState,
        };
      }
      case ReportDocumentTemplate.AiGenerated: {
        /**
         * Create background job to create initial lexical state using AI
         */
        await this.reportWorkflow.upsertJob(report);
        // Job currently doesn't do anything, setting the report status to generated to prevent it from being constantly created
        await ReportDocument.findByIdAndUpdate(reportId, { status: ReportDocumentStatus.Generated }).exec();
        return {
          status: report.status,
          /** @todo: return the lexical state if it is already generated */
        };
      }
      case ReportDocumentTemplate.Blank:
      default: {
        /**
         * No lexical state to generate
         */
        return {
          status: report.status,
        };
      }
    }
  }

  public async get({
    reportId,
    initiativeId,
  }: {
    reportId: string;
    initiativeId: string;
  }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOne({
      _id: new ObjectId(reportId),
      initiativeId: new ObjectId(initiativeId),
    })
      .orFail()
      .lean()
      .exec();
  }

  public async list({ initiativeId }: { initiativeId: string }): Promise<ReportDocumentPlain[]> {
    return ReportDocument.find({
      initiativeId: new ObjectId(initiativeId),
    })
      .lean()
      .exec();
  }

  public async update({
    reportId,
    initiativeId,
    ...payload
  }: {
    reportId: string;
    initiativeId: string;
    [key: string]: any;
  }): Promise<ReportDocumentPlain> {
    return ReportDocument.findOneAndUpdate(
      {
        _id: new ObjectId(reportId),
        initiativeId: new ObjectId(initiativeId),
      },
      {
        $set: payload,
      },
      { new: true }
    )
      .orFail()
      .exec();
  }

  public async deleteReport({ reportId, initiativeId }: { reportId: string; initiativeId: string }) {
    this.logger.warn('Deleting report document', { reportId, initiativeId });

    return ReportDocument.findOneAndDelete({
      initiativeId: new ObjectId(initiativeId),
      _id: new ObjectId(reportId),
    })
      .orFail()
      .exec();
  }

  public async getTemplate({ reportId, user }: { reportId: string; user: UserPlain }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean().exec();

    const generator = this.reportFactory.getLexicalStateGenerator(reportDocument.type);
    return generator.getTemplate({
      initiativeId: reportDocument.initiativeId,
      user,
    });
  }

  public async download({ reportId, editorState }: { reportId: string; editorState: SerializedEditorState }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean().exec();

    const generator = this.reportFactory.getLexicalStateGenerator(reportDocument.type);
    return generator.downloadReport({
      reportDocument,
      editorState,
    });
  }

  public async synchronizeDocumentReport(reportId: string) {
    const reportDocument = await ReportDocument.findById(reportId).orFail().exec();
    if (reportDocument.status !== ReportDocumentStatus.Generated) {
      return reportDocument;
    }

    reportDocument.status = ReportDocumentStatus.Completed;
    await reportDocument.save();

    return reportDocument;
  }
}

let instance: ReportDocumentManager;
export const getReportDocumentManager = () => {
  if (!instance) {
    instance = new ReportDocumentManager(wwgLogger, getReportingFactory(), getAIReportDocumentWorkflow());
  }
  return instance;
};
