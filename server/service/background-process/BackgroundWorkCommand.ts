/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import ContextError from '../../error/ContextError';
import { type LoggerInterface, wwgLogger } from '../wwgLogger';
import { isJobType } from '../../models/backgroundJob';
import { getBulkImportWorker } from '../survey/workflow/BulkImportWorker';
import { type BackgroundWorker, type JobResult } from './types';
import { getBulkSurveyCreateWorker } from './worker/BulkSurveyCreateWorker';
import { getGenerateReportWorker } from '../pptx-report/GenerateReportWorker';
import { getAutoAggregatedSurveyWorker } from '../survey/workflow/AutoAggregatedSurveyWorker';
import { getUpdateAllAggregatedSurveyWorker } from '../survey/workflow/UpdateAllAggregatedSurveyWorker';
import { getInternalReleaseWorker } from '../internal-release/InternalReleaseWorker';
import { getSuccessReportWorker } from '../staff-reporting/SuccessReportWorker';
import { getMaterialityAssessmentWorker } from '../materiality-assessment/background-job/MaterialityAssessmentWorker';
import { getCloneOrganizationWorker } from '../organization/clone/CloneWorker';
import { getMigrationWorker } from '../migration/MigrationWorker';
import { getInitiativeExportWorker } from '../initiative/export/InitiativeExportWorker';
import { getSurveyAutoAnswerWorker } from '../ai/survey-auto-answer/SurveyAutoAnswerWorker';
import { getAIDocumentLibraryScanWorker } from '../ai/document-utr-mapping/AIDocumentUtrMappingWorker';
import { getAIReportDocumentWorker } from '../report-document/AIReportDocumentWorker';

export class BackgroundWorkCommand {

  constructor(
    private logger: LoggerInterface,
    private workers: BackgroundWorker[],
  ) {
  }

  public async process(args: string[]) {
    const [jobType, jobId] = args;

    const retry = args.includes('--retry')

    this.logger.info(`Start processing background job ${jobType}`, { args })
    if (!isJobType(jobType)) {
      throw new ContextError(`Failed to process valid job type ${jobType}`, { args })
    }

    const results: JobResult[] = [];
    for (const worker of this.workers) {
      if (worker.canHandle(jobType)) {
        this.logger.info(`Found handler ${worker.constructor.name}, processing...`)
        const result = await worker.process(jobId, { retry });
        results.push(result);
        if (!result.passToNextHandler) {
          break;
        }
      }
    }

    this.logger.info(`Completed processing background job ${jobType}`, {
      args,
      results: results.map(r => ({ _id: r._id, status: r.status }))
    });
    return results;
  }
}

let instance: BackgroundWorkCommand;
export const getBackgroundWorkCommand = () => {
  if (!instance) {
    instance = new BackgroundWorkCommand(wwgLogger, [
      getBulkImportWorker(),
      getBulkSurveyCreateWorker(),
      getGenerateReportWorker(),
      getAutoAggregatedSurveyWorker(),
      getUpdateAllAggregatedSurveyWorker(),
      getInternalReleaseWorker(),
      getCloneOrganizationWorker(),
      getSuccessReportWorker(),
      getMaterialityAssessmentWorker(),
      getMigrationWorker(),
      getInitiativeExportWorker(),
      getSurveyAutoAnswerWorker(),
      getAIDocumentLibraryScanWorker(),
      getAIReportDocumentWorker(),
    ]);
  }
  return instance;
}
