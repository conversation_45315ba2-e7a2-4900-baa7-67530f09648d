/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { <PERSON>gger<PERSON>nterface, wwgLogger } from '../../wwgLogger';
import BackgroundJob, { JobStatus, JobType, LogMessage, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import { BackgroundBaseWorkflow, TaskResult } from '../../background-process/BackgroundBaseWorkflow';
import {
  SupportedJobModel,
  WorkflowCreate,
  CreatedJob,
  NotificationParams,
  TaskAIAutoAnswerProcess,
  AIAutoAnswerTask,
  TaskAIAutoAnswerComplete,
  TaskAIAutoAnswerSetup,
  ProcessedContext,
  TaskAIAutoAnswerPrepareDocuments,
  AutoAnswerUtrv,
  TaskAIAutoAnswerCleanup,
} from './types';
import { createLogEntry } from '../../../service/jobs/logMessage';
import { ObjectId } from 'bson';
import { getNotificationService, NotificationService } from '../../../service/notification/NotificationService';
import {
  CustomAttributes,
  NotificationCategory,
  NotificationPage,
} from '../../../service/notification/NotificationTypes';
import { UrlMapper } from '../../../service/url/UrlMapper';
import { LEVEL } from '../../../service/event/Events';
import User from '../../../models/user';
import { getSurveyAutoAnswerService, SurveyAutoAnswerService } from './SurveyAutoAnswerService';
import { getRootInitiativeService, RootInitiativeService } from '../../../service/organization/RootInitiativeService';
import Initiative, { InitiativePlain } from '../../../models/initiative';
import { generatedUUID } from '../../../service/crypto/token';
import { getBackgroundJobService } from '../../../service/background-process/BackgroundJobService';
import {
  AIDocumentUtrMappingService,
  getAIDocumentLibraryScanService,
} from '../document-utr-mapping/AIDocumentUtrMappingService';
import { RelevantDocumentMappings } from '../document-utr-mapping/types';
import { AIDocumentService, getAIDocumentService } from '../AIDocumentService';
import { processInBatches } from '../../../util/batch';

const BATCH_SIZE = 5;
export class SurveyAutoAnswerWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  protected jobType = JobType.AIAutoAnswerSurvey;

  constructor(
    protected logger: LoggerInterface,
    private autoAnswerService: SurveyAutoAnswerService,
    private notificationService: NotificationService,
    private rootInitiativeService: RootInitiativeService,
    private documentUtrMappingService: AIDocumentUtrMappingService,
    private aiDocumentService: AIDocumentService
  ) {
    super();
  }

  public async findLatestJobWithKey(initiativeId: ObjectId, surveyId: string) {
    const idempotencyKey = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
      idempotencyKey: `survey-${surveyId}`,
    });
    return super.findLatestJobWithKey(initiativeId, idempotencyKey);
  }

  public async createOrRetry(workflow: WorkflowCreate): Promise<CreatedJob> {
    const { initiativeId, surveyId, userId, useDocumentLibrary, isOverwriteMetric } = workflow;

    const idempotencyKey = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
      idempotencyKey: `survey-${surveyId}`,
    });

    // Ensure we are not spamming the same job again and again.
    const exists = await BackgroundJob.findOne({
      type: this.jobType,
      initiativeId,
      idempotencyKey,
      status: { $in: [JobStatus.Pending, JobStatus.Processing] },
    });

    if (exists) {
      this.logger.error(
        new ContextError(`AI auto answer job already exists for this configuration`, {
          initiativeId,
          idempotencyKey,
          jobType: this.jobType,
          existingJobId: exists._id,
        })
      );
      // re-run the Job
      await getBackgroundJobService().runFromJob(exists);
      return {
        jobId: exists._id.toString(),
        status: exists.status,
      };
    }

    return this.autoAnswerService.createJob({
      initiativeId,
      surveyId,
      userId,
      idempotencyKey,
      useDocumentLibrary,
      isOverwriteMetric,
    });
  }

  private getPrepareDocumentsTask({
    mappings,
    utrvs,
    surveyId,
    isOverwriteMetric,
  }: {
    mappings: RelevantDocumentMappings[];
    utrvs: AutoAnswerUtrv[];
    surveyId: ObjectId;
    isOverwriteMetric: boolean;
  }): TaskAIAutoAnswerPrepareDocuments {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Preparing documents for survey: ${surveyId.toString()}`,
      type: TaskType.AIAutoAnswerPrepareDocuments,
      status: TaskStatus.Pending,
      data: {
        mappings,
        utrvs,
        surveyId,
        isOverwriteMetric,
      },
    };
  }

  private getProcessAnswerTask({
    utrvId,
    surveyId,
    isOverwriteMetric,
    relatedDocumentIds,
    assistantId,
  }: {
    utrvId: ObjectId;
    surveyId: ObjectId;
    isOverwriteMetric: boolean;
    relatedDocumentIds?: string[];
    assistantId?: string;
  }): TaskAIAutoAnswerProcess {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Answering utrv: ${utrvId.toString()} in survey: ${surveyId.toString()}`,
      type: TaskType.AIAutoAnswerProcess,
      status: TaskStatus.Pending,
      data: {
        utrvId,
        isOverwriteMetric,
        relatedDocumentIds,
        assistantId,
      },
    };
  }

  private async processSetupTask(job: SupportedJobModel, task: TaskAIAutoAnswerSetup) {
    await this.startTask(job, task);

    const { utrvs, surveyId, useDocumentLibrary, isOverwriteMetric } = task.data;

    if (utrvs.length > 0) {
      if (!useDocumentLibrary) {
        utrvs.forEach(({ _id: utrvId }) => {
          const task = this.getProcessAnswerTask({ utrvId, surveyId, isOverwriteMetric });
          job.tasks.push(task);
        });

        const completeTask: TaskAIAutoAnswerComplete = this.getCompleteTask();
        job.tasks.push(completeTask);
      } else {
        const mappings = await this.documentUtrMappingService.getRelevantDocumentUtrMappings({
          utrCodes: utrvs.map(({ utrCode }) => utrCode),
          initiativeId: job.initiativeId,
        });

        const nextTask = this.getPrepareDocumentsTask({ mappings, utrvs, surveyId, isOverwriteMetric });
        job.tasks.push(nextTask);
      }
    }

    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed ${task.name} processing`, {
        metadata: {
          taskId: task.id,
        },
      })
    );

    job.markModified('tasks');
    return job.save();
  }

  private async prepareDocumentsTask(job: SupportedJobModel, task: TaskAIAutoAnswerPrepareDocuments) {
    await this.startTask(job, task);

    const { mappings, utrvs, surveyId, isOverwriteMetric } = task.data;
    const utrRelatedDocumentsMap = utrvs.map((utrv) => {
      // get top 3 related document per utrv
      const relatedDocuments = mappings
        .filter((mapping) => mapping.utrs.some((utr) => utr.code === utrv.utrCode))
        .toSorted((a, b) => {
          const aScore = a.utrs.find((utr) => utr.code === utrv.utrCode)?.score;
          const bScore = b.utrs.find((utr) => utr.code === utrv.utrCode)?.score;
          if (aScore === undefined || bScore === undefined) {
            return 0;
          }
          return aScore - bScore;
        })
        .slice(0, 3);
      return {
        utrvId: utrv._id,
        relatedDocuments,
      };
    });

    const uniqueDocuments = utrRelatedDocumentsMap.reduce<RelevantDocumentMappings[]>(
      (documents, { relatedDocuments }) => {
        const newDocuments = relatedDocuments.filter((doc) =>
          documents.every((d) => !d.documentId.equals(doc.documentId))
        );
        documents.push(...newDocuments);
        return documents;
      },
      []
    );

    this.logger.info(`Uploading ${uniqueDocuments.length} documents to AI`, {
      jobId: job._id,
      initiativeId: job.initiativeId,
    });

    const documentIdMap: Record<string, string> = {};
    await processInBatches({
      data: uniqueDocuments,
      processFn: async (document) => {
        return this.aiDocumentService.uploadDocument({
          _id: document.documentId,
          ...document,
        });
      },
      batchSize: BATCH_SIZE,
      batchRateLimit: { minWaitTime: 3, unit: 'second' },
      onBatchComplete: (results) => {
        results.forEach((result) => {
          documentIdMap[result.documentId.toString()] = result.uploadedFileId;
        });
      },
    });

    const assistantId = await this.autoAnswerService.getOrCreateAutoAnswerAssistantId();

    utrRelatedDocumentsMap.forEach(({ utrvId, relatedDocuments }) => {
      const relatedDocumentIds = relatedDocuments.map((d) => documentIdMap[d.documentId.toString()]);
      const task = this.getProcessAnswerTask({ utrvId, surveyId, isOverwriteMetric, relatedDocumentIds, assistantId });
      job.tasks.push(task);
    });

    job.logs.push(
      createLogEntry('Completed preparing documents and assistant', {
        metadata: {
          jobId: job._id.toString(),
          taskId: task.id,
          initiativeId: job.initiativeId.toString(),
          documentIdMap,
          assistantId,
        },
      })
    );

    job.tasks.push(this.getCleanupTask(documentIdMap), this.getCompleteTask());
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.markModified('tasks');
    return job.save();
  }

  private async processAnswerTask(job: SupportedJobModel, task: TaskAIAutoAnswerProcess) {
    await this.startTask(job, task);

    const initiative = await Initiative.findById(job.initiativeId).orFail().lean<InitiativePlain>().exec();
    const { utrvId, isOverwriteMetric, assistantId } = task.data;

    const processedResult = await this.autoAnswerService.processAnswerUtrv({
      initiative,
      utrvId,
      userId: job.userId,
      jobId: job._id,
      isOverwriteMetric,
      assistantId,
    });

    (task as TaskAIAutoAnswerProcess<ProcessedContext>).data = {
      utrvId,
      isOverwriteMetric,
      isSuccess: processedResult.isSuccess,
      ...(processedResult.errorMessage ? { errorMessage: processedResult.errorMessage } : {}),
    };
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    this.logger.info(`Completed answering utrvId: ${utrvId.toString()}`, processedResult);

    job.markModified('tasks');
    return job.save();
  }

  public getCompleteTask(): TaskAIAutoAnswerComplete {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Complete automatic answer the survey`,
      type: TaskType.AIAutoAnswerComplete,
      status: TaskStatus.Pending,
      data: {
        completedUtrvs: [],
        errorUtrvs: [],
      },
    };
  }

  private getCleanupTask(documentIdMap: Record<string, string>): TaskAIAutoAnswerCleanup {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Clean up temporary files`,
      type: TaskType.AIAutoAnswerCleanup,
      status: TaskStatus.Pending,
      data: {
        documentIdMap,
      },
    };
  }

  private async completeTaskHandler(job: SupportedJobModel, task: TaskAIAutoAnswerComplete) {
    await this.startTask(job, task);

    const setupTask = job.tasks.find((t) => t.type === TaskType.AIAutoAnswerSetup);
    if (!setupTask) {
      throw new ContextError('Not found a set up task to process', { jobId: job._id, taskId: task.id });
    }
    const surveyId = setupTask.data.surveyId;

    const processTasks = job.tasks.filter(
      (task) => task.type === TaskType.AIAutoAnswerProcess
    ) as TaskAIAutoAnswerProcess<ProcessedContext>[];

    const { completedUtrvs, errorUtrvs } = processTasks.reduce(
      (acc, task) => {
        if (task.data.isSuccess) {
          acc.completedUtrvs.push(task.data.utrvId);
        } else {
          acc.errorUtrvs.push(task.data.utrvId);
        }
        return acc;
      },
      { completedUtrvs: [] as ObjectId[], errorUtrvs: [] as ObjectId[] }
    );

    task.data = {
      completedUtrvs,
      errorUtrvs,
    };
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    const logMessage = `Completed the process of automatic answer the survey`;
    job.logs.push(
      createLogEntry(logMessage, {
        metadata: {
          jobId: job._id.toString(),
          taskId: task.id,
          initiativeId: job.initiativeId.toString(),
        },
      })
    );

    const user = await User.findById(job.userId).orFail().lean().exec();

    try {
      const log = await this.sendNotification({
        title: logMessage,
        content: `Check out for updates.`,
        job,
        user,
        surveyId,
        initiativeId: job.initiativeId,
      });
      job.logs.push(log);
    } catch (e) {
      job.logs.push(
        createLogEntry(`Failed to AI automatic answer for survey notification`, {
          severity: LEVEL.ERROR,
          metadata: {
            userId: user._id.toString(),
            jobId: job._id.toString(),
            errorMessage: e.message,
          },
        })
      );
    }

    job.markModified('tasks');
    return job.save();
  }

  private async cleanupTaskHandler(job: SupportedJobModel, task: TaskAIAutoAnswerCleanup) {
    await this.startTask(job, task);

    const { documentIdMap = {} } = task.data;
    const idMap = Object.entries(documentIdMap);

    await processInBatches({
      data: idMap,
      processFn: async ([documentId, fileId]) => {
        return this.aiDocumentService.cleanupDocument({ documentId, fileId }).catch(() => {
          this.logger.error(
            new ContextError(`Failed to clean up document from AI`, {
              initiativeId: job.initiativeId,
              idempotencyKey: job.idempotencyKey,
              jobType: this.jobType,
              documentId,
              fileId,
            })
          );
        });
      },
      batchSize: BATCH_SIZE,
      batchRateLimit: { minWaitTime: 3, unit: 'second' },
    });

    return this.completeTask(job, task);
  }

  private async sendNotification({
    job,
    user,
    title,
    content,
    surveyId,
    initiativeId,
  }: NotificationParams): Promise<LogMessage> {
    const initiative = await Initiative.findById(initiativeId).orFail().lean().exec();
    const org = await this.rootInitiativeService.getOrganization(initiative);
    const jobId = job._id.toString();
    const surveyIdStr = surveyId.toString();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.SurveyOverview,
      domain: undefined,
      initiativeId: initiativeId.toString(),
      surveyId: surveyIdStr,
      jobId: jobId,
    };

    const notificationId = new ObjectId();
    await this.notificationService.createNotification({
      _id: notificationId,
      title,
      content,
      category: NotificationCategory.Announcements,
      topic: `AI-auto-answer-survey-${surveyIdStr}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: [{ id: String(user._id) }],
    });

    return createLogEntry(`Finish AI automatic answer for surveyId: ${surveyIdStr}`, {
      metadata: {
        notificationId,
        userId: user._id.toString(),
        jobId: jobId,
      },
    });
  }

  public async processTask(job: SupportedJobModel, task: AIAutoAnswerTask): Promise<TaskResult<SupportedJobModel>> {
    switch (task.type) {
      case TaskType.AIAutoAnswerSetup:
        return {
          job: await this.processSetupTask(job, task),
          // Allow to go to next job straight away in the same process
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerPrepareDocuments:
        return {
          job: await this.prepareDocumentsTask(job, task),
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerProcess:
        return {
          job: await this.processAnswerTask(job, task),
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerComplete:
        return {
          job: await this.completeTaskHandler(job, task),
          executeNextTask: true,
        };
      case TaskType.AIAutoAnswerCleanup:
        return {
          job: await this.cleanupTaskHandler(job, task),
          executeNextTask: true,
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: SurveyAutoAnswerWorkflow;
export const getSurveyAutoAnswerWorkflow = () => {
  if (!instance) {
    instance = new SurveyAutoAnswerWorkflow(
      wwgLogger,
      getSurveyAutoAnswerService(),
      getNotificationService(),
      getRootInitiativeService(),
      getAIDocumentLibraryScanService(),
      getAIDocumentService()
    );
  }
  return instance;
};
