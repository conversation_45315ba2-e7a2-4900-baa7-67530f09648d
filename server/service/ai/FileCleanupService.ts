/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import type { AiUploadedFilePlain } from '../../models/aiUploadedFile';
import AiUploadedFile from '../../models/aiUploadedFile';
import type { FileStorageService } from './FileStorageService';
import { getFileStorageService } from './FileStorageService';
import type { ProviderFileManager } from './ProviderFileManager';
import { getProviderFileManager } from './ProviderFileManager';
import { getErrorMessage, getErrorDetails } from '../../util/errorUtils';

export interface CleanupResult {
  processedCount: number;
  deletedFiles: number;
  deletedBytes: number;
  errors: Array<{
    fileId: string;
    error: string;
  }>;
  duration: number;
}

export interface CleanupOptions {
  dryRun?: boolean;
  batchSize?: number;
  inactiveDays?: number;
  noProviderDays?: number;  // Days before cleaning up files without provider references
}

/**
 * FileCleanupService - Handles cleanup of inactive and unused files
 * 
 * This service:
 * - Cleans up files marked as inactive for extended periods
 * - Removes files without any provider references
 * - Cleans up expired Gemini provider files (48-hour expiration)
 * - Deletes files from cloud storage
 * - Provides dry-run capability for safety
 */
export class FileCleanupService {
  constructor(
    private fileStorage: FileStorageService,
    private providerFileManager: ProviderFileManager,
    private logger: Logger
  ) {}

  /**
   * Clean up inactive and unused files
   */
  async cleanupFiles(options: CleanupOptions = {}): Promise<CleanupResult> {
    const startTime = Date.now();
    const {
      dryRun = false,
      batchSize = 100,
      inactiveDays = 30,
      noProviderDays = 90
    } = options;

    const result: CleanupResult = {
      processedCount: 0,
      deletedFiles: 0,
      deletedBytes: 0,
      errors: [],
      duration: 0
    };

    this.logger.info('Starting file cleanup', {
      dryRun,
      batchSize,
      inactiveDays,
      noProviderDays
    });

    try {
      // Clean up inactive files
      await this.cleanupInactiveFiles(result, {
        dryRun,
        batchSize,
        inactiveDays
      });

      // Clean up files without provider references
      await this.cleanupFilesWithoutProviders(result, {
        dryRun,
        batchSize,
        noProviderDays
      });

      // Clean up expired provider files
      await this.cleanupExpiredProviderFiles(result, {
        dryRun,
        batchSize
      });

    } catch (error) {
      this.logger.error('File cleanup failed', getErrorDetails(error));
      result.errors.push({
        fileId: 'general',
        error: getErrorMessage(error)
      });
    }

    result.duration = Date.now() - startTime;

    this.logger.info('File cleanup completed', {
      ...result,
      durationSeconds: Math.round(result.duration / 1000)
    });

    return result;
  }

  /**
   * Clean up files marked as inactive
   */
  private async cleanupInactiveFiles(
    result: CleanupResult,
    options: {
      dryRun: boolean;
      batchSize: number;
      inactiveDays: number;
    }
  ): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - options.inactiveDays);

    this.logger.info('Cleaning up inactive files', {
      cutoffDate,
      dryRun: options.dryRun
    });

    // Find inactive files in batches
    let processedInBatch = 0;
    do {
      const inactiveFiles = await AiUploadedFile.find({
        isActive: false,
        updated: { $lt: cutoffDate }
      })
      .limit(options.batchSize)
      .lean();

      processedInBatch = inactiveFiles.length;
      result.processedCount += processedInBatch;

      for (const file of inactiveFiles) {
        try {
          if (options.dryRun) {
            this.logger.info('Would delete inactive file (dry run)', {
              fileId: file._id,
              originalName: file.originalName,
              size: file.metadata.size
            });
            result.deletedFiles++;
            result.deletedBytes += file.metadata.size || 0;
          } else {
            await this.deleteFile(file);
            result.deletedFiles++;
            result.deletedBytes += file.metadata.size || 0;
          }
        } catch (error) {
          this.logger.error('Failed to delete inactive file', {
            fileId: file._id,
            ...getErrorDetails(error)
          });
          result.errors.push({
            fileId: file._id.toString(),
            error: getErrorMessage(error)
          });
        }
      }
    } while (processedInBatch === options.batchSize);
  }

  /**
   * Clean up files with no provider references
   * These are files that were uploaded but never sent to any AI provider
   */
  private async cleanupFilesWithoutProviders(
    result: CleanupResult,
    options: {
      dryRun: boolean;
      batchSize: number;
      noProviderDays: number;
    }
  ): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - options.noProviderDays);

    this.logger.info('Cleaning up files without provider references', {
      cutoffDate,
      dryRun: options.dryRun
    });

    // Find files without any provider references
    let processedInBatch = 0;
    do {
      const filesWithoutProviders = await AiUploadedFile.find({
        $and: [
          { created: { $lt: cutoffDate } },
          {
            $or: [
              { providerFiles: { $exists: false } },
              { providerFiles: {} },
              { 
                $and: [
                  { 'providerFiles.openai': { $exists: false } },
                  { 'providerFiles.claude': { $exists: false } },
                  { 'providerFiles.gemini': { $exists: false } }
                ]
              }
            ]
          }
        ]
      })
      .limit(options.batchSize)
      .lean();

      processedInBatch = filesWithoutProviders.length;
      result.processedCount += processedInBatch;

      for (const file of filesWithoutProviders) {
        try {
          if (options.dryRun) {
            this.logger.info('Would delete file without providers (dry run)', {
              fileId: file._id,
              originalName: file.originalName,
              created: file.created
            });
            result.deletedFiles++;
            result.deletedBytes += file.metadata.size || 0;
          } else {
            await this.deleteFile(file);
            result.deletedFiles++;
            result.deletedBytes += file.metadata.size || 0;
          }
        } catch (error) {
          this.logger.error('Failed to delete file without providers', {
            fileId: file._id,
            ...getErrorDetails(error)
          });
          result.errors.push({
            fileId: file._id.toString(),
            error: getErrorMessage(error)
          });
        }
      }
    } while (processedInBatch === options.batchSize);
  }

  /**
   * Clean up expired provider files (currently only Gemini files expire)
   */
  private async cleanupExpiredProviderFiles(
    result: CleanupResult,
    options: {
      dryRun: boolean;
      batchSize: number;
    }
  ): Promise<void> {
    this.logger.info('Checking for expired provider files', {
      dryRun: options.dryRun
    });

    // Gemini files expire after 48 hours
    const geminiCutoff = new Date();
    geminiCutoff.setHours(geminiCutoff.getHours() - 48);

    // Find files with Gemini uploads older than 48 hours
    let processedInBatch = 0;
    do {
      const filesWithGemini = await AiUploadedFile.find({
        'providerFiles.gemini': { $exists: true },
        'providerFiles.gemini.uploadedAt': { $lt: geminiCutoff }
      })
      .limit(options.batchSize)
      .lean();

      processedInBatch = filesWithGemini.length;

      for (const file of filesWithGemini) {
        try {
          if (options.dryRun) {
            this.logger.info('Would remove expired Gemini reference (dry run)', {
              fileId: file._id,
              geminiFileId: file.providerFiles?.gemini?.fileId,
              uploadedAt: file.providerFiles?.gemini?.uploadedAt
            });
          } else {
            // Remove only the Gemini reference, not the entire file
            await AiUploadedFile.updateOne(
              { _id: file._id },
              { $unset: { 'providerFiles.gemini': 1 } }
            );
            
            this.logger.info('Removed expired Gemini reference', {
              fileId: file._id,
              geminiFileId: file.providerFiles?.gemini?.fileId
            });
          }
        } catch (error) {
          this.logger.error('Failed to clean up expired Gemini file', {
            fileId: file._id,
            ...getErrorDetails(error)
          });
          result.errors.push({
            fileId: file._id.toString(),
            error: `Gemini cleanup: ${getErrorMessage(error)}`
          });
        }
      }
    } while (processedInBatch === options.batchSize);
  }

  /**
   * Delete a file from all locations
   */
  private async deleteFile(file: AiUploadedFilePlain): Promise<void> {
    const fileId = file._id.toString();
    
    this.logger.info('Deleting file', {
      fileId,
      originalName: file.originalName,
      hasStoragePath: !!file.fileStoragePath,
      providerCount: file.providerFiles ? Object.keys(file.providerFiles).length : 0
    });

    // Delete from cloud storage
    if (file.fileStoragePath) {
      try {
        await this.fileStorage.deleteFile(file.fileStoragePath);
        this.logger.debug('Deleted from cloud storage', {
          fileId,
          storagePath: file.fileStoragePath
        });
      } catch (error) {
        this.logger.warn('Failed to delete from cloud storage', {
          fileId,
          storagePath: file.fileStoragePath,
          ...getErrorDetails(error)
        });
      }
    }

    // Delete from providers
    if (file.providerFiles) {
      try {
        await this.providerFileManager.deleteFile(fileId);
        this.logger.debug('Deleted from providers', { fileId });
      } catch (error) {
        this.logger.warn('Failed to delete from providers', {
          fileId,
          ...getErrorDetails(error)
        });
      }
    }

    // Remove from database
    await AiUploadedFile.deleteOne({ _id: file._id });
    
    this.logger.info('File deleted successfully', { fileId });
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStatistics(): Promise<{
    totalFiles: number;
    activeFiles: number;
    inactiveFiles: number;
    filesWithoutProviders: number;
    totalSize: number;
    potentialCleanupSize: number;
  }> {
    // Total files
    const totalFiles = await AiUploadedFile.countDocuments();
    
    // Active vs inactive
    const activeFiles = await AiUploadedFile.countDocuments({ isActive: true });
    const inactiveFiles = totalFiles - activeFiles;
    
    // Files without provider references
    const filesWithoutProviders = await AiUploadedFile.countDocuments({
      $or: [
        { providerFiles: { $exists: false } },
        { providerFiles: {} },
        { 
          $and: [
            { 'providerFiles.openai': { $exists: false } },
            { 'providerFiles.claude': { $exists: false } },
            { 'providerFiles.gemini': { $exists: false } }
          ]
        }
      ]
    });
    
    // Calculate sizes
    const sizeAggregation = await AiUploadedFile.aggregate([
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$metadata.size' },
          inactiveSize: {
            $sum: {
              $cond: [
                { $eq: ['$isActive', false] },
                '$metadata.size',
                0
              ]
            }
          }
        }
      }
    ]);
    
    const sizes = sizeAggregation[0] || { totalSize: 0, inactiveSize: 0 };
    
    return {
      totalFiles,
      activeFiles,
      inactiveFiles,
      filesWithoutProviders,
      totalSize: sizes.totalSize || 0,
      potentialCleanupSize: sizes.inactiveSize || 0
    };
  }
}

// Singleton instance
let instance: FileCleanupService | undefined;

export const getFileCleanupService = (): FileCleanupService => {
  if (!instance) {
    instance = new FileCleanupService(
      getFileStorageService(),
      getProviderFileManager(),
      wwgLogger
    );
  }
  return instance;
};