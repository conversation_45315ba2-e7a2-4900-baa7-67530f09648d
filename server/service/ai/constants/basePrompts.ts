/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

/**
 * Base system prompts for each AI model
 * These are the default prompts that are used when no custom system prompt is provided
 */

export const BASE_SYSTEM_PROMPTS = {
  // OpenAI/ChatGPT base prompt
  openai: 'You are an AI assistant that analyzes documents and matches them to sustainability metrics.',
  
  // Anthropic/Claude base prompt
  anthropic: 'You are an expert in sustainability reporting, ESG metrics, and document analysis. You analyze documents to find relevant information for sustainability tracking and reporting. Be precise and thorough in your analysis.',
  
  // Google/Gemini base prompt
  google: 'You are a sustainability analysis expert specializing in ESG reporting and metrics extraction. Analyze documents carefully to identify relevant sustainability data and match it to the appropriate Universal Trackers (UTRs).',
} as const;

export type BasePromptProvider = keyof typeof BASE_SYSTEM_PROMPTS;

/**
 * Special prompt prefixes or suffixes used by specific models
 */
export const PROMPT_PREFIXES = {
  // Claude uses JSON prefill to force proper response format
  claude_json_prefill: '{"result": [',
} as const;

/**
 * Prompt modes for how system prompts interact with base prompts
 */
export enum PromptMode {
  /** Extend/append to the base system prompt */
  Extend = 'extend',
  /** Completely override the base system prompt */
  Override = 'override'
}