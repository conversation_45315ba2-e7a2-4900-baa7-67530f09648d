/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Lo<PERSON> } from 'winston';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import * as crypto from 'crypto';

// Magic numbers for common file types
const MAGIC_NUMBERS: Record<string, { bytes: number[]; offset: number }[]> = {
  'application/pdf': [
    { bytes: [0x25, 0x50, 0x44, 0x46], offset: 0 } // %PDF
  ],
  'image/png': [
    { bytes: [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], offset: 0 } // PNG header
  ],
  'image/jpeg': [
    { bytes: [0xFF, 0xD8, 0xFF], offset: 0 } // JPEG header
  ],
  'image/jpg': [
    { bytes: [0xFF, 0xD8, 0xFF], offset: 0 } // JPEG header
  ],
  'image/gif': [
    { bytes: [0x47, 0x49, 0x46, 0x38, 0x37, 0x61], offset: 0 }, // GIF87a
    { bytes: [0x47, 0x49, 0x46, 0x38, 0x39, 0x61], offset: 0 }  // GIF89a
  ],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    { bytes: [0x50, 0x4B, 0x03, 0x04], offset: 0 } // DOCX (ZIP header)
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    { bytes: [0x50, 0x4B, 0x03, 0x04], offset: 0 } // XLSX (ZIP header)
  ],
  'application/zip': [
    { bytes: [0x50, 0x4B, 0x03, 0x04], offset: 0 } // ZIP header
  ]
};

// MIME type aliases for validation
const MIME_TYPE_ALIASES: Record<string, string[]> = {
  'application/pdf': ['application/x-pdf'],
  'image/jpeg': ['image/jpg'],
  'text/plain': ['text/csv', 'application/csv'],
  'application/json': ['text/json']
};

// Allowed MIME types
const ALLOWED_MIME_TYPES = new Set([
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv',
  'image/png',
  'image/jpeg',
  'image/jpg',
  'image/gif',
  'image/svg+xml',
  'application/json',
  'text/markdown'
]);

// Malicious content patterns
const MALICIOUS_PATTERNS = [
  // eslint-disable-next-line no-control-regex
  /[\x00].*<script/gi,           // Null byte before script
  /<script[^>]*>.*?<\/script>/gi, // Script tags
  /javascript:/gi,              // JavaScript protocol
  /data:.*script/gi,            // Data URL with script
  /vbscript:/gi,                // VBScript protocol
  /on\w+\s*=/gi,               // Event handlers
  /<iframe/gi,                  // iframes
  /<object/gi,                  // object tags
  /<embed/gi,                   // embed tags
];

export interface FileValidationResult {
  valid: boolean;
  detectedType?: string;
  errors: string[];
  warnings: string[];
}

export class FileValidationService {
  constructor(
    private logger: Logger
  ) {}

  /**
   * Validate file content including magic numbers and security checks
   */
  async validateFile(
    buffer: Buffer,
    declaredMimeType: string,
    filename: string
  ): Promise<FileValidationResult> {
    const result: FileValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    try {
      // 1. Check if MIME type is allowed
      if (!this.isAllowedMimeType(declaredMimeType)) {
        result.errors.push(`File type '${declaredMimeType}' is not allowed`);
        result.valid = false;
        return result;
      }

      // 2. Validate magic numbers
      const magicValidation = this.validateMagicNumbers(buffer, declaredMimeType);
      if (!magicValidation.valid) {
        result.errors.push(magicValidation.error!);
        result.valid = false;
      } else if (magicValidation.detectedType) {
        result.detectedType = magicValidation.detectedType;
      }

      // 3. Check for malicious content
      const maliciousCheck = this.checkMaliciousContent(buffer, declaredMimeType);
      if (!maliciousCheck.valid) {
        result.errors = result.errors.concat(maliciousCheck.errors);
        result.valid = false;
      }
      result.warnings = result.warnings.concat(maliciousCheck.warnings);

      // 4. Validate file structure for specific types
      const structureCheck = await this.validateFileStructure(buffer, declaredMimeType);
      if (!structureCheck.valid) {
        result.errors = result.errors.concat(structureCheck.errors);
        result.valid = false;
      }

      // 5. Check file extension matches MIME type
      const extensionCheck = this.validateFileExtension(filename, declaredMimeType);
      if (!extensionCheck.valid) {
        result.warnings.push(extensionCheck.warning!);
      }

      this.logger.info('File validation completed', {
        filename,
        declaredType: declaredMimeType,
        detectedType: result.detectedType,
        valid: result.valid,
        errorCount: result.errors.length,
        warningCount: result.warnings.length
      });

      return result;

    } catch (error) {
      this.logger.error(new ContextError('File validation failed', {
        filename,
        cause: error
      }));
      result.errors.push('File validation failed: ' + error.message);
      result.valid = false;
      return result;
    }
  }

  /**
   * Check if MIME type is allowed
   */
  private isAllowedMimeType(mimeType: string): boolean {
    return ALLOWED_MIME_TYPES.has(mimeType);
  }

  /**
   * Validate magic numbers
   */
  private validateMagicNumbers(
    buffer: Buffer,
    declaredMimeType: string
  ): { valid: boolean; error?: string; detectedType?: string } {
    // For text-based formats, skip magic number validation
    if (declaredMimeType.startsWith('text/') || 
        declaredMimeType === 'application/json' ||
        declaredMimeType === 'image/svg+xml') {
      return { valid: true, detectedType: declaredMimeType };
    }

    const magicSignatures = MAGIC_NUMBERS[declaredMimeType];
    if (!magicSignatures) {
      // No magic number validation for this type
      return { valid: true };
    }

    // Check if buffer matches any of the signatures
    const matches = magicSignatures.some(sig => {
      if (buffer.length < sig.offset + sig.bytes.length) {
        return false;
      }
      return sig.bytes.every((byte, i) => buffer[sig.offset + i] === byte);
    });

    if (!matches) {
      // Check if it matches any other known type
      for (const [mimeType, signatures] of Object.entries(MAGIC_NUMBERS)) {
        const otherMatches = signatures.some(sig => {
          if (buffer.length < sig.offset + sig.bytes.length) {
            return false;
          }
          return sig.bytes.every((byte, i) => buffer[sig.offset + i] === byte);
        });
        
        if (otherMatches) {
          // Check if this is an acceptable alias
          const aliases = MIME_TYPE_ALIASES[mimeType] || [];
          if (aliases.includes(declaredMimeType)) {
            return { valid: true, detectedType: mimeType };
          }
          
          return {
            valid: false,
            error: `File content does not match declared type. Declared: ${declaredMimeType}, Detected: ${mimeType}`
          };
        }
      }
      
      return {
        valid: false,
        error: `File content does not match declared type: ${declaredMimeType}`
      };
    }

    return { valid: true, detectedType: declaredMimeType };
  }

  /**
   * Check for malicious content
   */
  private checkMaliciousContent(
    buffer: Buffer,
    mimeType: string
  ): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Convert buffer to string for pattern matching (first 10KB)
    const sampleSize = Math.min(buffer.length, 10240);
    const content = buffer.toString('utf8', 0, sampleSize);

    // Check for malicious patterns
    for (const pattern of MALICIOUS_PATTERNS) {
      if (pattern.test(content)) {
        // For certain file types, some patterns might be legitimate
        if (mimeType === 'image/svg+xml' && pattern.source.includes('script')) {
          warnings.push('SVG file contains script elements - these will be sanitized');
        } else {
          errors.push(`File contains potentially malicious content: ${pattern.source}`);
        }
      }
    }

    // Check for null bytes (except in binary files)
    if (!mimeType.startsWith('image/') && 
        !mimeType.includes('pdf') && 
        !mimeType.includes('zip') &&
        !mimeType.includes('document')) {
      if (buffer.includes(0x00)) {
        errors.push('File contains null bytes which may indicate malicious content');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate file structure for specific types
   */
  private async validateFileStructure(
    buffer: Buffer,
    mimeType: string
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    switch (mimeType) {
      case 'application/pdf': {
        // Check for PDF end marker
        const pdfEnd = buffer.toString('utf8', Math.max(0, buffer.length - 1024));
        if (!pdfEnd.includes('%%EOF')) {
          errors.push('PDF file appears to be truncated or corrupted');
        }
        break;
      }

      case 'application/json': {
        // Validate JSON structure
        try {
          JSON.parse(buffer.toString('utf8'));
        } catch (e) {
          errors.push('Invalid JSON structure: ' + e.message);
        }
        break;
      }

      case 'text/csv': {
        // Basic CSV validation
        const csvContent = buffer.toString('utf8', 0, Math.min(buffer.length, 1024));
        const lines = csvContent.split('\n').filter(line => line.trim());
        if (lines.length > 1) {
          const firstLineColumns = lines[0].split(',').length;
          const inconsistentLines = lines.slice(1, 5).filter(line => 
            line.split(',').length !== firstLineColumns
          );
          if (inconsistentLines.length > 0) {
            errors.push('CSV file has inconsistent column counts');
          }
        }
        break;
      }

      case 'image/svg+xml': {
        // Validate SVG structure
        const svgContent = buffer.toString('utf8');
        if (!svgContent.includes('<svg') || !svgContent.includes('</svg>')) {
          errors.push('Invalid SVG structure');
        }
        break;
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate file extension matches MIME type
   */
  private validateFileExtension(
    filename: string,
    mimeType: string
  ): { valid: boolean; warning?: string } {
    const extension = filename.toLowerCase().split('.').pop();
    if (!extension) {
      return { valid: true }; // No extension to validate
    }

    const expectedExtensions: Record<string, string[]> = {
      'application/pdf': ['pdf'],
      'image/png': ['png'],
      'image/jpeg': ['jpg', 'jpeg'],
      'image/jpg': ['jpg', 'jpeg'],
      'image/gif': ['gif'],
      'image/svg+xml': ['svg'],
      'text/plain': ['txt'],
      'text/csv': ['csv'],
      'application/json': ['json'],
      'text/markdown': ['md', 'markdown'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['xlsx']
    };

    const expected = expectedExtensions[mimeType];
    if (expected && !expected.includes(extension)) {
      return {
        valid: false,
        warning: `File extension '.${extension}' does not match expected extensions for ${mimeType}: ${expected.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Generate file hash for integrity checking
   */
  generateFileHash(buffer: Buffer): string {
    return crypto.createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * Sanitize filename
   */
  sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')  // Replace unsafe characters
      .replace(/_{2,}/g, '_')             // Replace multiple underscores
      .replace(/^[._-]+|[._-]+$/g, '')   // Remove leading/trailing special chars
      .substring(0, 255);                 // Limit length
  }
}

// Singleton instance
let instance: FileValidationService | undefined;

export const getFileValidationService = (): FileValidationService => {
  if (!instance) {
    instance = new FileValidationService(wwgLogger);
  }
  return instance;
};