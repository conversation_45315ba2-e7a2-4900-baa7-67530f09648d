/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import ContextError from '../../error/ContextError';
import BadRequestError from '../../error/BadRequestError';
import UserError from '../../error/UserError';
import { type LoggerInterface, wwgLogger } from '../wwgLogger';
import UniversalTracker from '../../models/universalTracker';
import { getUnifiedAIModelFactory, UNIFIED_AI_MODELS } from './UnifiedAIModelFactory';
import { getProviderFileManager } from './ProviderFileManager';
import { getAITestingPromptService } from './AITestingPromptService';
import { getVectorStoreService } from './VectorStoreService';
import { z } from 'zod';

// Import composed services
import { UTRPreparationService } from './services/UTRPreparationService';
import { AIFileHandlingService } from './services/AIFileHandlingService';

// Import types
import type { FileSupportAiModelWithCapabilities } from './models/FileSupportAiModel';
import type { ExecuteTestRequest, ExecuteTestRequestInternal, ExecuteTestResponse } from './ai-testing-types';
import { PromptMode } from '../../types/aiTesting';
import { AiProvider } from './types';

// Default relevance threshold
const DEFAULT_RELEVANCE_THRESHOLD = 0.7;

// Schema for OpenAI Responses API - all fields must be required
// See: https://platform.openai.com/docs/guides/structured-outputs?api-mode=responses#all-fields-must-be-required
const executeTestResultSchema = z.object({
  result: z.array(
    z.object({
      code: z.string(),
      score: z.number().min(0).max(1),
      explanation: z.string().default(''), // Default to empty string
      matchedContent: z.array(z.string()).default([]), // Default to empty array
    })
  ),
  // Raw response field - required but defaults to empty string for OpenAI compatibility
  rawResponse: z.string().default(''),
});

/**
 * Refactored AITestingService using composition pattern
 * Orchestrates the AI testing process by delegating to specialized services
 */
export class AITestingService {
  constructor(
    private logger: LoggerInterface,
    private unifiedModelFactory: ReturnType<typeof getUnifiedAIModelFactory>,
    private promptService: ReturnType<typeof getAITestingPromptService>,
    private utrPreparationService: UTRPreparationService,
    private fileHandlingService: AIFileHandlingService
  ) {}

  /**
   * Execute a synchronous test of document-to-UTR matching
   */
  public async executeTest(request: ExecuteTestRequestInternal, userId: string): Promise<ExecuteTestResponse> {
    const startTime = Date.now();
    // Use provided execution ID (required in internal interface)
    const { executionId } = request;
    const temporaryFileIds: string[] = []; // Track only temporary files for cleanup
    const cacheHit = false;
    let aiApiTime = 0;
    let aiModel: FileSupportAiModelWithCapabilities | undefined;

    try {
      this.logger.info('Starting AI test execution', {
        executionId,
        hasFiles: !!(request.fileReference || request.uploadedFileIds?.length),
        aiModel: request.options?.aiModel || 'not-specified'
      });

      // Validate request
      this.validateExecuteTestRequest(request);

      // Ensure options are provided with required fields
      if (!request.options?.aiModel) {
        throw new BadRequestError('AI model must be specified in options.aiModel');
      }

      // Get the appropriate AI model for this request
      aiModel = this.getAiModel(request.options);

      // 1. Prepare UTRs using composed service
      const utrs = await this.utrPreparationService.prepareUtrs(request);
      this.logger.info('UTRs prepared', { executionId, utrCount: utrs.length });

      // 2. Prepare prompt using injected prompt service
      const { prompt: promptTemplate, systemPrompt, promptMode, variables = {} } = await this.preparePrompt(request.prompt);
      this.logger.info('Prompt prepared', {
        executionId,
        promptLength: promptTemplate.length,
        hasSystemPrompt: !!systemPrompt,
        promptMode: promptMode || PromptMode.Extend,
        variableCount: Object.keys(variables).length
      });

      // 3. Log provider being used
      const provider = aiModel.getProvider();
      this.logger.info(`Using ${provider} Responses API`, { executionId });

      // 4. Handle file upload/reference using composed service
      const fileResult = await this.fileHandlingService.handleFileInput(request, aiModel);

      // Track temporary files for cleanup
      temporaryFileIds.push(...fileResult.temporaryFileIds);

      const uploadedDocumentIds = fileResult.fileIds;
      this.logger.info('Files handled', {
        executionId,
        fileIds: fileResult.fileIds,
        temporaryCount: fileResult.temporaryFileIds.length
      });

      // 5. Add files to user's vector store (only for OpenAI)
      let userVectorStoreId: string | undefined;
      const isOpenAI = aiModel.getProvider() === AiProvider.OpenAi;

      if (isOpenAI && userId) {
        const vectorStoreStartTime = Date.now();
        userVectorStoreId = await this.fileHandlingService.prepareVectorStore(
          userId,
          uploadedDocumentIds
        );
        aiApiTime += Date.now() - vectorStoreStartTime;
        this.logger.info('Vector store prepared', {
          executionId,
          userId: userId,
          fileCount: uploadedDocumentIds.length,
          vectorStoreId: userVectorStoreId
        });
      }

      // 6. Handle UTRs - embed in prompt for small sets, create file for large sets
      let utrsFileId: string | undefined;
      const UTR_EMBED_THRESHOLD = 50; // Embed UTRs in prompt if less than this count

      if (utrs.length <= UTR_EMBED_THRESHOLD && utrs.length > 0) {
        // Add UTRs to variables for inline embedding
        variables['inline_utrs'] = JSON.stringify(utrs);
        this.logger.info('UTRs will be embedded via inline_utrs variable', { executionId, utrCount: utrs.length });
      } else if (utrs.length > UTR_EMBED_THRESHOLD) {
        // Create UTRs file for large sets using composed service
        const utrsFileStartTime = Date.now();
        utrsFileId = await this.utrPreparationService.createUtrsFile(utrs, aiModel);
        temporaryFileIds.push(utrsFileId); // Track for cleanup
        aiApiTime += Date.now() - utrsFileStartTime;
        this.logger.info('UTRs file created', { executionId, utrsFileId });

        // Add UTR file to vector store as well
        if (isOpenAI && userId && utrsFileId) {
          const vectorStoreStartTime = Date.now();
          await this.fileHandlingService.prepareVectorStore(
            userId,
            [],
            utrsFileId
          );
          aiApiTime += Date.now() - vectorStoreStartTime;
          this.logger.info('UTR file added to vector store', {
            executionId,
            userId: userId,
            utrsFileId
          });
        }
      }

      // 7. Execute matching
      const matchingStartTime = Date.now();
      const matchingResult = await this.executeMatching({
        utrsFileId,
        uploadedDocumentIds,
        options: request.options,
        aiModel,
        promptTemplate,
        systemPrompt,
        variables,
        temporaryFileIds,  // Pass temporary files for tracking
        vectorStoreId: userVectorStoreId,  // Pass the vector store ID
      });
      aiApiTime += Date.now() - matchingStartTime;

      this.logger.info('Matching completed', { executionId, resultCount: matchingResult.results.length });

      const totalDuration = Date.now() - startTime;

      // 8. Calculate cost based on token usage and model
      const tokenCost = this.calculateTokenCost(matchingResult.tokenUsage, request.options?.aiModel);

      // 9. Prepare response
      const response: ExecuteTestResponse = {
        executionId,
        timestamp: new Date(),
        duration: totalDuration,
        results: matchingResult.results,
        metrics: {
          tokenUsage: matchingResult.tokenUsage || {
            inputTokens: 0,
            outputTokens: 0,
            totalTokens: 0
          },
          cost: tokenCost,
          executionTime: totalDuration
        },
        debug: {
          cacheHit,
          processingSteps: [
            'Request validation',
            'UTR preparation',
            'Prompt preparation',
            'Provider setup',
            'File handling',
            'UTR file creation',
            'AI matching execution',
          ],
        },
        ...(matchingResult.rawResponse ? { rawResponse: matchingResult.rawResponse } : {}),
      };

      this.logger.info('Test execution completed', {
        executionId,
        duration: totalDuration,
        resultCount: matchingResult.results.length,
        aiApiTime,
        cacheHit,
        hasRawResponse: !!matchingResult.rawResponse,
      });

      return response;

    } catch (error) {
      // Don't wrap errors that are already well-formed
      if (error instanceof UserError || error instanceof BadRequestError) {
        throw error;
      }

      // For other errors, provide context
      throw new ContextError(`AI test execution failed: ${error.message}`, {
        executionId,
        cause: error,
      });
    } finally {
      // Cleanup only temporary files (UTR files and direct uploads, not referenced files)
      await this.fileHandlingService.cleanupTemporaryFiles(temporaryFileIds, aiModel, executionId);
    }
  }

  // Prompt-related methods delegated to AITestingPromptService for backward compatibility

  /**
   * Improve a prompt using AI (delegated to prompt service)
   */
  public async improvePrompt(promptId: string) {
    return this.promptService.improvePromptTemplate(promptId);
  }

  /**
   * Improve a custom prompt using AI (delegated to prompt service)
   */
  public async improveCustomPrompt(promptContent: string) {
    return this.promptService.improveCustomPrompt(promptContent);
  }

  /**
   * Generate a prompt from a task description (delegated to prompt service)
   */
  public async generatePromptFromDescription(description: string, category?: string) {
    return this.promptService.generatePromptFromDescription(description, category);
  }

  // Private helper methods

  /**
   * Calculate token cost based on usage and model
   */
  private calculateTokenCost(
    tokenUsage?: { inputTokens: number; outputTokens: number; totalTokens: number },
    modelName?: string
  ): number {
    if (!tokenUsage || !modelName) return 0;

    // Get cost from UNIFIED_AI_MODELS configuration
    const modelConfig = UNIFIED_AI_MODELS[modelName as keyof typeof UNIFIED_AI_MODELS];
    
    if (!modelConfig || !modelConfig.cost) {
      // Fallback to default costs if model not found
      this.logger.warn('Model cost not found in UNIFIED_AI_MODELS', { modelName });
      const defaultCost = { input: 2.50, output: 10.00 };
      const inputCost = (tokenUsage.inputTokens / 1000000) * defaultCost.input;
      const outputCost = (tokenUsage.outputTokens / 1000000) * defaultCost.output;
      return inputCost + outputCost;
    }
    
    // Calculate cost (convert from per 1M tokens to actual token count)
    const inputCost = (tokenUsage.inputTokens / 1000000) * modelConfig.cost.input;
    const outputCost = (tokenUsage.outputTokens / 1000000) * modelConfig.cost.output;
    
    return inputCost + outputCost;
  }

  /**
   * Get the appropriate AI model based on the request options
   */
  private getAiModel(options: NonNullable<ExecuteTestRequest['options']>): FileSupportAiModelWithCapabilities {
    try {
      // Use the new unified factory to get the file support model
      return this.unifiedModelFactory.getFileSupportModel(options.aiModel);
    } catch (error) {
      // Throw a user-friendly error
      throw new BadRequestError(`Invalid AI model specified: ${options.aiModel}. Error: ${error.message}`);
    }
  }

  private validateExecuteTestRequest(request: ExecuteTestRequest): void {
    if (!request.fileReference && (!request.uploadedFileIds || request.uploadedFileIds.length === 0)) {
      throw new BadRequestError('Either fileReference or uploadedFileIds must be provided');
    }

    if (!request.prompt.customPrompt) {
      throw new BadRequestError('customPrompt must be provided');
    }
  }

  private async preparePrompt(promptConfig: ExecuteTestRequest['prompt']): Promise<{ prompt: string; systemPrompt?: string; promptMode?: PromptMode; variables?: Record<string, any> }> {
    const prompt = promptConfig.customPrompt!; // Already validated in validateExecuteTestRequest
    const systemPrompt = promptConfig.systemPrompt;
    const promptMode = promptConfig.promptMode;
    const variables = promptConfig.variables || {};

    // Don't apply variable substitution here - just return them
    // Variables will be applied later after we add inline_utrs
    return { prompt, systemPrompt, promptMode, variables };
  }

  /**
   * Execute the AI matching process
   */
  private async executeMatching(params: {
    utrsFileId?: string;
    uploadedDocumentIds: string[];
    options: NonNullable<ExecuteTestRequest['options']>;
    aiModel: FileSupportAiModelWithCapabilities;
    promptTemplate: string;
    systemPrompt?: string;
    variables: Record<string, any>;
    temporaryFileIds?: string[];
    vectorStoreId?: string;
  }): Promise<{ 
    results: ExecuteTestResponse['results']; 
    rawResponse?: string;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    const { utrsFileId, uploadedDocumentIds, options, aiModel, promptTemplate, variables, vectorStoreId } = params;
    const threshold = options.relevanceThreshold || DEFAULT_RELEVANCE_THRESHOLD;

    // Apply variable substitution to the prompt template
    let fullPrompt = promptTemplate;

    // Replace all variables in the template
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      fullPrompt = fullPrompt.replace(regex, String(value));
    }

    // Log if inline_utrs variable was used
    if (promptTemplate.includes('{{inline_utrs}}') && variables['inline_utrs']) {
      this.logger.info('Applied inline_utrs variable to prompt template');
    }

    const threadMessage = `
      ${fullPrompt}

      Additional Requirements:
      - Only return UTRs with relevance scores >= ${threshold}
      - ${options.includeExplanation ? 'Provide detailed explanations for each match' : 'Omit explanation field or use empty string'}
      - ${options.includeMatchedContent ? 'Include relevant text excerpts from the document in matchedContent' : 'Omit matchedContent field or use empty array'}
      - Be conservative in your matching - precision is more important than recall

      Return the results in this EXACT JSON format:
      {
        "result": [
          {
            "code": "UTR-CODE-HERE",
            "score": 0.95,
            "explanation": "Explanation of why this UTR matches",
            "matchedContent": ["Relevant excerpt 1", "Relevant excerpt 2"]
          }
        ]
      }

      Requirements for the JSON response:
      - The root object MUST have a "result" key (not "utrs" or anything else)
      - The "result" value MUST be an array of objects
      - Each object MUST have: "code" (string) and "score" (number between 0 and 1)
      - "explanation" and "matchedContent" fields can be included or omitted based on requirements above
      - When omitted, they will default to empty string and empty array respectively

      Return ONLY valid JSON, no other text or commentary.
    `.trim();

    // Use the unified executeWithFiles method
    const allFileIds = utrsFileId ? [utrsFileId, ...uploadedDocumentIds] : uploadedDocumentIds;

    // Convert to new AIFileInfo format
    const files = allFileIds.map(fileId => ({
      fileId,
      vectorStoreId: vectorStoreId // Include vector store ID in file info when available
    }));

    const executeResult = await aiModel.executeWithFiles({
      prompt: threadMessage,
      files,
      jsonSchema: executeTestResultSchema,
      systemPrompt: params.systemPrompt,
      vectorStoreId,
      modelName: options.aiModel,  // Pass the model name from options
    });
    
    const result = executeResult.data;

    interface AITestingResult {
      code?: string;
      score?: number;
      explanation?: string;
      matchedContent?: string[];
      confidence?: number;
      // Allow for alternative field names that AI might use
      utrCode?: string;
      relevanceScore?: number;
    }

    // Handle various response formats gracefully
    let results: AITestingResult[] = [];
    let rawResponse: string | undefined;

    if (result) {
      // Check if we have a raw response (from failed JSON parsing)
      // Empty string is falsy, so this handles both undefined and empty strings
      if (result.rawResponse) {
        rawResponse = result.rawResponse;
        this.logger.info('Received raw response from AI model due to parsing failure');
      }

      if (Array.isArray(result.result)) {
        // Expected format: { result: [...] }
        results = result.result;
      } else if (Array.isArray(result)) {
        // Direct array format
        results = result;
      } else if (result.results && Array.isArray(result.results)) {
        // Alternative format: { results: [...] }
        results = result.results;
      } else if (result.data && Array.isArray(result.data)) {
        // Another alternative: { data: [...] }
        results = result.data;
      } else {
        // Log unexpected format but don't throw
        this.logger.warn('Unexpected AI response format, attempting to extract results', {
          responseKeys: Object.keys(result),
          responseType: typeof result
        });

        // Try to extract any array from the response
        const arrayValue = Object.values(result).find(v => Array.isArray(v));
        if (arrayValue) {
          results = arrayValue as AITestingResult[];
        }
      }
    }

    // Normalize the results based on the requested options
    const normalizedResults = results.map((r: AITestingResult) => ({
      utrCode: r.code || r.utrCode || 'UNKNOWN',
      relevanceScore: r.score ?? r.relevanceScore ?? r.confidence ?? 0,
      // Include fields if requested - let frontend handle empty values
      ...(options.includeExplanation ? { explanation: r.explanation || '' } : {}),
      ...(options.includeMatchedContent ? { matchedContent: r.matchedContent || [] } : {}),
    }));

    return {
      results: normalizedResults,
      ...(rawResponse ? { rawResponse } : {}),
      tokenUsage: executeResult.tokenUsage
    };
  }

}

// Singleton instance
let instance: AITestingService | undefined;

export const getAITestingService = (): AITestingService => {
  if (!instance) {
    const logger = wwgLogger;
    const providerFileManager = getProviderFileManager();
    const vectorStoreService = getVectorStoreService();

    // Create composed services
    const utrPreparationService = new UTRPreparationService(UniversalTracker);
    const fileHandlingService = new AIFileHandlingService(logger, providerFileManager, vectorStoreService);

    instance = new AITestingService(
      logger,
      getUnifiedAIModelFactory(),
      getAITestingPromptService(),
      utrPreparationService,
      fileHandlingService
    );
  }
  return instance;
};
