/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import AITestExecution, { TestExecutionStatus, TestExecutionType } from '../../models/aiTestExecution';
import type { ExecuteTestRequest, ExecuteTestRequestInternal, ExecuteTestResponse, TestResult, TestMetrics } from './ai-testing-types';
import { type AITestingService, getAITestingService } from './AITestingService';
import { type AITestingPromptService, getAITestingPromptService } from './AITestingPromptService';
import BadRequestError from '../../error/BadRequestError';
import ContextError from '../../error/ContextError';
import { wwgLogger } from '../wwgLogger';
import type { Logger } from 'winston';

/**
 * Service for managing AI test executions
 */
export class AITestingExecutionService {
  private logger: Logger;

  constructor(
    private aiTestingService: AITestingService,
    private promptService: AITestingPromptService,
    logger?: Logger
  ) {
    this.logger = logger || wwgLogger.child({ service: 'AITestingExecutionService' });
  }

  /**
   * Execute an AI test (returns immediately, processes in background)
   */
  async executeTest(
    request: ExecuteTestRequest,
    userId: string
  ): Promise<{
    testId: string;
    status: string;
    sessionId: string;
    templateId?: string;
    createdAt: Date;
    completedAt?: Date;
    results?: any[];
    metadata?: any;
    duration?: number;
  }> {
    // Validate prompt configuration
    if (!request.prompt.customPrompt) {
      throw new BadRequestError('customPrompt must be provided');
    }

    this.logger.info('Executing test', {
      uploadedFileIds: request.uploadedFileIds,
      testType: request.testType,
      aiModel: request.options?.aiModel,
      templateId: request.prompt.templateId,
      userId
    });

    // Create test execution record
    const sessionId = `admin-${userId}-${Date.now()}`;
    const testExecution = new AITestExecution({
      sessionId,
      testType: request.testType || TestExecutionType.UTRMatching,
      prompt: request.prompt.customPrompt,
      promptTemplateId: request.prompt.templateId ? new ObjectId(request.prompt.templateId) : undefined, // Store the template ID if provided
      aiModel: request.options?.aiModel, // Store the AI model used
      uploadedFileIds: request.uploadedFileIds || [],
      parameters: {
        options: request.options,
        utrSelection: request.utrSelection,
        promptVariables: request.prompt.variables
      },
      status: TestExecutionStatus.Running,
      createdBy: userId
    });

    await testExecution.save();

    // Execute the test asynchronously (don't await)
    this.executeTestAsync(testExecution._id.toString(), request, userId)
      .catch(error => {
        this.logger.error(new ContextError('Failed to execute test asynchronously', {
          executionId: testExecution._id.toString(),
          cause: error
        }));
        // Error is already handled in executeTestAsync
      });

    // Return immediately with running status
    return {
      testId: testExecution._id.toString(),
      sessionId: testExecution.sessionId,
      templateId: testExecution.promptTemplateId?.toString(),
      status: 'running',
      createdAt: testExecution.created
    };
  }

  /**
   * Execute test asynchronously in the background
   */
  private async executeTestAsync(
    executionId: string,
    request: ExecuteTestRequest,
    userId: string
  ): Promise<void> {
    try {
      // Pass the execution ID to the service for consistent tracking
      const requestWithId: ExecuteTestRequestInternal = { ...request, executionId };
      
      // Execute the test using AITestingService
      const serviceResponse: ExecuteTestResponse = await this.aiTestingService.executeTest(requestWithId, userId);

      // Update test execution with results
      const testExecution = await AITestExecution.findById(executionId);
      if (!testExecution) {
        this.logger.error(new ContextError('Test execution not found for async update', { executionId }));
        return;
      }

      testExecution.results = serviceResponse.results || [];
      testExecution.status = TestExecutionStatus.Completed;
      testExecution.completedAt = new Date();
      testExecution.metrics = {
        executionTime: serviceResponse.metrics?.executionTime ?? serviceResponse.duration,
        tokenUsage: serviceResponse.metrics?.tokenUsage ?? {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          cost: 0
        },
        cost: serviceResponse.metrics?.cost ?? 0
      };

      await testExecution.save();

      // Increment usage count for prompt template if used
      if (testExecution.promptTemplateId) {
        await this.promptService.incrementUsageCount(testExecution.promptTemplateId.toString());
      }

      this.logger.info('Test execution completed', {
        executionId,
        resultsCount: serviceResponse.results?.length || 0,
        duration: serviceResponse.duration
      });
    } catch (executionError) {
      this.logger.error(new ContextError('Test execution failed', { executionId, cause: executionError }));

      // Update test execution with error
      await AITestExecution.findByIdAndUpdate(executionId, {
        status: TestExecutionStatus.Failed,
        error: executionError.message,
        completedAt: new Date()
      });
    }
  }

  /**
   * Get test execution history
   */
  async getTestHistory(params: {
    limit?: number;
    offset?: number;
    status?: string;
    testType?: string;
    startDate?: string;
    endDate?: string;
    createdBy?: string;
  }): Promise<{
    tests: Array<{
      testId: string;
      sessionId: string;
      testType: string;
      status: string;
      prompt: string;
      promptTemplateId?: string;
      promptTemplateName?: string;
      aiModel?: string;
      results?: any;
      error?: string;
      metrics?: any;
      createdBy: any;
      createdAt: Date;
      completedAt?: Date;
      duration?: number;
    }>;
    total: number;
  }> {
    const {
      limit = 20,
      offset = 0,
      status,
      testType,
      startDate,
      endDate,
      createdBy
    } = params;

    // Build query
    const query: any = {};

    if (status && typeof status === 'string') {
      if (!Object.values(TestExecutionStatus).includes(status as any)) {
        throw new BadRequestError('Invalid status. Must be one of: ' + Object.values(TestExecutionStatus).join(', '));
      }
      query.status = status;
    }

    if (testType && typeof testType === 'string') {
      if (!Object.values(TestExecutionType).includes(testType as any)) {
        throw new BadRequestError('Invalid test type. Must be one of: ' + Object.values(TestExecutionType).join(', '));
      }
      query.testType = testType;
    }

    if (startDate || endDate) {
      query.created = {};
      if (startDate && typeof startDate === 'string') {
        query.created.$gte = new Date(startDate);
      }
      if (endDate && typeof endDate === 'string') {
        query.created.$lte = new Date(endDate);
      }
    }

    if (createdBy && typeof createdBy === 'string') {
      query.createdBy = createdBy;
    }

    // Execute query with pagination
    const [tests, total] = await Promise.all([
      AITestExecution.find(query)
        .sort({ created: -1 })
        .limit(limit)
        .skip(offset)
        .populate('promptTemplateId', 'name category')
        .populate('createdBy', 'firstName lastName email')
        .lean()
        .exec(),
      AITestExecution.countDocuments(query)
    ]);

    // Convert to response format
    return {
      tests: tests.map((test) => ({
        testId: test._id.toString(),
        sessionId: test.sessionId,
        testType: test.testType,
        status: test.status,
        prompt: test.prompt.substring(0, 100) + (test.prompt.length > 100 ? '...' : ''),
        promptTemplateId: test.promptTemplateId?._id?.toString(),
        aiModel: test.aiModel, // Include the AI model used
        results: test.results?.length > 0 ? {
          count: test.results.length,
          averageScore: test.results.reduce((sum: number, r: any) => sum + r.relevanceScore, 0) / test.results.length
        } : undefined,
        error: test.error,
        metrics: test.metrics,
        createdBy: test.createdBy?.toString(),
        createdAt: test.created,
        completedAt: test.completedAt,
        duration: test.completedAt && test.created ?
          new Date(test.completedAt).getTime() - new Date(test.created).getTime() : undefined
      })),
      total,
    };
  }

  /**
   * Get test status (lightweight for polling)
   */
  async getTestStatus(id: string): Promise<{
    testId: string;
    status: string;
    createdAt: Date;
    completedAt?: Date;
    error?: string;
    results?: any[];
    duration?: number;
    aiModel?: string;
    promptTemplateId?: string;
    metrics?: {
      executionTime?: number;
      tokenUsage?: {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
        cost?: number;
      };
      cost?: number;
    };
  }> {
    const test = await AITestExecution.findById(id, {
      status: 1,
      created: 1,
      completedAt: 1,
      error: 1,
      results: 1,
      aiModel: 1,
      promptTemplateId: 1,
      metrics: 1
    })
      .lean()
      .exec();

    if (!test) {
      throw new BadRequestError(`Test execution not found: ${id}`);
    }

    return {
      testId: test._id.toString(),
      status: test.status,
      createdAt: test.created,
      completedAt: test.completedAt,
      error: test.error,
      results: test.status === TestExecutionStatus.Completed ? test.results : undefined,
      duration: test.completedAt && test.created ?
        new Date(test.completedAt).getTime() - new Date(test.created).getTime() : undefined,
      aiModel: test.aiModel,
      promptTemplateId: test.promptTemplateId?.toString(),
      metrics: test.status === TestExecutionStatus.Completed ? test.metrics : undefined
    };
  }

  /**
   * Get a specific test execution
   */
  async getTestExecution(id: string): Promise<{
    executionId: string;
    timestamp: Date;
    duration: number;
    results: TestResult[];
    metrics?: TestMetrics;
    testId: string;
    status: string;
    error?: string;
    createdAt: Date;
    completedAt?: Date;
    details: {
      sessionId?: string;
      testType: string;
      prompt?: string;
      promptTemplateId?: string;
      uploadedFileIds?: string[];
      parameters?: Record<string, unknown>;
      metrics?: TestMetrics;
      createdBy?: string;
      duration?: number;
      totalUtrCount: number;
      resultCount: number;
      averageRelevanceScore: number;
    };
  }> {

    const test = await AITestExecution.findById(id)
      .populate('uploadedFileIds', 'originalName metadata.mimetype metadata.extension')
      .lean()
      .exec();

    if (!test) {
      throw new BadRequestError(`Test execution not found: ${id}`);
    }

    // Convert to detailed response format
    return {
      executionId: test._id.toString(),
      timestamp: test.created,
      duration: test.completedAt && test.created ?
        new Date(test.completedAt).getTime() - new Date(test.created).getTime() : 0,
      results: test.results || [],
      metrics: test.metrics || {
        tokenUsage: {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0
        },
        cost: 0,
        executionTime: test.completedAt && test.created ?
          new Date(test.completedAt).getTime() - new Date(test.created).getTime() : 0
      },
      testId: test._id.toString(),
      status: test.status,
      error: test.error,
      createdAt: test.created,
      completedAt: test.completedAt,
      details: {
        sessionId: test.sessionId,
        testType: test.testType,
        prompt: test.prompt,
        promptTemplateId: test.promptTemplateId?.toString(),
        uploadedFileIds: test.uploadedFileIds,
        parameters: test.parameters,
        metrics: test.metrics,
        createdBy: test.createdBy?.toString(),
        duration: test.completedAt && test.created ?
          new Date(test.completedAt).getTime() - new Date(test.created).getTime() : undefined,
        totalUtrCount: test.parameters?.utrSelection?.filters?.codes?.length || 0,
        resultCount: test.results?.length || 0,
        averageRelevanceScore: test.results?.length > 0 ?
          test.results.reduce((sum: number, r: any) => sum + r.relevanceScore, 0) / test.results.length : 0
      }
    };
  }

  /**
   * Parse request body for test execution
   * Only supports the modern request format from the frontend
   */
  parseExecuteTestRequest(body: any): ExecuteTestRequest {
    // Validate required fields
    if (!body.prompt) {
      throw new BadRequestError('Missing required field: prompt');
    }

    if (!body.uploadedFileIds || !Array.isArray(body.uploadedFileIds) || body.uploadedFileIds.length === 0) {
      throw new BadRequestError('At least one file reference is required');
    }

    // Build clean request object with defaults
    const request: ExecuteTestRequest = {
      // File references (required)
      uploadedFileIds: body.uploadedFileIds.filter((id: unknown) => id && typeof id === 'string'),

      // UTR selection (optional)
      utrSelection: body.utrSelection ? {
        filters: body.utrSelection.filters || {}
      } : undefined,

      // Prompt configuration (required)
      prompt: {
        customPrompt: body.prompt.customPrompt,
        variables: body.prompt.variables,
        templateId: body.prompt.templateId
      },

      // Execution options with defaults
      options: {
        relevanceThreshold: body.options?.relevanceThreshold ?? 0.7,
        includeExplanation: body.options?.includeExplanation ?? true,
        includeMatchedContent: body.options?.includeMatchedContent ?? true,
        maxResults: body.options?.maxResults ?? 50,
        aiModel: body.options?.aiModel
      },

      // Test type (optional)
      testType: body.testType
    };

    // Validate prompt has customPrompt
    if (!request.prompt.customPrompt) {
      throw new BadRequestError('customPrompt must be provided');
    }

    // Validate AI model is specified
    if (!request.options?.aiModel) {
      throw new BadRequestError('AI model must be specified in options.aiModel');
    }

    return request;
  }

}

// Singleton instance
let instance: AITestingExecutionService | undefined;

export const getAITestingExecutionService = () => {
  if (!instance) {
    instance = new AITestingExecutionService(
      getAITestingService(),
      getAITestingPromptService()
    );
  }
  return instance;
};
