/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import AiUploadedFile from '../../models/aiUploadedFile';
import { ProviderFileManager, getProviderFileManager } from './ProviderFileManager';
import { FileStorageService, getFileStorageService } from './FileStorageService';
import { AIFileHandlingService } from './services/AIFileHandlingService';
import { FileData } from './ai-testing-types';
import BadRequestError from '../../error/BadRequestError';
import { wwgLogger } from '../wwgLogger';
import config from '../../config';
import ContextError from '../../error/ContextError';
import { getVectorStoreService } from './VectorStoreService';

/**
 * Service for managing AI testing file operations
 */
export class AITestingFileService {
  private logger = wwgLogger.child({ service: 'AITestingFileService' });

  constructor(
    private readonly providerFileManager: ProviderFileManager,
    private readonly fileStorage: FileStorageService,
    private readonly fileHandlingService: AIFileHandlingService
  ) {}

  // Supported file types
  private readonly SUPPORTED_MIME_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    'text/plain',
    'application/json',
    'image/png',
    'image/jpeg',
    'image/gif',
    'image/svg+xml'
  ];

  /**
   * Generate prefixed filename with environment prefix
   */
  generatePrefixedFilename(originalName: string): string {
    const prefix = config.appEnv;
    const timestamp = Date.now();
    const extension = originalName.split('.').pop();
    const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, '');
    return `${prefix}_${timestamp}_${nameWithoutExtension}.${extension}`;
  }

  /**
   * Validate file type
   */
  validateFileType(mimeType: string): boolean {
    return this.SUPPORTED_MIME_TYPES.includes(mimeType);
  }

  /**
   * Get supported MIME types
   */
  getSupportedMimeTypes(): string[] {
    return this.SUPPORTED_MIME_TYPES;
  }

  /**
   * Upload a file and create reference
   */
  async uploadFile(
    file: FileData,
    userId: string
  ): Promise<{
    fileId: string;
    fileName: string;
    prefixedName: string;
    uploadedAt: string;
  }> {
    // Generate prefixed filename
    const prefixedName = this.generatePrefixedFilename(file.originalname);

    const fileReference = await this.fileHandlingService.createFileReference({
      buffer: file.buffer,
      originalname: prefixedName,
      mimetype: file.mimetype,
      size: file.size
    }, userId);

    return {
      fileId: fileReference.referenceId,
      fileName: file.originalname,
      prefixedName: prefixedName,
      uploadedAt: fileReference.metadata.uploadedAt.toISOString(),
    };
  }

  /**
   * Get uploaded files for a user
   */
  async getUserFiles(params: {
    userId: string;
    limit?: number;
    offset?: number;
    search?: string;
  }): Promise<{
    files: Array<{
      id: string;
      originalName: string;
      prefixedName: string;
      metadata: any;
      tags: string[];
      description?: string;
      providerFiles: any;
      uploadedBy: any;
      created: Date;
    }>;
    total: number;
  }> {
    const { userId, limit = 50, offset = 0, search } = params;

    // Build query
    const query: any = {
      isActive: true,
      uploadedBy: userId,
    };

    if (search && typeof search === 'string') {
      query.$or = [
        { originalName: { $regex: search, $options: 'i' } },
        { prefixedName: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const [files, total] = await Promise.all([
      AiUploadedFile.find(query)
        .sort({ created: -1 })
        .limit(limit)
        .skip(offset)
        .populate('uploadedBy', 'name email')
        .lean()
        .exec(),
      AiUploadedFile.countDocuments(query)
    ]);

    return {
      files: files.map(file => ({
        id: file._id.toString(),
        originalName: file.originalName,
        prefixedName: file.prefixedName,
        metadata: file.metadata,
        tags: file.tags || [],
        description: file.description,
        providerFiles: file.providerFiles || {},
        uploadedBy: file.uploadedBy,
        created: file.created,
      })),
      total
    };
  }

  /**
   * Delete a file
   */
  async deleteFile(fileId: string, userId: string): Promise<{
    success: boolean;
    message: string;
    fileId: string;
    warning?: string;
  }> {
    // Find the file and ensure it belongs to the current user
    const file = await AiUploadedFile.findOne({
      _id: fileId,
      uploadedBy: userId
    });

    if (!file) {
      throw new BadRequestError('File not found or access denied');
    }

    // Check if file is already deleted
    if (!file.isActive) {
      return {
        success: true,
        message: 'File was already deleted',
        fileId: fileId
      };
    }

    try {
      // This will:
      // 1. Delete from all AI providers (OpenAI, Claude, etc.)
      // 2. Delete from cloud storage
      // 3. Mark as inactive in MongoDB
      await this.providerFileManager.deleteFile(fileId);

      return {
        success: true,
        message: 'File deleted successfully from all providers',
        fileId: fileId
      };
    } catch (deleteError) {
      // Check if the error is because file was already inactive
      const errorMessage = deleteError instanceof Error ? deleteError.message : String(deleteError);
      if (errorMessage.includes('File not found')) {
        // File was already marked as inactive, this is okay
        return {
          success: true,
          message: 'File was already deleted',
          fileId: fileId
        };
      }

      // Log the error but still return success if the file was marked inactive
      this.logger.error(new ContextError('Error during provider cleanup:', {
        cause: deleteError,
        fileId: fileId
      }));

      // If provider deletion fails, at least ensure the file is marked inactive
      if (file.isActive) {
        file.isActive = false;
        await file.save();
      }

      return {
        success: true,
        message: 'File marked as deleted (some provider cleanup may have failed)',
        fileId: fileId,
        warning: `Provider cleanup error: ${errorMessage}`
      };
    }
  }

  /**
   * Rename a file
   */
  async renameFile(fileId: string, newName: string, userId: string): Promise<void> {
    // Validate input
    if (!newName || typeof newName !== 'string' || newName.trim().length === 0) {
      throw new BadRequestError('New name is required');
    }

    // Find the file and ensure it belongs to the current user
    const file = await AiUploadedFile.findOne({
      _id: fileId,
      uploadedBy: userId,
      isActive: true
    });

    if (!file) {
      throw new BadRequestError('File not found or access denied');
    }

    // Update the original name
    file.originalName = newName.trim();
    await file.save();
  }

  /**
   * Download file from storage
   */
  async downloadFile(fileId: string, userId: string): Promise<{
    originalName: string;
    mimetype: string;
    buffer: Buffer;
  }> {
    // Find the file and ensure it belongs to the current user
    const file = await AiUploadedFile.findOne({
      _id: fileId,
      uploadedBy: userId,
      isActive: true
    });

    if (!file) {
      throw new BadRequestError('File not found or access denied');
    }

    // Download the file from storage
    const buffer = await this.fileStorage.retrieveFile(file.fileStoragePath);

    return {
      originalName: file.originalName,
      mimetype: file.metadata.mimetype,
      buffer
    };
  }


}

export const getAITestingFileService = () => {
  const providerFileManager = getProviderFileManager();
  const vectorStoreService = getVectorStoreService();
  const fileHandlingService = new AIFileHandlingService(
    wwgLogger.child({ service: 'AIFileHandlingService' }),
    providerFileManager,
    vectorStoreService
  );
  
  return new AITestingFileService(
    providerFileManager,
    getFileStorageService(),
    fileHandlingService
  );
};
