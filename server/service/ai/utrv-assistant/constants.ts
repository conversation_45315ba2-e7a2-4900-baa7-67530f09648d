import { UtrValueType } from '../../../models/public/universalTrackerType';
import { SupportedMeasureUnits } from '../../../types/units';
import { DataPeriods } from '../../../types/constants';
import type { SupportedValueType } from './types';

export const DEFAULT_MAX_TOKEN = 10000;
const ANSWER_FORMAT = 'object';
const WORD_LIMIT = 130;

export const SUPPORTED_VALUE_TYPES: SupportedValueType[] = [
  UtrValueType.Number,
  UtrValueType.Percentage,
  UtrValueType.Text,
  UtrValueType.NumericValueList,
  UtrValueType.TextValueList,
  UtrValueType.Table,
];

export const MULTI_INPUT_TYPES: SupportedValueType[] = [
  UtrValueType.TextValueList,
  UtrValueType.NumericValueList,
  UtrValueType.Table,
];

export const ROLE_PROMPT =
  `As a World Wide Generation system administrator,` +
  `your job is to help senior members of enterprises to be able to answer sustainability questions. ` +
  `The information is given in the context wrapped by triple square brackets below, ` +
  `which is a JSON object containing the information of the current question and its previous answers.\n`;

const getInputFormat = ({ extra = '', inputData }: { extra?: string; inputData: string }) => {
  return `\n[[[{
    title: it is the title of question to answer.
    type: it is the type of sustainability reporting, such as GRI, CDP, SASB, etc.
    period: it is the period of the survey containing the question, can be one of these values: ${Object.values(
      DataPeriods
    ).join(', ')}.
    ${extra}
    industry: if given, it contains the industry of the company.
    furtherExplanation: if given, it contains the further explanation or note for the question.
    instructions: if given, it contains the instructions for the question.
    previousUtrvs: it is an array of previous answers to the same question, following this JSON format:
    {
      effectiveDate: due date of the answer,
      inputData: the answer in ${inputData}.
    }
  }]]]\n`;
};

const getMultiInputFormatPromptTemplate = ({ value }: { value: string }) => {
  return {
    extra:
      'columns: an array of objects, each of them contains a column of the question, following this JSON format { code: the code of the column, name: the title of the column }',
    inputData: `an array of objects containing previous values of the columns, following this format: { code: the column code, mapped with the code in the columns field to get the title of the column, value: ${value} }`,
  };
};

const getTableFormatPromptTemplate = () => {
  return {
    extra:
      'columns: an array of objects, each of them contains a column of the question, following this JSON format { code: the code of the column, name: the title of the column }',
    inputData: `an array of objects containing previous values of the columns, following this format:
      {
        code: the column code which is mapped with the code in the columns field to get the title of the column,
        options: it may exist or be undefined, it is an array of items having this format: { code: the code of list item, name: the name of list item },
        value: 'if the options property exists, the answer must not be an object
          and it is based on the name of a mapped item in options with code equals the current value,
          in case the current value does not exist, then randomly select an option.
          Otherwise, the answer is based on the type of the column'
      }`,
  };
};

export const INPUT_FORMAT_PROMPT: {
  [key in SupportedValueType]: string;
} = {
  [UtrValueType.Number]: getInputFormat({
    extra: `unitType: if given, it is the unit of measurement of the answer, can be one of these values: ${Object.values(
      SupportedMeasureUnits
    ).join(', ')}.
    unit: if given, the unit for the unitType, it is the unit of measurement of the answer.
  `,
    inputData: 'number',
  }),
  [UtrValueType.Percentage]: getInputFormat({ inputData: 'percentage' }),
  [UtrValueType.Text]: getInputFormat({ inputData: 'text' }),
  [UtrValueType.TextValueList]: getInputFormat(
    getMultiInputFormatPromptTemplate({ value: 'the text answer of the column' })
  ),
  [UtrValueType.NumericValueList]: getInputFormat(
    getMultiInputFormatPromptTemplate({ value: 'the numeric answer of the column' })
  ),
  [UtrValueType.Table]: getInputFormat(getTableFormatPromptTemplate()),
};

const getExplanationPrompt = () => {
  return (
    `explain the question in ${WORD_LIMIT} words or less, using simple, everyday language ` +
    `about why this information matters for sustainability reporting so it becomes easier to understand`
  );
};

const getKeyInformationNeededPrompt = () => {
  return (
    'describe the key information needed, formatted as an array, focusing on these points: list the specific data points required, ' +
    'include any calculation methods if applicable, note any specific time periods or boundaries that matter '
  );
};

const getWhereToFindPrompt = () => {
  return (
    'describe where to find the data to the question, from internal sources and external sources, like this example: ' +
    `{
      internalSource: [
        Utility bills and energy consumption records,
        Facilities management reports,
        Purchase records,
        Equipment maintenance logs,
        EHS (Environmental Health & Safety) documentation,
        Operations reports
      ],
      externalSource: [
        Third-party consultant reports,
        Verification statements,
        Government submissions,
        Industry association data
      ]
    }`
  );
};

const getSuggestedEvidencePrompt = () => {
  return (
    'suggest the evidence files, grouped by primary documentation and supporting documentation, like this example: ' +
    `{
      primaryDocumentation: [
        Raw data spreadsheets,
        Calculation worksheets,
        Third-party verification reports,
        Official certificates or permits,
      ],
      supportingDocumentation: [
        Internal policies and procedures,
        Training records,
        Meeting minutes,
        Progress tracking documents,
        Methodology statements
      ]
    }`
  );
};

const getBestPracticePrompt = () => {
  return (
    'suggest the best practice tips, formatted as an array, focusing on these points: include practical tips for data collection, ' +
    'note any industry standards to reference, suggest ways to improve data quality'
  );
};

const getCommandPromptTemplate = ({ answer }: { answer: string }) => {
  const contextPromts = [
    '\nBased on the context',
    getExplanationPrompt(),
    getKeyInformationNeededPrompt(),
    getWhereToFindPrompt(),
    getSuggestedEvidencePrompt(),
    getBestPracticePrompt(),
    `and then predict the ${answer}.`,
  ].join(',');

  return `${contextPromts} Refer to yourself as "we" or "our", not "Our company" or "Our organization".`;
};

const getCommandPrompt = (valueType: UtrValueType) => {
  switch (valueType) {
    case UtrValueType.TextValueList:
    case UtrValueType.NumericValueList:
    case UtrValueType.Table:
      return getCommandPromptTemplate({
        answer:
          `answer for each column in the "columns" field. The predicted answer should be formatted in ${ANSWER_FORMAT} ` +
          `with keys are the column codes and values are predicted value for each corresponding column. `,
      });
    case UtrValueType.Text:
    case UtrValueType.Number:
    case UtrValueType.Percentage:
    default:
      return getCommandPromptTemplate({
        answer: `${valueType} answer for the current question. `,
      });
  }
};

export const COMMAND_PROMPT: {
  [key in SupportedValueType]: string;
} = {
  [UtrValueType.Number]: getCommandPrompt(UtrValueType.Number),
  [UtrValueType.Percentage]: getCommandPrompt(UtrValueType.Percentage),
  [UtrValueType.Text]: getCommandPrompt(UtrValueType.Text),
  [UtrValueType.NumericValueList]: getCommandPrompt(UtrValueType.NumericValueList),
  [UtrValueType.TextValueList]: getCommandPrompt(UtrValueType.TextValueList),
  [UtrValueType.Table]: getCommandPrompt(UtrValueType.Table),
};

export const ANSWER_TEMPLATE =
  `Your response must always be formatted in ${ANSWER_FORMAT} with keys "questionExplanation" ` +
  `(your question explanation), "keyInfo" (key information needed), "whereToFind" (where to find the answer), ` +
  `"bestPractice" (best practice), "suggestedEvidence" (suggested evidence), and "predictedAnswer" (the answer that you predicted, ` +
  `if it's number or percentage, must be single number, if it's an object, do not add any comment).`;
