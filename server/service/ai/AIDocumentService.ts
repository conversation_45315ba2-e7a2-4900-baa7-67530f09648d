import { AIModelType, getAIModelFactory } from './AIModelFactory';
import { ChatGPT } from './models/ChatGPT';
import { DocumentPlain } from '../../models/document';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { downloadFile } from '../file/file';
import { getStorage } from '../storage/fileStorage';
import { FileSupportAiModel } from './models/FileSupportAiModel';

export class AIDocumentService {
  constructor(
    private aiModel: FileSupportAiModel,
    private logger: LoggerInterface,
    private fileStorage: ReturnType<typeof getStorage>,
    private fileSystem: Pick<typeof fs, 'createReadStream' | 'unlinkSync'>,
  ) {}

  public async uploadDocument(document: Pick<DocumentPlain, '_id' | 'metadata' | 'path'>) {
    this.logger.info('Uploading document', { documentId: document._id });

    const tempFilePath = path.join('/tmp', `${document._id}-${uuidv4()}.${document.metadata?.extension || ''}`);

    // Download the file
    const [signedUrl] = await this.fileStorage.getSignedUrl(document.path);
    await downloadFile(signedUrl, tempFilePath);

    try {
      const uploadedFile = await this.aiModel.createFile({
        file: this.fileSystem.createReadStream(tempFilePath),
        purpose: 'user_data',
      });
      this.logger.info('Document uploaded', { documentId: document._id, uploadedFile });
      return {
        documentId: document._id,
        uploadedFileId: uploadedFile.id,
      };
    } finally {
      this.fileSystem.unlinkSync(tempFilePath);
    }
  }

  public async cleanupDocument({ documentId, fileId }: { documentId: string; fileId: string }) {
    this.logger.info('Deleting uploaded document', { documentId, fileId });
    return this.aiModel.deleteFile(fileId);
  }

}

let instance: AIDocumentService | undefined;
export const getAIDocumentService = () => {
  if (!instance) {
    instance = new AIDocumentService(
      getAIModelFactory().getModel(AIModelType.ChatGPT) as ChatGPT,
      wwgLogger,
      getStorage(),
      fs,
    );
  }
  return instance;
};
