/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Types } from 'mongoose';
import AITestExecution from '../../models/aiTestExecution';
import { PromptTemplate, CreatePromptTemplateRequest, UpdatePromptTemplateRequest, PromptImprovement, isValidPromptTemplateCategory } from './ai-testing-types';
import { PromptTemplateCategory } from '../../models/aiPromptTemplate';
import { PromptMode } from '../../types/aiTesting';
import BadRequestError from '../../error/BadRequestError';
import ContextError from '../../error/ContextError';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import { getUnifiedAIModelFactory } from './UnifiedAIModelFactory';
import { PromptTemplateRepository } from './repositories/PromptTemplateRepository';

/**
 * Service for managing AI prompt templates and related operations
 */
export class AITestingPromptService {
  private logger: LoggerInterface;
  private promptTemplates: Map<string, PromptTemplate> = new Map();

  constructor(
    private promptRepository: PromptTemplateRepository,
    private modelFactory: ReturnType<typeof getUnifiedAIModelFactory>,
    logger: LoggerInterface
  ) {
    this.logger = logger.child({ service: 'AITestingPromptService' });
    this.initializeDefaultTemplates();
  }

  /**
   * Initialize default prompt templates with system prompts
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates: PromptTemplate[] = [
      {
        id: 'default-gri',
        name: 'GRI Standards Matching',
        content: `
          Focus on GRI sustainability indicators and standards. Look for:
          - Quantitative metrics and KPIs
          - Environmental, social, and governance data
          - Specific GRI disclosure requirements
          - Materiality and stakeholder engagement information

          Be particularly attentive to:
          - Water and energy consumption data
          - Waste and emissions figures
          - Employee and diversity metrics
          - Supply chain and human rights information
        `,
        systemPrompt: 'You are an expert in GRI (Global Reporting Initiative) sustainability standards and reporting frameworks. You have deep knowledge of GRI disclosure requirements, materiality assessment, and stakeholder engagement practices. Analyze documents with focus on quantitative sustainability metrics and their alignment with GRI standards.',
        promptMode: PromptMode.Override,
        description: 'Optimized for GRI sustainability reporting standards',
        category: PromptTemplateCategory.General,
        variables: [],
        tags: ['gri', 'sustainability', 'default'],
        isPublic: true,
        isActive: true,
        isDefault: true,
        created: new Date(),
        updated: new Date(),
        version: 1,
        usageCount: 0,
      },
      {
        id: 'default-tcfd',
        name: 'TCFD Climate Reporting',
        content: `
          Focus on climate-related financial disclosures following TCFD framework:
          - Governance around climate risks and opportunities
          - Strategy and climate-related risks/opportunities
          - Risk management processes
          - Metrics and targets for climate performance

          Look specifically for:
          - Climate scenario analysis
          - Physical and transition risks
          - Carbon footprint and emissions data
          - Climate-related financial impacts
        `,
        systemPrompt: 'You are a climate risk and TCFD (Task Force on Climate-related Financial Disclosures) specialist with expertise in climate scenario analysis, physical and transition risks, and climate-related financial disclosure requirements. Focus on identifying climate governance structures, risk management processes, and quantitative climate metrics.',
        description: 'Focused on climate-related financial disclosures',
        category: PromptTemplateCategory.General,
        variables: [],
        tags: ['tcfd', 'climate', 'default'],
        isPublic: true,
        isActive: true,
        isDefault: true,
        created: new Date(),
        updated: new Date(),
        version: 1,
        usageCount: 0,
      },
      {
        id: 'default-general',
        name: 'General Sustainability Matching',
        content: `Analyze the provided {{document_type}} and identify content that matches Universal Tracker (UTR) requirements.

Focus on {{analysis_focus}} with a minimum confidence threshold of {{min_confidence}}%.

Please examine:
- Quantitative data and measurements
- Environmental impact metrics
- Social responsibility indicators
- Governance and ethical practices
- Stakeholder engagement information

Provide results that include:
1. UTR matches with confidence scores
2. Supporting evidence from the document
3. Any gaps or missing information

{{additional_instructions}}`,
        systemPrompt: 'You are a sustainability expert with broad knowledge across ESG (Environmental, Social, and Governance) frameworks and reporting standards. You excel at identifying and extracting quantitative sustainability metrics, stakeholder engagement practices, and governance structures from documents.',
        description: 'General purpose template for sustainability reporting',
        category: PromptTemplateCategory.General,
        variables: ['document_type', 'analysis_focus', 'min_confidence', 'additional_instructions'],
        tags: ['general', 'sustainability', 'default'],
        isPublic: true,
        isActive: true,
        isDefault: true,
        created: new Date(),
        updated: new Date(),
        version: 1,
        usageCount: 0,
      },
    ];

    defaultTemplates.forEach(template => {
      this.promptTemplates.set(template.id, template);
    });
  }

  /**
   * Get default prompt templates
   */
  async getDefaultPrompts(): Promise<PromptTemplate[]> {
    return Array.from(this.promptTemplates.values()).filter(template => template.isDefault);
  }

  /**
   * Get all prompt templates (default + user created)
   */
  async getAllPromptTemplates(): Promise<PromptTemplate[]> {
    // Get default templates from memory
    const defaultTemplates = Array.from(this.promptTemplates.values());

    // Get user-created templates from database
    try {
      const dbTemplates = await this.promptRepository.findAllActive();
      const convertedDbTemplates = dbTemplates.map(template => this.convertToPromptTemplate(template));

      return [...defaultTemplates, ...convertedDbTemplates];
    } catch (error) {
      this.logger.warn('Failed to load database templates, returning defaults only', { error: error.message });
      return defaultTemplates;
    }
  }

  /**
   * Get a specific prompt template by ID
   */
  async getPromptTemplate(id: string): Promise<PromptTemplate | null> {
    // Check default templates first (these use nanoid IDs)
    const defaultTemplate = this.promptTemplates.get(id);
    if (defaultTemplate) {
      return defaultTemplate;
    }

    // Check database for user templates
    try {
      const template = await this.promptRepository.findById(id);
      if (template) {
        return this.convertToPromptTemplate(template);
      }
    } catch (error) {
      this.logger.warn('Failed to load template from database', { id, error: error.message });
    }

    return null;
  }

  /**
   * Create a new prompt template (saved to database)
   */
  async createPromptTemplate(request: CreatePromptTemplateRequest, userId?: string): Promise<PromptTemplate> {
    // Validation
    if (!request.name || !request.content) {
      throw new BadRequestError('name and content are required');
    }

    if (!request.category || !isValidPromptTemplateCategory(request.category)) {
      throw new BadRequestError('Invalid category. Must be one of: ' + Object.values(PromptTemplateCategory).join(', '));
    }

    // Extract variables from template content
    const extractedVariables = this.extractVariablesFromTemplate(request.content);

    // Create new prompt template in database
    const templateData = {
      name: request.name,
      description: request.description,
      content: request.content,
      systemPrompt: request.systemPrompt,
      category: request.category as PromptTemplateCategory,
      variables: Array.from(extractedVariables), // Store as array
      tags: request.tags || [],
      isPublic: request.isPublic || false,
      isActive: true,
      createdBy: userId ? new Types.ObjectId(userId) : undefined,
      usageCount: 0,
      version: 1,
      created: new Date(),
      updated: new Date(),
    };

    const savedTemplate = await this.promptRepository.create(templateData);
    const result = this.convertToPromptTemplate(savedTemplate.toObject ? savedTemplate.toObject() : savedTemplate);

    this.logger.info('Prompt template created', {
      templateId: result.id,
      name: result.name
    });

    return result;
  }

  /**
   * Update an existing prompt template
   */
  async updatePromptTemplate(id: string, request: UpdatePromptTemplateRequest, userId: string): Promise<PromptTemplate> {
    // 1. Check if it's a default template
    const defaultTemplate = this.promptTemplates.get(id);
    if (defaultTemplate) {
      throw new BadRequestError('Cannot modify default prompt templates');
    }

    // 2. Load the template
    const template = await this.promptRepository.findByIdAsDocument(id);
    if (!template) {
      throw new BadRequestError(`Prompt template not found: ${id}`);
    }

    // 3. Verify ownership
    if (template.createdBy.toString() !== userId) {
      throw new BadRequestError('You do not have permission to update this template');
    }

    // 4. Update fields
    if (request.name !== undefined) {
      template.name = request.name;
    }
    if (request.description !== undefined) {
      template.description = request.description;
    }
    if (request.systemPrompt !== undefined) {
      template.systemPrompt = request.systemPrompt;
    }
    if (request.content !== undefined) {
      template.content = request.content;
      template.variables = Array.from(this.extractVariablesFromTemplate(request.content));
    }
    if (request.category !== undefined) {
      if (!isValidPromptTemplateCategory(request.category)) {
        throw new BadRequestError('Invalid category. Must be one of: ' + Object.values(PromptTemplateCategory).join(', '));
      }
      template.category = request.category;
    }
    if (request.tags !== undefined) {
      template.tags = request.tags;
    }
    if (request.isPublic !== undefined) {
      template.isPublic = request.isPublic;
    }

    // 5. Save
    await template.save();
    
    const result = this.convertToPromptTemplate(template);
    this.logger.info('Prompt template updated', {
      templateId: id,
      name: result.name
    });

    return result;
  }

  /**
   * Delete a prompt template
   */
  async deletePromptTemplate(id: string, userId: string): Promise<boolean> {
    // 1. Check if it's a default template
    const defaultTemplate = this.promptTemplates.get(id);
    if (defaultTemplate) {
      throw new BadRequestError('Cannot delete default prompt templates');
    }

    // 2. Load the template
    const template = await this.promptRepository.findByIdAsDocument(id);
    if (!template) {
      throw new BadRequestError(`Prompt template not found: ${id}`);
    }

    // 3. Verify ownership
    if (template.createdBy.toString() !== userId) {
      throw new BadRequestError('You do not have permission to delete this template');
    }

    // 4. Check if in use
    const testsUsingTemplate = await AITestExecution.countDocuments({ promptTemplateId: id });
    if (testsUsingTemplate > 0) {
      throw new BadRequestError(`Cannot delete prompt template. It is being used by ${testsUsingTemplate} test execution(s).`);
    }

    // 5. Delete
    await template.deleteOne();
    
    this.logger.info('Prompt template deleted', { templateId: id });
    return true;
  }

  /**
   * Get prompt templates with filtering (includes both default and database templates)
   */
  async getPromptTemplates(params: {
    category?: string;
    isActive?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ prompts: PromptTemplate[]; total: number }> {
    const { category, isActive, search, limit = 50, offset = 0 } = params;

    // Get default templates
    let defaultTemplates = Array.from(this.promptTemplates.values());

    // Filter default templates
    if (category) {
      defaultTemplates = defaultTemplates.filter(t => t.category === category);
    }
    if (isActive !== undefined) {
      defaultTemplates = defaultTemplates.filter(t => t.isActive === isActive);
    }
    if (search) {
      const searchLower = search.toLowerCase();
      defaultTemplates = defaultTemplates.filter(t =>
        t.name.toLowerCase().includes(searchLower) ||
        (t.description || '').toLowerCase().includes(searchLower)
      );
    }

    // Get database templates
    const query: any = {};
    if (category && typeof category === 'string') {
      query.category = category;
    }
    if (isActive !== undefined) {
      query.isActive = isActive;
    }
    if (search && typeof search === 'string') {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const [dbTemplates] = await Promise.all([
      this.promptRepository.findWithQuery(query, { created: -1 })
    ]);

    const convertedDbTemplates = dbTemplates.map(template => this.convertToPromptTemplate(template));

    // Combine and sort all templates
    const allTemplates = [...defaultTemplates, ...convertedDbTemplates]
      .sort((a, b) => {
        // Default templates first, then by creation date
        if (a.isDefault && !b.isDefault) {
          return -1;
        }
        if (!a.isDefault && b.isDefault) {
          return 1;
        }
        return new Date(b.created).getTime() - new Date(a.created).getTime();
      });

    // Apply pagination
    const total = allTemplates.length;
    const prompts = allTemplates.slice(offset, offset + limit);

    return { prompts, total };
  }





  /**
   * Improve a prompt template using AI
   */
  async improvePromptTemplate(id: string): Promise<PromptImprovement> {
    const template = await this.getPromptTemplate(id);
    if (!template) {
      throw new BadRequestError(`Prompt template not found: ${id}`);
    }

    try {
      this.logger.info('Improving prompt with Anthropic API', { promptId: id, promptName: template.name });

      const improverPrompt = `You are an expert prompt engineer. Your task is to improve the following prompt for sustainability reporting and document analysis.

Original Prompt:
${template.content}

Please provide an improved version that is:
- Clearer and more specific
- Better structured for AI understanding
- More effective for matching sustainability documents to UTRs (Universal Trackers)
- Includes relevant examples or context where helpful

IMPORTANT: If the prompt contains variables in the format {{variableName}}, you MUST preserve them exactly as {{variableName}} in your improved version. Do NOT change them to [variableName] or any other format.

Return ONLY the improved prompt text without any explanations, metadata, or JSON formatting. Just the improved prompt text itself.`;

      // Use Claude 4 with larger context window for prompt improvement
      const claudeModel = this.modelFactory.getModel('claude-sonnet-4-20250514');
      const response = await claudeModel.runCompletion([
        { role: 'system', content: 'You are an expert prompt engineer specializing in sustainability reporting and document analysis. Return only the improved prompt text without any additional formatting or explanation. IMPORTANT: Preserve all variables in {{variableName}} format exactly as they appear.' },
        { role: 'user', content: improverPrompt }
      ], 10000);

      let improvedPrompt = response.content.trim();

      // Post-process to ensure variables are in the correct format
      // Convert any [variable] back to {{variable}}
      improvedPrompt = improvedPrompt.replace(/\[(\w+)\]/g, '{{$1}}');

      this.logger.info('Prompt improved successfully', {
        promptId: id,
        originalLength: template.content.length,
        improvedLength: improvedPrompt.length
      });

      return {
        original: template.content,
        improved: improvedPrompt
      };

    } catch (error) {
      throw new ContextError(`Failed to improve prompt: ${error.message}`, {
        promptId: id,
        cause: error
      });
    }
  }

  /**
   * Generate a prompt from description
   */
  async generatePromptFromDescription(
    description: string,
    category?: string
  ): Promise<{
    prompt: string;
    name: string;
    variables: string[];
  }> {
    if (!description || typeof description !== 'string' || description.trim().length === 0) {
      throw new BadRequestError('Description is required');
    }

    // Validate category if provided
    if (category && !isValidPromptTemplateCategory(category)) {
      throw new BadRequestError('Invalid category. Must be one of: ' + Object.values(PromptTemplateCategory).join(', '));
    }

    try {
      this.logger.info('Generating prompt from description', {
        descriptionLength: description.length,
        category
      });

      const generatorPrompt = `You are an expert prompt engineer. Generate a professional prompt template for the following task in sustainability reporting:

Task Description:
${description}

${category ? `Category: ${category}` : ''}

Create a prompt that:
1. Is optimized for matching sustainability documents to UTRs (Universal Trackers)
2. Includes clear instructions and structure
3. Uses variables (in {{variable}} format) where appropriate
4. Is specific and actionable

Return your response in this JSON format:
{
  "prompt": "the generated prompt template",
  "name": "a short descriptive name for the prompt",
  "variables": ["list", "of", "variables", "used"]
}`;

      // Use Claude 4 with larger context window for prompt generation
      const claudeModel = this.modelFactory.getModel('claude-sonnet-4-20250514');
      const response = await claudeModel.runCompletion([
        { role: 'system', content: 'You are an expert prompt engineer specializing in sustainability reporting and document analysis. Always respond with valid JSON.' },
        { role: 'user', content: generatorPrompt }
      ], 10000);

      let generatedData: {
        prompt: string;
        name: string;
        variables: string[];
      };

      try {
        generatedData = JSON.parse(response.content);
      } catch (parseError) {
        throw new Error(`Failed to parse AI response as JSON: ${parseError.message}`);
      }

      if (!generatedData) {
        throw new Error('AI model returned null response');
      }

      if (!generatedData.prompt || !generatedData.name) {
        throw new Error('AI model returned incomplete response - missing prompt or name');
      }

      this.logger.info('Prompt generated successfully', {
        promptName: generatedData.name,
        variableCount: generatedData.variables?.length || 0
      });

      return {
        prompt: generatedData.prompt,
        name: generatedData.name,
        variables: generatedData.variables || []
      };

    } catch (error) {
      throw new ContextError(`Failed to generate prompt: ${error.message}`, {
        description,
        cause: error
      });
    }
  }

  /**
   * Improve a custom prompt using AI
   */
  async improveCustomPrompt(promptContent: string): Promise<PromptImprovement> {
    if (!promptContent || promptContent.length === 0) {
      throw new BadRequestError('Prompt content is required');
    }

    try {
      this.logger.info('Improving custom prompt with Anthropic API', {
        promptLength: promptContent.length
      });

      const improverPrompt = `You are an expert prompt engineer. Your task is to improve the following prompt for sustainability reporting and document analysis.

Original Prompt:
${promptContent}

Please provide an improved version that is:
- Clearer and more specific
- Better structured for AI understanding
- More effective for matching sustainability documents to UTRs (Universal Trackers)
- Includes relevant examples or context where helpful
- Maintains the original intent and variables

IMPORTANT: If the prompt contains variables in the format {{variableName}}, you MUST preserve them exactly as {{variableName}} in your improved version. Do NOT change them to [variableName] or any other format.

Return ONLY the improved prompt text without any explanations, metadata, or JSON formatting. Just the improved prompt text itself.`;

      const claudeModel = this.modelFactory.getModel('claude-sonnet-4-20250514');
      const response = await claudeModel.runCompletion([
        { role: 'system', content: 'You are an expert prompt engineer specializing in sustainability reporting and document analysis. Return only the improved prompt text without any additional formatting or explanation. IMPORTANT: Preserve all variables in {{variableName}} format exactly as they appear.' },
        { role: 'user', content: improverPrompt }
      ], 10000);

      let improvedPrompt = response.content.trim();

      // Post-process to ensure variables are in the correct format
      // Convert any [variable] back to {{variable}}
      improvedPrompt = improvedPrompt.replace(/\[(\w+)\]/g, '{{$1}}');

      return {
        original: promptContent,
        improved: improvedPrompt
      };

    } catch (error) {
      throw new ContextError(`Failed to improve custom prompt: ${error.message}`, {
        cause: error
      });
    }
  }

  /**
   * Increment usage count for a prompt template
   */
  async incrementUsageCount(templateId: string): Promise<void> {
    await this.promptRepository.incrementUsageCount(templateId);
  }

  /**
   * Helper method to extract variables from template content
   */
  private extractVariablesFromTemplate(content: string): Set<string> {
    const variablePattern = /\{\{(\w+)\}\}/g;
    const variables = new Set<string>();
    let match;
    while ((match = variablePattern.exec(content)) !== null) {
      variables.add(match[1]);
    }
    return variables;
  }

  /**
   * Convert database model to PromptTemplate type
   */
  private convertToPromptTemplate(template: any): PromptTemplate {
    return {
      id: template._id.toString(),
      name: template.name,
      content: template.content,
      systemPrompt: template.systemPrompt,
      description: template.description || '',
      category: template.category,
      variables: template.variables || [],
      tags: template.tags || [],
      isPublic: template.isPublic || false,
      isActive: template.isActive,
      isDefault: false,
      createdBy: template.createdBy?.toString(),
      created: template.created,
      updated: template.updated,
      version: template.version || 1,
      usageCount: template.usageCount || 0,
    };
  }
}

// Singleton instance
let instance: AITestingPromptService | undefined;

export const getAITestingPromptService = () => {
  if (!instance) {
    const promptRepository = new PromptTemplateRepository();
    instance = new AITestingPromptService(
      promptRepository,
      getUnifiedAIModelFactory(),
      wwgLogger
    );
  }
  return instance;
};
