import ContextError from '../../error/ContextError';
import { type ResponseFormat } from '../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { type AIModel, type AIPrompt, type AIResponse } from './models/AIModel';

// Represents a single step in our chain.
export interface ChainStep<T = any> {
  model: AIModel;
  maxTokens?: number;
  format?: ResponseFormat;
  generatePrompt: (response?: AIResponse<T>) => Promise<AIPrompt | undefined>;
}

export class PromptChainBuilder {
  private readonly steps: ChainStep[] = [];

  constructor(steps: ChainStep[]) {
    if (steps) {
      this.steps = steps;
    }
  }

  public addStep(newStep: ChainStep): PromptChainBuilder {
    this.steps.push(newStep);
    return this;
  }

  public async execute<T>(): Promise<AIResponse<T>> {
    if (this.steps.length === 0) {
      throw new ContextError('Cannot execute an empty chain. Add steps using .addStep()');
    }

    let currentResponse;

    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];

      const prompt = await step.generatePrompt(currentResponse);

      if (!prompt) {
        break;
      }

      const response = await step.model.parseCompletion([prompt], step.maxTokens, step.format);

      currentResponse = response;
    }

    return currentResponse as AIResponse<T>;
  }
}
