import type { ObjectId } from 'bson';
import { PromptTemplateCategory } from '../../models/aiPromptTemplate';
import { PromptMode } from '../../types/aiTesting';

// Core data types
export interface FileData {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

export interface UTRPropertyFilter {
  path: string;
  operator: 'equals' | 'contains' | 'exists' | 'notExists' | 'in' | 'notIn' | 'regex' | 'gt' | 'lt' | 'gte' | 'lte' | 'startsWith' | 'endsWith' | 'arrayContains' | 'arraySize' | 'isNull' | 'isNotNull' | 'typeEquals';
  value?: any;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date';
  arrayIndex?: number;
  arrayElementPath?: string;
  caseSensitive?: boolean;
  options?: {
    multiline?: boolean;
    ignoreCase?: boolean;
    global?: boolean;
  };
}

export interface UTRFilters {
  // Basic filters
  type?: string[];
  codes?: string[];
  valueType?: string[];
  blueprintCodes?: string[];
  ownerId?: ObjectId[];

  // Advanced property filters
  properties?: UTRPropertyFilter[];

  // Text search
  textSearch?: {
    query: string;
    fields?: string[];
    options?: {
      caseSensitive?: boolean;
      wholeWord?: boolean;
      fuzzy?: boolean;
    };
  };

  // Date range filters
  dateRange?: {
    field: string;
    start?: Date;
    end?: Date;
  };

  // Array filtering
  arrayFilters?: {
    path: string;
    elementFilters: UTRPropertyFilter[];
    matchType?: 'all' | 'any' | 'none';
  }[];

  // Aggregation options
  aggregation?: {
    groupBy?: string[];
    having?: UTRPropertyFilter[];
    orderBy?: { field: string; direction: 'asc' | 'desc' }[];
  };

  // Pagination
  limit?: number;
  offset?: number;

  // Projection
  projection?: string[];

  // Cache options
  useCache?: boolean;
  cacheTtl?: number;
}


export interface PromptConfiguration {
  customPrompt: string; // Required - the actual prompt content
  systemPrompt?: string; // Optional system prompt to customize AI behavior
  promptMode?: PromptMode; // How system prompt interacts with base prompt (default: extend)
  variables?: Record<string, any>;
  templateId?: string; // Optional ID of the template used to generate this prompt
}

export interface ExecutionOptions {
  relevanceThreshold?: number;
  includeExplanation?: boolean;
  includeMatchedContent?: boolean;
  maxResults?: number;
  aiModel: string; // Required - Model name like 'gpt-4', 'claude-3-5-sonnet', 'gemini-1.5-pro'
  maxTokens?: number; // Maximum tokens for AI response
}

// Request/Response types
export interface ExecuteTestRequest {
  // File source (one of the following)
  fileReference?: string;
  uploadedFileIds?: string[]; // MongoDB ObjectIds of AiUploadedFile documents

  // UTR source
  utrSelection?: {
    filters: UTRFilters;
  };
  utrs?: string[]; // Legacy: direct UTR codes

  // Prompt configuration
  prompt: PromptConfiguration;

  // Execution options
  options?: ExecutionOptions;

  // Test metadata
  testType?: string; // Type of test being executed

  // User information
  userId?: ObjectId; // User ID for vector store association
}

// Internal interface with required executionId for service-to-service calls
export interface ExecuteTestRequestInternal extends ExecuteTestRequest {
  executionId: string; // Required execution ID for tracking
}

export interface TestResult {
  utrCode: string;
  relevanceScore: number;
  explanation?: string;
  matchedContent?: string[];
  confidence?: number;
}

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost?: number;
}

export interface TestMetrics {
  tokenUsage?: TokenUsage;
  cost?: number;
  executionTime?: number;
}

export interface ExecuteTestResponse {
  executionId: string;
  timestamp: Date;
  duration: number;

  results: TestResult[];

  metrics?: TestMetrics;

  debug?: {
    cacheHit: boolean;
    processingSteps?: string[];
  };

  // Raw response from AI when JSON parsing fails
  rawResponse?: string;
}

export interface TypeFilter {
  type: string;
  typeTags?: string[];
}

export interface SearchUtrsRequest {
  query?: string;
  types?: TypeFilter[];
  filters?: UTRFilters;
  properties?: UTRPropertyFilter[]; // For property-based filtering
  projection?: string[];
  limit?: number;
  offset?: number;
  sort?: Record<string, 1 | -1>;
}

export interface UTRSearchResult {
  id: string;
  name: string;
  code: string;
  type: string;
  typeCode?: string;
  description: string;
  valueLabel: string;
  valueType: string;
  matchedVia?: string;  // Indicates which standard/framework this UTR matched through via alternatives
  properties?: {
    valueValidation?: any;
    tags?: string[];
    blueprintCodes?: string[];
    valueListOptions?: any[];
    alternatives?: Record<string, any>;
  };
}

export interface SearchUtrsResponse {
  utrs: UTRSearchResult[];
  total: number;
  cached: boolean;
  query?: any;
  executionTime?: number;
}

export interface FileReferenceRequest {
  file: FileData;
  metadata?: {
    name?: string;
    description?: string;
    tags?: string[];
    expiresAt?: Date;
  };
}

export interface FileReferenceResponse {
  referenceId: string;
  metadata: {
    name: string;
    size: number;
    type: string;
    uploadedAt: Date;
    expiresAt?: Date;
    tags?: string[];
  };
}

export interface PromptTemplate {
  id: string;
  name: string;
  content: string;
  systemPrompt?: string; // Optional system prompt for the template
  promptMode?: PromptMode; // How system prompt interacts with base prompt
  description?: string;
  category?: PromptTemplateCategory;
  variables: string[];
  tags?: string[];
  isPublic?: boolean;
  isActive?: boolean;
  isDefault?: boolean;
  createdBy?: string;
  created: Date;
  updated: Date;
  version?: number;
  usageCount?: number;
  metadata?: {
    parameters?: Array<{
      name: string;
      type: string;
      required: boolean;
      description?: string;
      defaultValue?: any;
    }>;
    [key: string]: any;
  };
}

export interface CreatePromptTemplateRequest {
  name: string;
  description?: string;
  content: string; // Frontend sends 'content' field
  systemPrompt?: string; // Optional system prompt
  promptMode?: PromptMode; // How system prompt interacts with base prompt
  category?: PromptTemplateCategory;
  tags?: string[];
  isPublic?: boolean;
}

export interface UpdatePromptTemplateRequest {
  name?: string;
  description?: string;
  content?: string; // Frontend sends 'content' field
  systemPrompt?: string; // Optional system prompt
  promptMode?: PromptMode; // How system prompt interacts with base prompt
  category?: PromptTemplateCategory;
  tags?: string[];
  isPublic?: boolean;
}

// Cache-related types
export interface CachedAssistant {
  assistant: any;
  expires: Date;
  promptHash: string;
  usageCount: number;
}

export interface CachedUTRQuery {
  results: any[];
  total: number;
  timestamp: Date;
  queryHash: string;
}

// Statistics and monitoring types

export interface TestSession {
  id: string;
  userId: ObjectId;
  createdAt: Date;
  lastActivity: Date;

  // Session data
  recentExecutions: string[];
  savedPrompts: string[];
  fileReferences: Map<string, FileReferenceResponse>;

  // Statistics
  totalExecutions: number;
  totalTokensUsed: number;
  averageExecutionTime: number;
}

// Error types
export interface AITestingError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  executionId?: string;
}

// Validation types
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Export enums
export enum FilterOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'notEquals',
  CONTAINS = 'contains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  EXISTS = 'exists',
  NOT_EXISTS = 'notExists',
  IN = 'in',
  NOT_IN = 'notIn',
  GREATER_THAN = 'gt',
  LESS_THAN = 'lt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN_OR_EQUAL = 'lte',
  REGEX = 'regex'
}

// PromptCategory enum removed - use PromptTemplateCategory from models/aiPromptTemplate.ts instead

// Type guard function to check if a string is a valid PromptTemplateCategory
export function isValidPromptTemplateCategory(value: string): value is PromptTemplateCategory {
  return Object.values(PromptTemplateCategory).includes(value as PromptTemplateCategory);
}

export enum TestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Prompt improvement types
export interface PromptImprovement {
  original: string;
  improved: string;
}

export interface GeneratePromptRequest {
  description: string;
  category?: PromptTemplateCategory;
}

export interface GeneratePromptResponse {
  prompt: string;
  name: string;
  variables: string[];
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
