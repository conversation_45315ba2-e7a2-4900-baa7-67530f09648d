/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Types } from 'mongoose';
import BadRequestError from '../../../error/BadRequestError';
import ContextError from '../../../error/ContextError';
import { type LoggerInterface } from '../../wwgLogger';
import type { ExecuteTestRequest, FileData, FileReferenceResponse } from '../ai-testing-types';
import type { FileSupportAiModelWithCapabilities } from '../models/FileSupportAiModel';
import type { ProviderFileManager } from '../ProviderFileManager';
import type { VectorStoreService } from '../VectorStoreService';
import { ObjectId } from 'bson';

/**
 * Service responsible for handling file operations in AI testing
 */
export class AIFileHandlingService {
  constructor(
    private logger: LoggerInterface,
    private providerFileManager: ProviderFileManager,
    private vectorStoreService: VectorStoreService
  ) { }

  /**
   * Handle file input from various sources
   */
  async handleFileInput(
    request: ExecuteTestRequest,
    aiModel: FileSupportAiModelWithCapabilities
  ): Promise<{
    fileIds: string[];
    temporaryFileIds: string[];
  }> {
    const fileIds: string[] = [];
    const temporaryFileIds: string[] = [];

    // Handle file references (single or multiple)
    const fileReferences = [
      ...(request.fileReference ? [request.fileReference] : []),
      ...(request.uploadedFileIds || [])
    ];

    // Resolve all file references in parallel
    const resolvedIds = await Promise.all(
      fileReferences.map(ref => this.resolveFileReference(ref, aiModel))
    );
    fileIds.push(...resolvedIds);

    if (fileIds.length === 0) {
      throw new BadRequestError('No file input provided');
    }

    return { fileIds, temporaryFileIds };
  }

  /**
   * Resolve a file reference to a provider-specific file ID
   */
  async resolveFileReference(
    fileReferenceId: string,
    aiModel: FileSupportAiModelWithCapabilities
  ): Promise<string> {
    try {
      const provider = aiModel.getProvider();

      // Use ProviderFileManager for all providers (now includes Claude adapter)
      const providerFileId = await this.providerFileManager.getProviderFile(
        fileReferenceId,
        provider,
        {
          autoReupload: true // Enable automatic re-upload on missing/expired files
        }
      );

      this.logger.info('Resolved file reference', {
        mongoId: fileReferenceId,
        provider,
        providerFileId
      });

      return providerFileId;
    } catch (error) {
      throw new ContextError('File reference resolution failed', {
        cause: error,
        fileReferenceId,
        provider: aiModel.getProvider()
      });
    }
  }

  /**
   * Create a file reference for reuse
   */
  async createFileReference(fileData: FileData, userId: string): Promise<FileReferenceResponse> {
    try {
      this.logger.info('Creating file reference', {
        filename: fileData.originalname,
        size: fileData.size,
        type: fileData.mimetype,
        userId
      });

      // Use ProviderFileManager to upload file with multi-provider support
      const userObjectId = new Types.ObjectId(userId);

      const uploadResult = await this.providerFileManager.uploadFile(
        {
          buffer: fileData.buffer,
          originalname: fileData.originalname,
          mimetype: fileData.mimetype,
          size: fileData.size
        },
        userObjectId,
        {
          description: 'AI Testing file upload',
          tags: ['ai-testing']
        }
      );

      const response: FileReferenceResponse = {
        referenceId: uploadResult.mongoId.toString(),
        metadata: {
          name: fileData.originalname,
          size: fileData.size,
          type: fileData.mimetype,
          uploadedAt: new Date(),
        },
      };

      this.logger.info('File reference created', {
        referenceId: response.referenceId,
        filename: fileData.originalname
      });

      return response;

    } catch (error) {
      throw new ContextError('File reference creation failed', {
        filename: fileData.originalname,
        cause: error,
      });
    }
  }

  /**
   * Clean up temporary files created during test execution
   */
  async cleanupTemporaryFiles(
    fileIds: string[],
    aiModel: FileSupportAiModelWithCapabilities | undefined,
    executionId: string
  ): Promise<void> {
    if (!aiModel || fileIds.length === 0) return;

    await Promise.all(
      fileIds.map(async (fileId) => {
        try {
          await aiModel.deleteFile(fileId);
          this.logger.info('Cleaned up temporary file', { executionId, fileId });
        } catch (error) {
          this.logger.warn('Failed to cleanup temporary file', {
            executionId,
            fileId,
            error: error.message
          });
        }
      })
    );
  }

  /**
   * Prepare vector store for OpenAI file operations
   */
  async prepareVectorStore(
    userId: string,
    uploadedFileIds: string[],
    utrsFileId?: string
  ): Promise<string | undefined> {
    const userObjectId = new ObjectId(userId);

    // Get or create user's vector store
    const vectorStoreId = await this.vectorStoreService.getOrCreateUserStore(userObjectId);

    // Add uploaded files to vector store
    if (uploadedFileIds.length > 0) {
      await this.vectorStoreService.addFilesToUserStore(userObjectId, uploadedFileIds);
      this.logger.info('Files added to vector store', {
        vectorStoreId,
        fileCount: uploadedFileIds.length
      });
    }

    // Add UTR file to vector store if provided
    if (utrsFileId) {
      await this.vectorStoreService.addFilesToUserStore(userObjectId, [utrsFileId]);
      this.logger.info('UTR file added to vector store', {
        vectorStoreId,
        utrsFileId
      });
    }

    return vectorStoreId;
  }
}
