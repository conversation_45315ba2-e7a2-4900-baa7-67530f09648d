/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { Logger } from 'winston';
import type { Types } from 'mongoose';
import type { AiUploadedFileModel } from '../../models/aiUploadedFile';
import AiUploadedFile from '../../models/aiUploadedFile';
import ContextError from '../../error/ContextError';
import UserError from '../../error/UserError';
import type { FileProviderAdapter } from './adapters/FileProviderAdapter';
import type { FileStorageService } from './FileStorageService';
import { getFileStorageService } from './FileStorageService';
import type { AdapterFactory } from './adapters/AdapterFactory';
import { DefaultAdapterFactory } from './adapters/AdapterFactory';
import { wwgLogger } from '../wwgLogger';
import { getErrorMessage } from '../../util/errorUtils';
import type { AiProvider } from './types';

export interface ProviderFileReference {
  fileId: string;
  uploadedAt: Date;
}

export interface FileUploadResult {
  mongoId: Types.ObjectId;
}

export interface FileRetrievalOptions {
  preferredProvider?: AiProvider;
  autoReupload?: boolean;
}

/**
 * ProviderFileManager - Manages file uploads and references across multiple AI providers
 *
 * Features:
 * - Provider-agnostic file storage using FileStorageService
 * - Lazy loading of provider-specific files
 * - Automatic re-upload on 404 errors
 * - Support for multiple AI providers (OpenAI, Claude, Gemini)
 */
export class ProviderFileManager {
  private adapters: Map<string, FileProviderAdapter> = new Map();

  constructor(
    private fileStorage: FileStorageService,
    private logger: Logger,
    private adapterFactory: AdapterFactory
  ) {}

  /**
   * Get or create adapter for a provider
   */
  protected getAdapter(provider: AiProvider): FileProviderAdapter {
    // Return existing adapter if already initialized
    const existing = this.adapters.get(provider);
    if (existing) {
      return existing;
    }

    // Lazily create adapter using factory
    const adapter = this.adapterFactory.createAdapter(provider, this.logger);
    this.adapters.set(provider, adapter);
    return adapter;
  }

  /**
   * Upload a file and store with provider-agnostic reference
   */
  async uploadFile(
    fileData: { buffer: Buffer; originalname: string; mimetype: string; size: number },
    userId: Types.ObjectId,
    metadata?: { description?: string; tags?: string[] }
  ): Promise<FileUploadResult> {
    // Store the file in cloud storage
    const storageResult = await this.fileStorage.storeFile(fileData, userId.toString());

    // Generate unique prefixed name
    const timestamp = Date.now();
    const prefixedName = `${timestamp}_${fileData.originalname}`;

    // Create MongoDB record with file storage path
    const fileRecord = new AiUploadedFile({
      originalName: fileData.originalname,
      prefixedName,
      uploadedBy: userId,
      fileStoragePath: storageResult.storagePath,
      tags: metadata?.tags || [],
      description: metadata?.description,
      providerFiles: {},
      metadata: {
        mimetype: fileData.mimetype,
        size: fileData.size,
        extension: this.getFileExtension(fileData.originalname),
        uploadedAt: new Date()
      },
      isActive: true
    });

    await fileRecord.save();

    return {
      mongoId: fileRecord._id
    };
  }

  /**
   * Get file reference for a specific provider, uploading if necessary
   */
  async getProviderFile(
    mongoFileId: string | Types.ObjectId,
    provider: AiProvider,
    options?: FileRetrievalOptions
  ): Promise<string> {
    const fileRecord = await this.getFileRecord(mongoFileId);

    // Check if we already have the file for this provider
    const existingFile = fileRecord.providerFiles?.[provider];
    if (existingFile) {
      // Verify file still exists with provider
      const adapter = this.getAdapter(provider);
      const exists = await adapter.fileExists(existingFile.fileId);
      if (exists) {
        return existingFile.fileId;
      }
      // File doesn't exist or verification failed, will re-upload
    }

    // Upload file to provider if not exists or verification failed
    if (options?.autoReupload !== false) {
      return await this.uploadToProvider(fileRecord, provider);
    }

    throw new UserError(`File not available for provider: ${provider}`, {
      mongoId: fileRecord._id,
      provider
    });
  }

  /**
   * Upload file to a specific provider
   */
  private async uploadToProvider(
    fileRecord: AiUploadedFileModel,
    provider: AiProvider
  ): Promise<string> {
    const adapter = this.getAdapter(provider);

    // Check if we have the file storage path
    if (!fileRecord.fileStoragePath) {
      throw new UserError('File storage path not available', {
        mongoId: fileRecord._id,
        provider
      });
    }

    let providerFile: ProviderFileReference | null = null;

    try {
      // Retrieve file from storage
      const buffer = await this.fileStorage.retrieveFile(fileRecord.fileStoragePath);

      const fileData = {
        buffer,
        originalname: fileRecord.originalName,
        mimetype: fileRecord.metadata.mimetype,
        size: fileRecord.metadata.size
      };

      providerFile = await adapter.uploadFile(fileData);

      // Update record with new provider file
      fileRecord.providerFiles[provider] = {
        fileId: providerFile.fileId,
        uploadedAt: providerFile.uploadedAt
      };

      await fileRecord.save();

      return providerFile.fileId;

    } catch (error) {
      // Cleanup uploaded file if upload succeeded but save failed
      if (providerFile) {
        try {
          await adapter.deleteFile(providerFile.fileId);
        } catch (cleanupError) {
          // Ignore cleanup errors
          this.logger.debug('Cleanup after upload failure failed', {
            providerFileId: providerFile.fileId,
            provider,
            error: getErrorMessage(cleanupError)
          });
        }
      }

      throw new ContextError(`Failed to upload file to ${provider}`, {
        mongoId: fileRecord._id,
        provider,
        cause: error
      });
    }
  }

  /**
   * Delete file from all providers and cloud storage
   */
  async deleteFile(mongoFileId: string | Types.ObjectId): Promise<void> {
    const fileRecord = await this.getFileRecord(mongoFileId);

    // Delete from all providers
    const providerFiles = fileRecord.providerFiles || {};
    for (const [provider, fileRef] of Object.entries(providerFiles)) {
      try {
        const adapter = this.getAdapter(provider as AiProvider);
        await adapter.deleteFile(fileRef.fileId);
      } catch (error) {
        // Continue with other deletions even if one fails
        this.logger.warn(`Failed to delete file from ${provider} provider during cleanup`, {
          provider,
          providerFileId: fileRef.fileId,
          error: getErrorMessage(error)
        });
      }
    }

    // Delete from cloud storage
    if (fileRecord.fileStoragePath) {
      try {
        await this.fileStorage.deleteFile(fileRecord.fileStoragePath);
      } catch (error) {
        // Continue even if storage deletion fails
        this.logger.warn('Failed to delete file from cloud storage during cleanup', {
          storagePath: fileRecord.fileStoragePath,
          storageProvider: 'cloud storage',
          error: getErrorMessage(error)
        });
      }
    }

    // Mark as inactive in MongoDB
    fileRecord.isActive = false;
    await fileRecord.save();
  }

  /**
   * Get file record from MongoDB
   */
  protected async getFileRecord(mongoFileId: string | Types.ObjectId): Promise<AiUploadedFileModel> {
    const fileRecord = await AiUploadedFile.findOne({
      _id: mongoFileId,
      isActive: true
    });

    if (!fileRecord) {
      throw new UserError(`File not found: ${mongoFileId}`);
    }

    return fileRecord;
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.slice(lastDotIndex).toLowerCase() : '';
  }


  /**
   * Get file content from storage
   */
  async getFileContent(mongoFileId: string | Types.ObjectId): Promise<Buffer> {
    const fileRecord = await this.getFileRecord(mongoFileId);

    if (!fileRecord.fileStoragePath) {
      throw new UserError('File storage path not available', {
        mongoId: fileRecord._id
      });
    }

    return await this.fileStorage.retrieveFile(fileRecord.fileStoragePath);
  }

  /**
   * Get a signed URL for direct file access
   */
  async getFileSignedUrl(mongoFileId: string | Types.ObjectId, expirationMinutes: number = 60): Promise<string> {
    const fileRecord = await this.getFileRecord(mongoFileId);

    if (!fileRecord.fileStoragePath) {
      throw new UserError('File storage path not available', {
        mongoId: fileRecord._id
      });
    }

    return await this.fileStorage.getSignedUrl(fileRecord.fileStoragePath, expirationMinutes);
  }
}

// Singleton instance
let instance: ProviderFileManager | undefined;

export const getProviderFileManager = (): ProviderFileManager => {
  if (!instance) {
    instance = new ProviderFileManager(
      getFileStorageService(),
      wwgLogger,
      new DefaultAdapterFactory()
    );
  }
  return instance;
};
