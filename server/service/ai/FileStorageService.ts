/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { FileStorageInterface, getStorage } from '../storage/fileStorage';
import ContextError from '../../error/ContextError';
import UserError from '../../error/UserError';
import * as path from 'path';
import * as crypto from 'crypto';
import * as fs from 'fs/promises';
import BadRequestError from '../../error/BadRequestError';
import { FileValidationService, getFileValidationService } from './FileValidationService';

export interface FileData {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

export interface StoredFileResult {
  storagePath: string;
  url?: string;
  size: number;
}

// File size limits configuration
interface FileSizeLimits {
  default: number;
  byType: Record<string, number>;
  byUser?: Record<string, number>;
}

const FILE_SIZE_LIMITS: FileSizeLimits = {
  default: 50 * 1024 * 1024, // 50MB default
  byType: {
    'application/pdf': 100 * 1024 * 1024,      // 100MB for PDFs
    'image/png': 10 * 1024 * 1024,             // 10MB for PNGs
    'image/jpeg': 10 * 1024 * 1024,            // 10MB for JPEGs
    'image/jpg': 10 * 1024 * 1024,             // 10MB for JPGs
    'text/csv': 200 * 1024 * 1024,             // 200MB for CSVs
    'text/plain': 50 * 1024 * 1024,            // 50MB for text files
    'application/json': 50 * 1024 * 1024,      // 50MB for JSON
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 100 * 1024 * 1024, // 100MB for Excel
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 50 * 1024 * 1024 // 50MB for Word
  }
};

/**
 * FileStorageService - Handles file storage and retrieval for AI uploaded files
 * 
 * This service manages the actual file storage in cloud storage (Google Cloud Storage or Azure)
 * separately from the provider-specific file uploads (OpenAI, Claude, Gemini).
 * 
 * Benefits:
 * - Files are stored permanently in our cloud storage
 * - No dependency on provider file expiration policies
 * - Enables re-upload to any provider when needed
 * - Reduces storage in MongoDB (no Buffer fields)
 */
export class FileStorageService {
  private readonly basePath = 'ai-uploaded-files';

  constructor(
    private storage: FileStorageInterface,
    private fileValidator: FileValidationService,
    private logger: Logger
  ) {}

  /**
   * Generate a unique storage path for a file
   */
  private generateStoragePath(originalName: string, userId: string): string {
    // Validate userId to prevent injection
    if (!userId.match(/^[a-f0-9]{24}$/i)) {
      throw new BadRequestError('Invalid user ID format');
    }
    
    // Prevent path traversal attempts
    if (originalName.includes('..') || originalName.includes('//') || 
        originalName.includes('\\') || originalName.startsWith('/')) {
      throw new BadRequestError('Invalid filename: potential path traversal detected');
    }
    
    const timestamp = Date.now();
    const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);
    const extension = path.extname(originalName);
    const basename = path.basename(originalName, extension);
    
    // Sanitize basename more thoroughly
    const cleanBasename = basename
      .replace(/[^a-zA-Z0-9-_]/g, '_')  // Remove special chars
      .replace(/_{2,}/g, '_')           // Replace multiple underscores
      .replace(/^_|_$/g, '')            // Remove leading/trailing underscores
      .substring(0, 50);
    
    // Validate and clean extension
    const cleanExtension = extension.toLowerCase().replace(/[^a-z0-9.]/g, '');
    if (cleanExtension && cleanExtension.length > 10) {
      throw new BadRequestError('Invalid file extension: too long');
    }
    
    // Structure: ai-uploaded-files/YYYY/MM/userId/timestamp-hash-filename.ext
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    
    return `${this.basePath}/${year}/${month}/${userId}/${timestamp}-${hash}-${cleanBasename}${cleanExtension}`;
  }

  /**
   * Validate file size against configured limits
   */
  private validateFileSize(fileData: FileData, userId?: string): void {
    const typeLimit = FILE_SIZE_LIMITS.byType[fileData.mimetype] || FILE_SIZE_LIMITS.default;
    const userLimit = userId && FILE_SIZE_LIMITS.byUser?.[userId];
    const effectiveLimit = userLimit || typeLimit;
    
    if (fileData.size > effectiveLimit) {
      const limitMB = Math.round(effectiveLimit / (1024 * 1024));
      const sizeMB = Math.round(fileData.size / (1024 * 1024));
      throw new BadRequestError(
        `File size ${sizeMB}MB exceeds limit of ${limitMB}MB for type ${fileData.mimetype}`
      );
    }
  }
  
  /**
   * Store a file in cloud storage
   */
  async storeFile(fileData: FileData, userId: string): Promise<StoredFileResult> {
    try {
      // Validate file size
      this.validateFileSize(fileData, userId);
      
      // Validate file content (magic numbers, malicious content, etc.)
      const validationResult = await this.fileValidator.validateFile(
        fileData.buffer,
        fileData.mimetype,
        fileData.originalname
      );
      
      if (!validationResult.valid) {
        throw new BadRequestError(
          `File validation failed: ${validationResult.errors.join('; ')}`
        );
      }
      
      // Log warnings if any
      if (validationResult.warnings.length > 0) {
        this.logger.warn('File validation warnings', {
          filename: fileData.originalname,
          warnings: validationResult.warnings
        });
      }
      
      const storagePath = this.generateStoragePath(fileData.originalname, userId);
      
      this.logger.info('Storing file in cloud storage', {
        originalName: fileData.originalname,
        size: fileData.size,
        storagePath,
        detectedType: validationResult.detectedType || fileData.mimetype
      });

      // Upload the file to cloud storage
      const result = await this.storage.streamBufferUpload({
        path: storagePath,
        contents: fileData.buffer,
      });

      this.logger.info('File stored successfully', {
        storagePath: result.path,
        size: fileData.size,
      });

      return {
        storagePath: result.path,
        size: fileData.size,
      };
    } catch (error) {
      throw new ContextError('Failed to store file in cloud storage', {
        filename: fileData.originalname,
        cause: error,
      });
    }
  }

  /**
   * Retrieve a file from cloud storage
   */
  async retrieveFile(storagePath: string): Promise<Buffer> {
    // Validate storage path to prevent path traversal
    if (!this.isValidStoragePath(storagePath)) {
      throw new BadRequestError('Invalid storage path');
    }
    
    try {
      this.logger.debug('Retrieving file from cloud storage', { storagePath });

      // Use a temporary file approach that works with all storage providers
      const tmpDir = path.join(process.cwd(), 'tmp', 'ai-downloads');
      await fs.mkdir(tmpDir, { recursive: true });
      
      // Generate unique temporary filename
      const timestamp = Date.now();
      const randomId = crypto.randomBytes(8).toString('hex');
      const filename = path.basename(storagePath);
      const tmpPath = path.join(tmpDir, `${timestamp}-${randomId}-${filename}`);
      
      try {
        // Download file to temporary location
        await this.storage.downloadFile(storagePath, tmpPath);
        
        // Read the downloaded file into a buffer
        const buffer = await fs.readFile(tmpPath);
        
        // Clean up temporary file immediately after reading
        try {
          await fs.unlink(tmpPath);
        } catch (cleanupError) {
          this.logger.warn('Failed to clean up temporary file', {
            tmpPath,
            error: cleanupError.message
          });
        }
        
        this.logger.debug('File retrieved successfully', {
          storagePath,
          size: buffer.length,
        });

        return buffer;
      } catch (error) {
        // Ensure cleanup even on error
        try {
          await fs.unlink(tmpPath);
        } catch { 
          // Ignore cleanup errors
        }
        
        throw error;
      }
    } catch (error) {
      // Distinguish between different error types
      if (error.code === 404 || error.message.includes('No such object') || error.message.includes('not found')) {
        throw new UserError(`File not found in storage: ${storagePath}`, {
          status: 404,
          storagePath,
          cause: error
        });
      }
      
      // Network/timeout errors
      if (error.code === 'ETIMEDOUT' || error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        throw new ContextError('Storage service temporarily unavailable', {
          status: 503,
          storagePath,
          retryable: true,
          cause: error
        });
      }
      
      // Permission errors
      if (error.code === 403 || error.message.includes('Forbidden') || error.message.includes('Access denied')) {
        throw new UserError('Access denied to storage file', {
          status: 403,
          storagePath,
          cause: error
        });
      }
      
      throw new ContextError('Failed to retrieve file from cloud storage', {
        storagePath,
        cause: error,
      });
    }
  }

  /**
   * Delete a file from cloud storage
   */
  async deleteFile(storagePath: string): Promise<void> {
    // Validate storage path to prevent path traversal
    if (!this.isValidStoragePath(storagePath)) {
      throw new BadRequestError('Invalid storage path');
    }
    
    try {
      this.logger.info('Deleting file from cloud storage', { storagePath });

      await this.storage.remove(storagePath);
      
      this.logger.info('File deleted successfully', { storagePath });
    } catch (error) {
      // Log but don't throw - file might already be deleted
      this.logger.warn('Failed to delete file from cloud storage', {
        storagePath,
        error: error.message,
      });
    }
  }

  /**
   * Get a signed URL for direct file access
   */
  async getSignedUrl(storagePath: string, expirationMinutes: number = 60): Promise<string> {
    // Validate storage path to prevent path traversal
    if (!this.isValidStoragePath(storagePath)) {
      throw new BadRequestError('Invalid storage path');
    }
    
    try {
      const expirationTimestamp = Date.now() + (expirationMinutes * 60 * 1000);
      const urls = await this.storage.getSignedUrl(storagePath, { expireTimestamp: expirationTimestamp });
      
      return urls[0];
    } catch (error) {
      throw new ContextError('Failed to generate signed URL', {
        storagePath,
        cause: error,
      });
    }
  }

  /**
   * Check if a file exists in storage
   */
  async fileExists(storagePath: string): Promise<boolean> {
    // Validate storage path to prevent path traversal
    if (!this.isValidStoragePath(storagePath)) {
      throw new BadRequestError('Invalid storage path');
    }
    
    try {
      // Try to get file metadata - if it throws, file doesn't exist
      await this.storage.downloadFile(storagePath, storagePath);
      return true;
    } catch (error) {
      if (error.code === 404 || error.message.includes('No such object')) {
        return false;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Copy a file to a new location
   */
  async copyFile(sourcePath: string, destinationPath: string): Promise<StoredFileResult> {
    // Validate both paths to prevent path traversal
    if (!this.isValidStoragePath(sourcePath) || !this.isValidStoragePath(destinationPath)) {
      throw new BadRequestError('Invalid storage path');
    }
    
    try {
      this.logger.info('Copying file in cloud storage', {
        source: sourcePath,
        destination: destinationPath,
      });

      const result = await this.storage.copyFile({
        source: sourcePath,
        destination: destinationPath,
      });

      // Get file size by downloading it (not ideal, but necessary)
      const [buffer] = await this.storage.downloadFile(destinationPath, destinationPath);

      return {
        storagePath: result.path,
        size: buffer.length,
      };
    } catch (error) {
      throw new ContextError('Failed to copy file in cloud storage', {
        source: sourcePath,
        destination: destinationPath,
        cause: error,
      });
    }
  }

  /**
   * Validate storage path to prevent path traversal attacks
   */
  private isValidStoragePath(storagePath: string): boolean {
    // Check for null bytes
    if (storagePath.includes('\0')) {
      return false;
    }

    // Check for path traversal patterns
    const pathTraversalPatterns = [
      '../',
      '..\\',
      '..%2F',
      '..%5C',
      '%2E%2E%2F',
      '%2E%2E%5C',
      '..%252F',
      '..%255C'
    ];

    const normalizedPath = storagePath.toLowerCase();
    for (const pattern of pathTraversalPatterns) {
      if (normalizedPath.includes(pattern.toLowerCase())) {
        return false;
      }
    }

    // Ensure path starts with expected prefix
    const validPrefixes = [
      'ai-testing/', 
      'uploads/ai/', 
      'tmp/ai/',
      'ai-uploaded-files/' // Legacy prefix still in use
    ];
    const hasValidPrefix = validPrefixes.some(prefix => storagePath.startsWith(prefix));
    
    if (!hasValidPrefix) {
      this.logger.warn('Storage path does not have valid prefix', { storagePath });
      return false;
    }

    return true;
  }
}

// Singleton instance
let instance: FileStorageService | undefined;

export const getFileStorageService = (): FileStorageService => {
  if (!instance) {
    instance = new FileStorageService(
      getStorage(),
      getFileValidationService(),
      wwgLogger
    );
  }
  return instance;
};