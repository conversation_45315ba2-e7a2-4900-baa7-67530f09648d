/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { <PERSON><PERSON> } from 'winston';
import type { FileProviderAdapter } from './FileProviderAdapter';
import type { ChatGPT } from '../models/ChatGPT';
import ContextError from '../../../error/ContextError';

/**
 * OpenAIFileAdapter - Implements file operations for OpenAI provider
 * 
 * Features:
 * - File upload with retry logic
 * - 404 error detection and recovery
 * - File existence verification
 * - Expiration tracking (OpenAI files expire after 7 days)
 */
export class OpenAIFileAdapter implements FileProviderAdapter {
  // OpenAI files do not automatically expire - they are stored until manually deleted
  private readonly OPENAI_FILE_EXPIRY_DAYS = null; // No automatic expiration

  constructor(
    private aiModel: ChatGPT,
    private logger: Logger
  ) {}

  /**
   * Upload a file to OpenAI
   */
  async uploadFile(fileData: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  }): Promise<{
    fileId: string;
    uploadedAt: Date;
    expiresAt?: Date;
  }> {
    this.logger.debug('Uploading file to OpenAI', {
      filename: fileData.originalname,
      size: fileData.size,
      mimetype: fileData.mimetype
    });

    // Convert buffer to File object for OpenAI API
    const file = new File([fileData.buffer], fileData.originalname, {
      type: fileData.mimetype,
    });

    // Upload to OpenAI
    const uploadedFile = await this.aiModel.createFile({
      file,
      purpose: 'assistants'
    });

    const uploadedAt = new Date();
    // OpenAI files don't expire automatically
    const expiresAt = undefined;

    this.logger.info('File uploaded to OpenAI successfully', {
      fileId: uploadedFile.id,
      filename: fileData.originalname,
      expiresAt
    });

    return {
      fileId: uploadedFile.id,
      uploadedAt,
      expiresAt
    };
  }

  /**
   * Delete a file from OpenAI
   */
  async deleteFile(fileId: string): Promise<void> {
    try {
      this.logger.debug('Deleting file from OpenAI', { fileId });
      
      await this.aiModel.deleteFile(fileId);
      
      this.logger.info('File deleted from OpenAI', { fileId });
    } catch (error) {
      // Check if error is 404 (file already deleted)
      if (this.is404Error(error)) {
        this.logger.warn('File already deleted from OpenAI', { fileId });
        return;
      }
      
      throw new ContextError('OpenAI file deletion failed', {
        fileId,
        cause: error
      });
    }
  }

  /**
   * Check if a file exists with OpenAI
   */
  async fileExists(fileId: string): Promise<boolean> {
    try {
      this.logger.debug('Checking file existence in OpenAI', { fileId });
      
      // Try to retrieve file info
      const fileInfo = await this.getFileInfo(fileId);
      
      return !!fileInfo;
    } catch (error) {
      // For any error, assume file doesn't exist
      this.logger.debug('File not found or error checking existence', { fileId });
      return false;
    }
  }

  /**
   * Get file metadata from OpenAI
   */
  async getFileInfo(fileId: string): Promise<{
    id: string;
    name: string;
    size: number;
    createdAt: Date;
    expiresAt?: Date;
  } | null> {
    try {
      this.logger.debug('Retrieving file info from OpenAI', { fileId });
      
      // Use the new retrieveFile method if available
      if (this.aiModel.retrieveFile) {
        const fileObject = await this.aiModel.retrieveFile(fileId);
        if (!fileObject) {
          return null;
        }
        
        return {
          id: fileObject.id,
          name: fileObject.filename || 'unknown',
          size: fileObject.bytes || 0,
          createdAt: new Date(fileObject.created_at * 1000),
          expiresAt: undefined // OpenAI files don't expire automatically
        };
      }
      
      // Fallback for models that don't implement retrieveFile yet
      this.logger.warn('Model does not implement retrieveFile, using fallback', {
        provider: this.aiModel.getProvider()
      });
      
      return {
        id: fileId,
        name: 'unknown',
        size: 0,
        createdAt: new Date(),
        expiresAt: undefined
      };
    } catch (error) {
      if (this.is404Error(error)) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Check if an error is a 404 Not Found error
   */
  private is404Error(error: unknown): boolean {
    // Type guard for error with status
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;
      
      // Check for common 404 error patterns
      if (errorObj.status === 404) return true;
      if (errorObj.code === 'file_not_found') return true;
      
      // Check message field
      if (typeof errorObj.message === 'string') {
        if (errorObj.message.toLowerCase().includes('not found')) return true;
        if (errorObj.message.includes('404')) return true;
      }
      
      // Check for OpenAI-specific error format
      if (typeof errorObj.error === 'object' && errorObj.error !== null) {
        const nestedError = errorObj.error as Record<string, unknown>;
        if (nestedError.code === 'file_not_found') return true;
      }
    }
    
    return false;
  }
}