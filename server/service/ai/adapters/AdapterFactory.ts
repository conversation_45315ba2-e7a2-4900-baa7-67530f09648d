/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { Logger } from 'winston';
import type { FileProviderAdapter } from './FileProviderAdapter';
import { OpenAIFileAdapter } from './OpenAIFileAdapter';
import { ClaudeFileAdapter } from './ClaudeFileAdapter';
import { GeminiFileAdapter } from './GeminiFileAdapter';
import { getChatGPT } from '../models/ChatGPT';
import { getClaudeAI } from '../models/ClaudeAI';
import { getGeminiAI } from '../models/GeminiAI';
import UserError from '../../../error/UserError';
import { AiProvider } from '../types';

export interface AdapterFactory {
  createAdapter(provider: AiProvider, logger: Logger): FileProviderAdapter;
}

export class DefaultAdapterFactory implements AdapterFactory {
  createAdapter(provider: AiProvider, logger: Logger): FileProviderAdapter {
    switch (provider) {
      case AiProvider.OpenAi: {
        const openaiModel = getChatGPT();
        return new OpenAIFileAdapter(openaiModel, logger);
      }
      case AiProvider.Claude: {
        const claudeModel = getClaudeAI();
        return new ClaudeFileAdapter(claudeModel, logger);
      }
      case AiProvider.Gemini: {
        const geminiModel = getGeminiAI();
        return new GeminiFileAdapter(geminiModel, logger);
      }
      default: {
        throw new UserError(`Provider not supported: ${provider}`);
      }
    }
  }
}