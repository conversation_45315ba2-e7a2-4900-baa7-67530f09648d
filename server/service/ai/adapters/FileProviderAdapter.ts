/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export interface FileProviderAdapter {
  /**
   * Upload a file to the provider
   */
  uploadFile(fileData: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  }): Promise<{
    fileId: string;
    uploadedAt: Date;
    expiresAt?: Date;
  }>;

  /**
   * Delete a file from the provider
   */
  deleteFile(fileId: string): Promise<void>;

  /**
   * Check if a file exists with the provider
   */
  fileExists(fileId: string): Promise<boolean>;

  /**
   * Get file metadata from the provider
   */
  getFileInfo?(fileId: string): Promise<{
    id: string;
    name: string;
    size: number;
    createdAt: Date;
    expiresAt?: Date;
  } | null>;
}