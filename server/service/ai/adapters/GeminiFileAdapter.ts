/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { <PERSON><PERSON> } from 'winston';
import type { FileProviderAdapter } from './FileProviderAdapter';
import type { GeminiA<PERSON> } from '../models/GeminiAI';

/**
 * GeminiFileAdapter - Implements file operations for Gemini provider
 * 
 * Features:
 * - File upload via Gemini Files API
 * - No automatic expiration (files persist until deleted)
 * - File existence verification through local tracking
 * - Note: Gemini Files API doesn't support deletion in current version
 */
export class GeminiFileAdapter implements FileProviderAdapter {
  constructor(
    private aiModel: GeminiAI,
    private logger: Logger
  ) {}

  /**
   * Upload a file to Gemini
   */
  async uploadFile(fileData: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  }): Promise<{
    fileId: string;
    uploadedAt: Date;
    expiresAt?: Date;
  }> {
    this.logger.debug('Uploading file to Gemini', {
      filename: fileData.originalname,
      size: fileData.size,
      mimetype: fileData.mimetype
    });

    // Create a File object from buffer for Gemini
    const file = new File([fileData.buffer], fileData.originalname, {
      type: fileData.mimetype,
    });

    // Upload to Gemini
    const uploadedFile = await this.aiModel.createFile({ file });

    const uploadedAt = new Date();
    // Gemini files don't expire automatically
    const expiresAt = undefined;

    this.logger.info('File uploaded to Gemini successfully', {
      fileId: uploadedFile.id,
      filename: fileData.originalname,
      expiresAt
    });

    return {
      fileId: uploadedFile.id,
      uploadedAt,
      expiresAt
    };
  }

  /**
   * Delete a file from Gemini
   * Note: Gemini doesn't support file deletion in current API
   */
  async deleteFile(fileId: string): Promise<void> {
    this.logger.debug('Attempting to delete file from Gemini', { fileId });
    
    // Gemini doesn't support file deletion
    // Just log and return
    this.logger.info('Gemini does not support file deletion - reference removed', { fileId });
  }

  /**
   * Check if a file exists with Gemini
   */
  async fileExists(fileId: string): Promise<boolean> {
    try {
      this.logger.debug('Checking file existence in Gemini', { fileId });
      
      // Try to retrieve file info
      const fileInfo = await this.getFileInfo(fileId);
      
      // Check if file exists and is in a valid state
      if (fileInfo && fileInfo.state) {
        return fileInfo.state === 'ACTIVE' || fileInfo.state === 'PROCESSING';
      }
      
      return !!fileInfo;
    } catch (error) {
      this.logger.debug('File not found in Gemini', { fileId });
      return false;
    }
  }

  /**
   * Get file metadata from Gemini
   */
  async getFileInfo(fileId: string): Promise<{
    id: string;
    name: string;
    size: number;
    createdAt: Date;
    expiresAt?: Date;
    state?: string;
  } | null> {
    try {
      this.logger.debug('Retrieving file info from Gemini', { fileId });
      
      // Validate Gemini file ID format
      if (!fileId || !fileId.startsWith('gemini_')) {
        this.logger.debug('Invalid Gemini file ID format', { fileId });
        return null;
      }
      
      // Try to retrieve file using the model's retrieveFile method
      if (this.aiModel.retrieveFile) {
        try {
          const file = await this.aiModel.retrieveFile(fileId);
          if (file) {
            return {
              id: file.id,
              name: file.filename || 'unknown',
              size: file.bytes || 0,
              createdAt: new Date(file.created_at * 1000),
              expiresAt: undefined,
              state: 'state' in file && typeof file.state === 'string' ? file.state : 'ACTIVE'
            };
          }
        } catch (error) {
          this.logger.debug('Failed to retrieve file from Gemini', { fileId });
          // Continue to return null rather than throw
        }
      }
      
      return null;
    } catch (error) {
      if (this.is404Error(error)) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Check if an error is a 404 Not Found error
   */
  private is404Error(error: unknown): boolean {
    // Type guard for error with status
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;
      
      // Check for common 404 error patterns
      if (errorObj.status === 404) return true;
      if (errorObj.code === 'file_not_found') return true;
      
      // Check message field
      if (typeof errorObj.message === 'string') {
        if (errorObj.message.toLowerCase().includes('not found')) return true;
        if (errorObj.message.includes('404')) return true;
      }
    }
    
    return false;
  }
}