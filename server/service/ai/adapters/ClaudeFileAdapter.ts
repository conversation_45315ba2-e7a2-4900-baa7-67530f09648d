/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { <PERSON><PERSON> } from 'winston';
import type { FileProviderAdapter } from './FileProviderAdapter';
import type { <PERSON><PERSON><PERSON> } from '../models/ClaudeAI';
import { mimeType as mimeTypeConstants } from '../../file/constants';

/**
 * ClaudeFileAdapter - Implements file operations for Claude provider
 *
 * Features:
 * - File upload via Claude Files API (beta)
 * - 404 error detection and recovery
 * - File existence verification
 * - Note: Claude Files API does not support file deletion
 */
export class ClaudeFileAdapter implements FileProviderAdapter {

  constructor(
    private aiModel: ClaudeAI,
    private logger: Logger
  ) {}

  /**
   * Upload a file to Claude
   */
  async uploadFile(fileData: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  }): Promise<{
    fileId: string;
    uploadedAt: Date;
    expiresAt?: Date;
  }> {
    this.logger.debug('Uploading file to <PERSON>', {
      filename: fileData.originalname,
      size: fileData.size,
      mimetype: fileData.mimetype
    });

    // For JSON files, use text/plain mimetype since <PERSON> only supports PDF and plaintext
    let mimeType = fileData.mimetype;
    if (mimeType === mimeTypeConstants.json) {
      mimeType = mimeTypeConstants.txt;
      this.logger.info('Converting JSON mimetype to text/plain for Claude', {
        filename: fileData.originalname
      });
    }

    // Convert buffer to File object for Claude API
    const file = new File([fileData.buffer], fileData.originalname, {
      type: mimeType,
    });

    // Upload to Claude using the Files API
    const uploadedFile = await this.aiModel.createFile({
      file
    });

    const uploadedAt = new Date();
    // Claude files don't expire automatically
    const expiresAt = undefined;

    this.logger.info('File uploaded to Claude successfully', {
      fileId: uploadedFile.id,
      filename: fileData.originalname,
      expiresAt
    });

    return {
      fileId: uploadedFile.id,
      uploadedAt,
      expiresAt
    };
  }

  /**
   * Delete a file from Claude
   * Note: Claude Files API does not support file deletion
   */
  async deleteFile(fileId: string): Promise<void> {
    this.logger.debug('Attempting to delete file from Claude', { fileId });

    // Check if the model has a deleteFile method
    if (typeof this.aiModel.deleteFile === 'function') {
      try {
        await this.aiModel.deleteFile(fileId);
        this.logger.info('File deleted from Claude', { fileId });
      } catch (error) {
        // Check if error is 404 (file already deleted/not found)
        if (this.is404Error(error)) {
          this.logger.warn('File not found in Claude (already deleted or expired)', { fileId });
          return;
        }
        throw error;
      }
    } else {
      // Claude Files API doesn't support deletion as of current version
      this.logger.warn('Claude Files API does not support file deletion', { fileId });
      // We don't throw an error here as this is expected behavior
    }
  }

  /**
   * Check if a file exists with Claude
   */
  async fileExists(fileId: string): Promise<boolean> {
    try {
      this.logger.debug('Checking file existence in Claude', { fileId });

      // Try to retrieve file info
      const fileInfo = await this.getFileInfo(fileId);

      return !!fileInfo;
    } catch (error: unknown) {
      // For any error, assume file doesn't exist
      this.logger.debug('File not found or error checking existence', { fileId, message: error instanceof Error ? error.message : 'Unknown error' });
      return false;
    }
  }

  /**
   * Get file metadata from Claude
   */
  async getFileInfo(fileId: string): Promise<{
    id: string;
    name: string;
    size: number;
    createdAt: Date;
    expiresAt?: Date;
  } | null> {
    try {
      this.logger.debug('Retrieving file info from Claude', { fileId });

      // For Claude, since we can't verify file existence via API,
      // we'll assume the file exists if we have a fileId stored
      // This prevents unnecessary re-uploads
      if (fileId && fileId.startsWith('file_')) {
        this.logger.debug('Assuming Claude file exists based on stored ID', { fileId });
        return {
          id: fileId,
          name: 'unknown',
          size: 0,
          createdAt: new Date(),
          expiresAt: undefined
        };
      }

      // Invalid file ID format
      return null;
    } catch (error) {
      if (this.is404Error(error)) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Check if an error is a 404 Not Found error
   */
  private is404Error(error: unknown): boolean {
    // Type guard for error with status
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;

      // Check for common 404 error patterns
      if (errorObj.status === 404) {
        return true;
      }
      if (errorObj.code === 'file_not_found') {
        return true;
      }
      if (errorObj.type === 'not_found_error') {
        return true;
      }

      // Check message field
      if (typeof errorObj.message === 'string') {
        if (errorObj.message.toLowerCase().includes('not found')) {
          return true;
        }
        if (errorObj.message.includes('404')) {
          return true;
        }
      }

      // Check for Claude-specific error format
      if (typeof errorObj.error === 'object' && errorObj.error !== null) {
        const nestedError = errorObj.error as Record<string, unknown>;
        if (nestedError.code === 'file_not_found') {
          return true;
        }
        if (nestedError.type === 'not_found_error') {
          return true;
        }
      }
    }

    return false;
  }
}
