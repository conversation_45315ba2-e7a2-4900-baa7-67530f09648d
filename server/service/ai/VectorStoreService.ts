/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Types } from 'mongoose';
import VectorStore, { VectorStorePlain } from '../../models/vectorStore';
import AiUploadedFile from '../../models/aiUploadedFile';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import type { ChatGPT } from './models/ChatGPT';
import { getChatGPT } from './models/ChatGPT';
import User from '../../models/user';

export class VectorStoreService {
  constructor(
    private logger: LoggerInterface,
    private vectorStoreModel: typeof VectorStore,
    private aiUploadedFileModel: typeof AiUploadedFile,
    private userModel: typeof User,
    private chatGPT: ChatGPT,
  ) {}

  /**
   * Get or create a user's AI testing vector store
   */
  public async getOrCreateUserStore(userId: Types.ObjectId): Promise<string> {
    // Check if user already has a vector store
    const existingStore = await this.vectorStoreModel.findOne({
      ownerId: userId,
      purpose: 'ai_testing',
    });

    if (existingStore) {
      this.logger.info('Using existing vector store for user', {
        userId: userId.toString(),
        vectorStoreId: existingStore.vectorStoreId,
      });
      return existingStore.vectorStoreId;
    }

    // Get user name for store naming
    const user = await this.userModel.findById(userId, { name: 1 });
    const storeName = `AI Testing - ${user?.name || userId.toString()}`;

    // Create new vector store
    this.logger.info('Creating new vector store for user', {
      userId: userId.toString(),
      storeName,
    });

    const vectorStoreId = await this.chatGPT.createVectorStore(storeName);

    // Save to database
    await this.vectorStoreModel.create({
      vectorStoreId,
      name: storeName,
      purpose: 'ai_testing',
      ownerId: userId,
      fileCount: 0,
    });

    this.logger.info('Vector store created successfully', {
      userId: userId.toString(),
      vectorStoreId,
    });

    return vectorStoreId;
  }

  /**
   * Add files to user's vector store
   */
  public async addFilesToUserStore(userId: Types.ObjectId, fileIds: string[]): Promise<void> {
    const vectorStoreId = await this.getOrCreateUserStore(userId);

    // Check which files are already in the vector store
    const alreadyInStore = await this.aiUploadedFileModel.find({
      'providerFiles.openai.fileId': { $in: fileIds },
      'providerFiles.openai.vectorStoreId': vectorStoreId,
    });

    const alreadyInStoreIds = alreadyInStore
      .map(file => file.providerFiles?.openai?.fileId)
      .filter(Boolean) as string[];

    const newFileIds = fileIds.filter(id => !alreadyInStoreIds.includes(id));

    if (newFileIds.length === 0) {
      this.logger.info('All files already in vector store', {
        userId: userId.toString(),
        vectorStoreId,
        fileCount: fileIds.length,
      });
      return;
    }

    this.logger.info('Adding files to vector store', {
      userId: userId.toString(),
      vectorStoreId,
      newFileCount: newFileIds.length,
      existingFileCount: alreadyInStoreIds.length,
    });

    try {
      // Add files to vector store via OpenAI API
      await this.chatGPT.addFilesToVectorStore(vectorStoreId, newFileIds);

      // Update file records with vector store ID
      await this.aiUploadedFileModel.updateMany(
        {
          'providerFiles.openai.fileId': { $in: newFileIds },
        },
        {
          $set: {
            'providerFiles.openai.vectorStoreId': vectorStoreId,
          },
        }
      );

      // Update file count in vector store record
      await this.vectorStoreModel.findOneAndUpdate(
        { vectorStoreId },
        { 
          $inc: { fileCount: newFileIds.length },
          $set: { updatedAt: new Date() },
        }
      );

      this.logger.info('Files added to vector store successfully', {
        userId: userId.toString(),
        vectorStoreId,
        addedCount: newFileIds.length,
      });
    } catch (error) {
      throw new ContextError('Failed to add files to vector store', {
        userId: userId.toString(),
        vectorStoreId,
        fileIds: newFileIds,
        cause: error,
      });
    }
  }

  /**
   * Get vector store by ID
   */
  public async getVectorStore(vectorStoreId: string): Promise<VectorStorePlain | null> {
    return this.vectorStoreModel.findOne({ vectorStoreId }).lean();
  }

  /**
   * Get all vector stores for a user
   */
  public async getUserVectorStores(userId: Types.ObjectId): Promise<VectorStorePlain[]> {
    return this.vectorStoreModel.find({ ownerId: userId }).lean();
  }
}

let instance: VectorStoreService | undefined;

export const getVectorStoreService = () => {
  if (!instance) {
    instance = new VectorStoreService(
      wwgLogger,
      VectorStore,
      AiUploadedFile,
      User,
      getChatGPT(),
    );
  }
  return instance;
};