import type { <PERSON><PERSON><PERSON> } from 'openai';
import type { FileDeleted, FileObject, FilePurpose } from 'openai/resources/files';
import type { MessageCreateParams } from 'openai/resources/beta/threads/messages';
import type { AssistantCreateParams, AssistantDeleted } from 'openai/resources/beta/assistants';
import type { Uploadable } from 'openai/uploads';
import type { ZodType, z } from 'zod';
import type { UnifiedAIModel, AIFileInfo } from './UnifiedAIModel';
import type { AIModel } from './AIModel';
import type { AiProvider } from '../types';

// TODO: Make this interface less OpenAI specific.
export interface FileSupportAiModel {
  createFile(params: { file: Uploadable; purpose?: FilePurpose }): Promise<FileObject>;
  deleteFile(fileId: string): Promise<FileDeleted>;
  createAssistant(body: Pick<AssistantCreateParams, 'name' | 'instructions' | 'tools'>): Promise<OpenAI.Beta.Assistants.Assistant>;
  deleteAssistant(assistantId: string): Promise<AssistantDeleted>;
  runThreadWithAssistant<Output extends ZodType>(params: {
    assistantId: string;
    message: MessageCreateParams;
    jsonSchema: ZodType;
  }): Promise<z.infer<Output> | undefined>;
}

/**
 * Combined interface that includes both FileSupportAiModel and the additional methods
 * This is what the implementations actually provide
 */
export interface FileSupportAiModelWithCapabilities extends FileSupportAiModel, AIModel {
  // Execute with files - allows each implementation to handle file-based execution in their own way
  executeWithFiles<Output extends ZodType>(params: {
    prompt: string;
    files: AIFileInfo[];  // Flexible file info format
    jsonSchema: ZodType;
    systemPrompt?: string;
    vectorStoreId?: string;  // Optional vector store ID for OpenAI Responses API
    modelName?: string;  // Optional model override for providers that support multiple models
  }): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }>;

  // Capability checks
  supportsAssistants(): boolean;

  // Provider identification
  getProvider(): AiProvider;
}

// ============================================================================
// Unified File Support Interface
// ============================================================================

export interface FileUploadResult {
  id: string;
  name?: string;
  size?: number;
  createdAt?: Date;
}

/**
 * Extended unified AI interface for providers that support file operations
 * This interface extends UnifiedAIModel and provides provider-agnostic file operations
 */
export interface UnifiedFileSupportAIModel extends UnifiedAIModel {
  /**
   * Upload a file to the provider
   */
  uploadFile(file: Buffer | Blob | string, metadata?: {
    filename?: string;
    mimeType?: string;
    purpose?: string;
  }): Promise<FileUploadResult>;

  /**
   * Delete a file from the provider
   * Returns object for compatibility with legacy FileSupportAiModel interface
   */
  deleteFile(fileId: string): Promise<{ id: string; object: string; deleted: boolean }>;

  /**
   * Check if a file exists
   */
  fileExists(fileId: string): Promise<boolean>;

  /**
   * Execute a prompt with files and structured output
   */
  executeWithFiles<Output extends ZodType>(
    model: string,
    params: {
      prompt: string;
      files: AIFileInfo[];
      jsonSchema: Output;
      maxTokens?: number;
      temperature?: number;
      systemPrompt?: string;
      vectorStoreId?: string;  // Optional vector store ID for OpenAI Responses API
    }
  ): Promise<z.infer<Output> | undefined>;

  /**
   * Retrieve file information (optional - not all providers support this)
   */
  retrieveFile?(fileId: string): Promise<FileObject | null>;

  /**
   * Capability checks
   */
  supportsAssistants(): boolean;

  /**
   * Provider identification
   */
  getProvider(): AiProvider;
}
