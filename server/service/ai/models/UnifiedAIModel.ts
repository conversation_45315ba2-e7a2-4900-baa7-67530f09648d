/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { AiProvider } from '../types';

export type AIPrompt = {
  role: "user" | "system" | "assistant";
  content: string;
};

export type AIResponse<T = string> = {
  content: T;
  usage?: {
    total_tokens: number;
    prompt_tokens?: number;
    completion_tokens?: number;
  }
}

/**
 * File information for AI processing
 * Flexible format to support different providers
 */
export interface AIFileInfo {
  fileId: string;
  mimeType?: string;
  vectorStoreId?: string; // For OpenAI
  title?: string;
  context?: string; // Additional context about the file
}

/**
 * Unified AI Model interface that follows provider SDK patterns
 * Allows passing model name as parameter to each method call
 */
export interface UnifiedAIModel {
  /**
   * Run a completion with the specified model
   * @param model - The model name (e.g., 'gpt-4', 'claude-3-5-sonnet', 'gemini-2.5-pro')
   * @param messages - Array of messages
   * @param options - Optional parameters like maxTokens, temperature, etc.
   */
  runCompletion(
    model: string,
    messages: AIPrompt[],
    options?: {
      maxTokens?: number;
      temperature?: number;
      responseFormat?: ResponseFormat;
    }
  ): Promise<AIResponse>;

  /**
   * Run a completion and parse the response as JSON
   */
  parseCompletion<T = any>(
    model: string,
    messages: AIPrompt[],
    options?: {
      maxTokens?: number;
      temperature?: number;
      responseFormat?: ResponseFormat;
    }
  ): Promise<AIResponse<T>>;

  /**
   * Get the provider name
   */
  getProvider(): AiProvider;

  /**
   * Check if a model is supported by this provider
   */
  supportsModel(model: string): boolean;

  /**
   * Get list of supported models
   */
  getSupportedModels(): string[];
}
