/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { OpenAI } from 'openai';
import type { AIModel, AIPrompt, AIResponse } from './AIModel';
import type { AIFileInfo } from './UnifiedAIModel';
import config from '../../../config';
import UserError from '../../../error/UserError';
import { wwgLogger, type LoggerInterface } from '../../wwgLogger';
import type { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { DEFAULT_MAX_TOKEN } from '../utrv-assistant/constants';
import type { FilePurpose, FileObject } from 'openai/resources/files';
import type { MessageCreateParams } from 'openai/resources/beta/threads/messages';
import ContextError from '../../../error/ContextError';
import type { AssistantCreateParams } from 'openai/resources/beta/assistants';
import type { Uploadable } from 'openai/uploads';
import z, { type ZodType } from 'zod';
import { zodResponseFormat, zodTextFormat } from 'openai/helpers/zod';
import { AIInteraction } from '../../../models/ai-interaction';
import { AiProvider } from '../types';
import type { FileSupportAiModelWithCapabilities } from './FileSupportAiModel';
import AiUploadedFile from '../../../models/aiUploadedFile';


export const OPENAI_MODEL = 'gpt-4.1-2025-04-14';
const TOKEN_LIMIT = 100000;

export interface OpenAiResponse extends Partial<OpenAI.Chat.Completions.ChatCompletion> {
  usage?: OpenAI.Completions.CompletionUsage | undefined;
}

export class ChatGPT implements AIModel, FileSupportAiModelWithCapabilities {
  private openai = new OpenAI({
    apiKey: config.ai.chatGPT.apiKey,
  });

  constructor(private logger: LoggerInterface, private aiInteractionModel: typeof AIInteraction) {}

  public async runCompletion(messages: AIPrompt[], maxTokens?: number) {
    try {
      const response = await this.openai.chat.completions.create({
        model: OPENAI_MODEL,
        messages,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN),
      });
      return {
        content: response.choices[0].message.content ?? '',
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to generate a response. Please try again later', { messages, cause: e });
    }
  }

  public async parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat): Promise<AIResponse<T>> {
    try {
      const tokens = Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN);
      const response = await this.openai.chat.completions.parse({
        model: OPENAI_MODEL,
        messages,
        max_tokens: tokens,
        ...(responseFormat ? { response_format: responseFormat } : {}),
      });

      this.aiInteractionModel
        .create({
          model: OPENAI_MODEL,
          maxTokens: tokens,
          usage: {
            promptTokens: response.usage?.prompt_tokens ?? 0,
            completionTokens: response.usage?.completion_tokens ?? 0,
            totalTokens: response.usage?.total_tokens ?? 0,
          },
          prompt: messages?.[0].content,
        })
        .catch(() => {
          this.logger.error(
            new ContextError(`Failed to create AI interaction`, {
              prompt: messages?.[0].content,
            })
          );
        });

      return {
        content: response.choices[0].message.parsed as T,
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to communicate with ChatGPTAI. Please try again later', { messages, cause: e });
    }
  }

  public getModelVersion(): string {
    return OPENAI_MODEL;
  }

  public async createFile({ file, purpose = 'assistants' }: { file: Uploadable; purpose?: FilePurpose }) {
    return this.openai.files.create({
      file,
      purpose,
    });
  }

  public async deleteFile(fileId: string) {
    return this.openai.files.delete(fileId);
  }

  public async retrieveFile(fileId: string): Promise<FileObject | null> {
    try {
      return await this.openai.files.retrieve(fileId);
    } catch (error: any) {
      // Return null for 404 errors
      if (error.status === 404 || error.code === 'file_not_found') {
        return null;
      }
      throw error;
    }
  }

  // Vector Store Management Methods
  public async createVectorStore(name: string): Promise<string> {
    const vectorStore = await this.openai.vectorStores.create({
      name,
    });
    return vectorStore.id;
  }

  public async addFilesToVectorStore(vectorStoreId: string, fileIds: string[]): Promise<void> {
    try {
      // Add files to vector store
      for (const fileId of fileIds) {
        await this.openai.vectorStores.files.create(vectorStoreId, {
          file_id: fileId
        });
      }

      this.logger.info('Files successfully added to vector store', {
        vectorStoreId,
        fileIds,
        count: fileIds.length
      });
    } catch (error) {
      throw new ContextError('Failed to add files to vector store', {
        vectorStoreId,
        fileIds,
        cause: error
      });
    }
  }

  public async deleteVectorStore(vectorStoreId: string): Promise<void> {
    await this.openai.vectorStores.delete(vectorStoreId);
  }

  public async createAssistant(body: Pick<AssistantCreateParams, 'name' | 'instructions' | 'tools'>) {
    return this.openai.beta.assistants.create({ ...body, model: OPENAI_MODEL });
  }

  public async deleteAssistant(assistantId: string) {
    return this.openai.beta.assistants.delete(assistantId);
  }

  public async runThreadWithAssistant<Output extends ZodType>({
    assistantId,
    message,
    jsonSchema,
  }: {
    assistantId: string;
    message: MessageCreateParams;
    jsonSchema: Output;
  }): Promise<z.infer<Output> | undefined> {
    // 1. Create a thread
    const thread = await this.openai.beta.threads.create();

    // 2. Add the user message to the thread
    await this.openai.beta.threads.messages.create(thread.id, message);

    // 3. Run the assistant AND wait for completion
    const responseFormat = zodResponseFormat(jsonSchema, 'output');
    const run = await this.openai.beta.threads.runs.createAndPoll(thread.id, {
      assistant_id: assistantId,
      response_format: responseFormat,
      max_completion_tokens: TOKEN_LIMIT,
    });

    await this.aiInteractionModel.create({
      model: run.model,
      maxTokens: run.max_completion_tokens,
      usage: {
        promptTokens: run.usage?.prompt_tokens ?? 0,
        completionTokens: run.usage?.completion_tokens ?? 0,
        totalTokens: run.usage?.total_tokens ?? 0,
      },
      prompt: message.content,
    });

    if (run.status === 'failed') {
      throw new ContextError('Thread run failed', {
        assistantId,
        threadId: thread.id,
        runId: run.id,
        cause: run.last_error,
      });
    }

    if (run.status === 'incomplete') {
      throw new ContextError('Thread run incomplete', {
        assistantId,
        threadId: thread.id,
        runId: run.id,
        cause: run.incomplete_details,
      });
    }

    // 4. Fetch messages once the run is complete
    const messages = await this.openai.beta.threads.messages.list(thread.id);
    const assistantMessage = messages.data.find((m) => m.role === 'assistant');

    if (!assistantMessage) {
      this.logger.error(new ContextError('Thread message not found', { assistantId, threadId: thread.id, messages }));
      return;
    }

    // 5. Extract the assistant's content text
    const rawResult = assistantMessage.content[0].type === 'text' ? assistantMessage.content[0].text.value : '';
    try {
      return jsonSchema.parse(JSON.parse(rawResult));
    } catch (e) {
      this.logger.error(
        new ContextError('Failed to parse AI response to result', {
          assistantId,
          threadId: thread.id,
          responseContent: rawResult,
          cause: e,
        })
      );
    }
  }

  public async executeWithFiles<Output extends ZodType>(params: {
    prompt: string;
    files: AIFileInfo[];
    jsonSchema: ZodType;
    systemPrompt?: string;
    vectorStoreId?: string;
    modelName?: string;
  }): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    const { prompt, files, jsonSchema, systemPrompt, vectorStoreId, modelName } = params;
    
    // Use provided model name or default
    const model = modelName || OPENAI_MODEL;

    try {
      // Extract unique vector store IDs from files or use provided vectorStoreId
      const vectorStoreIds = await this.resolveVectorStoreIdsFromFileInfos(files, vectorStoreId);

      if (vectorStoreIds.length === 0) {
        throw new ContextError('No vector stores found for provided files', {
          fileIds: files.map(f => f.fileId),
          message: 'Files must be added to a vector store before using the Responses API'
        });
      }

      // Create tools array with the vector store IDs
      const tools: OpenAI.Responses.Tool[] = [{
        type: 'file_search',
        vector_store_ids: vectorStoreIds
      }];

      this.logger.info('Executing with files using Responses API', { vectorStoreIds });

      // Use the new Responses API with parse for structured output
      const response = await this.openai.responses.parse({
        model,
        instructions: systemPrompt || 'You are an AI assistant that analyzes documents and matches them to sustainability metrics.',
        input: [
          {
            role: 'user',
            content: [
              {
                type: 'input_text',
                text: prompt
              }
            ]
          }
        ],
        tools,
        max_output_tokens: TOKEN_LIMIT,
        text: {
          format: zodTextFormat(jsonSchema, 'output')
        }
      });

      // Extract token usage - OpenAI Responses API uses snake_case (input_tokens/output_tokens)
      const tokenUsage = response.usage ? {
        inputTokens: response.usage.input_tokens || 0,
        outputTokens: response.usage.output_tokens || 0,
        totalTokens: response.usage.total_tokens || 0
      } : undefined;

      // Track usage
      await this.aiInteractionModel.create({
        model: response.model || OPENAI_MODEL,
        maxTokens: TOKEN_LIMIT,
        usage: {
          promptTokens: tokenUsage?.inputTokens ?? 0,
          completionTokens: tokenUsage?.outputTokens ?? 0,
          totalTokens: tokenUsage?.totalTokens ?? 0,
        },
        prompt: prompt,
      });

      // Log the raw response for debugging
      this.logger.debug('Responses API raw response', {
        output: response.output,
        model: response.model,
        id: response.id
      });

      // Return the parsed output with token usage
      return {
        data: response.output_parsed,
        tokenUsage
      };
    } catch (error) {
      console.log(error);
      // If it's a parse error, it might be because the API returned text instead of JSON
      if (error instanceof Error && error.message.includes('Unexpected token')) {
        throw new UserError(
          'OpenAI returned invalid response format. The Responses API may not be properly configured for JSON output.',
          { originalError: error.message }
        );
      }

      throw new ContextError('Failed to execute with files', { cause: error });
    }
  }

  /**
   * Resolves vector store IDs from file infos
   * @param fileInfos - Array of file information objects
   * @param overrideVectorStoreId - Optional override vector store ID
   * @returns Array of unique vector store IDs
   */
  private async resolveVectorStoreIdsFromFileInfos(
    fileInfos: AIFileInfo[],
    overrideVectorStoreId?: string
  ): Promise<string[]> {
    // If override is provided, use it
    if (overrideVectorStoreId) {
      return [overrideVectorStoreId];
    }

    const vectorStoreIds = new Set<string>();

    // First check if files have vectorStoreId in their metadata
    for (const fileInfo of fileInfos) {
      if (fileInfo.vectorStoreId) {
        vectorStoreIds.add(fileInfo.vectorStoreId);
      }
    }

    // If no vector stores found in metadata, look up from database
    if (vectorStoreIds.size === 0) {
      for (const fileInfo of fileInfos) {
        const uploadedFile = await AiUploadedFile.findOne({
          'providerFiles.openai.fileId': fileInfo.fileId,
          'providerFiles.openai.vectorStoreId': { $exists: true }
        });

        if (uploadedFile?.providerFiles?.openai?.vectorStoreId) {
          vectorStoreIds.add(uploadedFile.providerFiles.openai.vectorStoreId);
        }
      }
    }

    return Array.from(vectorStoreIds);
  }

  public supportsAssistants(): boolean {
    return false; // Using Responses API, not assistants
  }

  public getProvider(): AiProvider {
    return AiProvider.OpenAi;
  }
}

let instance: ChatGPT;
export const getChatGPT = () => {
  if (!instance) {
    instance = new ChatGPT(wwgLogger, AIInteraction);
  }
  return instance;
};
