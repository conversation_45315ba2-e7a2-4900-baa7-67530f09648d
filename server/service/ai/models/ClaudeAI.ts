/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { type AIModel, type AIPrompt, type AIResponse } from './AIModel';
import { type Logger } from 'winston';
import config from '../../../config';
import { wwgLogger } from '../../wwgLogger';
import UserError from '../../../error/UserError';
import Anthropic from '@anthropic-ai/sdk';
import type { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import ContextError from '../../../error/ContextError';
import { type FileSupportAiModelWithCapabilities } from './FileSupportAiModel';
import type { AIFileInfo } from './UnifiedAIModel';
import { toFile } from '@anthropic-ai/sdk/uploads';
import type z from 'zod';
import { type FileObject, type FilePurpose } from 'openai/resources/files';
import { type Uploadable } from 'openai/uploads';
import { <PERSON><PERSON><PERSON>ider } from '../types';

const CLAUDE_MODEL = 'claude-sonnet-4-20250514';
const TOKEN_LIMIT = 8192;

// Claude-specific file interfaces to match Anthropic API
export interface ClaudeFileUpload {
  file: Buffer | Uint8Array | File;
  filename: string;
}

export interface ClaudeFileMetadata {
  id: string;
  filename: string;
  mime_type: string;
  size_bytes: number;
  created_at: string;
  type: 'file';
  downloadable?: boolean;
}

export class ClaudeAI implements AIModel, FileSupportAiModelWithCapabilities {
  private claude = new Anthropic({
    apiKey: config.ai.claude.apiKey,
    defaultHeaders: {
      'anthropic-beta': 'files-api-2025-04-14' // Enable Files API beta
    }
  });

  constructor(private logger: Logger) {}

  public async parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat): Promise<AIResponse<T>> {
    throw new ContextError('ClaudeAI does not support response formats', {
      messages: messages.map((m) => ({
        role: m.role,
        content: m.content?.slice(0, 100),
      })),
      maxTokens,
      responseFormat,
    });
  }

  private async query(params: {
    system: string;
    messages: Anthropic.Messages.MessageParam[];
    maxTokens?: number;
    prefill?: string;
    model?: string;
  }) {
    try {
      const { system, messages, maxTokens = TOKEN_LIMIT, prefill, model = CLAUDE_MODEL } = params;

      // If prefill is provided, add it as an assistant message
      const allMessages = prefill
        ? [...messages, { role: 'assistant' as const, content: prefill }]
        : messages;

      return this.claude.messages.create({
        model,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens),
        temperature: 1,
        system,
        messages: allMessages,
      });
    } catch (e) {
      throw new UserError('Unable to communicate with ClaudeAI. Please try again later', { messages: params.messages, cause: e });
    }
  }

  public async runCompletion(messages: AIPrompt[], maxTokens?: number): Promise<AIResponse> {
    const userMessages = messages.filter((m) => m.role === 'user') as Anthropic.Messages.MessageParam[];
    const systemMessages = messages.filter((m) => m.role === 'system');
    const system = systemMessages.map((m) => m.content).join('\n');

    const response = await this.query({
      system,
      messages: userMessages,
      maxTokens,
    });
    const content = response.content[0].type === 'text' ? response.content[0] : { text: '' };

    return {
      content: content.text,
      usage: {
        completion_tokens: response.usage.output_tokens,
        prompt_tokens: response.usage.input_tokens,
        total_tokens: response.usage.output_tokens + response.usage.input_tokens,
      }
    };
  }

  public getModelVersion(): string {
    // TODO: Fix model version reporting
    // Currently this always returns the hardcoded CLAUDE_MODEL constant ('claude-sonnet-4-20250514')
    // regardless of which Claude model is actually being used (e.g., 'claude-sonnet-4-20250514').
    //
    // This causes incorrect model names in token estimation logs and other places that call getModelVersion().
    //
    // To fix this:
    // 1. Add a model parameter to the ClaudeAI constructor
    // 2. Store the actual model being used as an instance variable
    // 3. Update the query() method to use the instance model instead of CLAUDE_MODEL constant
    // 4. Return the actual model in this method
    // 5. Update getClaudeAI() factory to accept and pass through the model parameter
    // 6. Update UnifiedAIModelFactory to pass the specific model when creating ClaudeAI instances
    //
    // This change will ensure accurate model reporting throughout the system.
    return CLAUDE_MODEL;
  }

  // Files API support
  public async uploadFile(upload: ClaudeFileUpload): Promise<ClaudeFileMetadata> {
    this.logger.info('Uploading file to Claude', {
      filename: upload.filename,
      fileSize: upload.file instanceof Buffer ? upload.file.length : 'unknown'
    });

    // Convert Buffer/Uint8Array to File using toFile helper
    const file = await toFile(upload.file, upload.filename);

    const result = await this.claude.beta.files.upload({
      file: file
    });

    this.logger.info('File uploaded successfully to Claude', {
      fileId: result.id,
      filename: result.filename,
      mimeType: result.mime_type,
      sizeBytes: result.size_bytes
    });

    return result;
  }


  public async listFiles(): Promise<ClaudeFileMetadata[]> {
    this.logger.info('Listing files from Claude');

    const files = [];
    for await (const file of this.claude.beta.files.list()) {
      files.push(file);
    }

    this.logger.info('Files listed successfully from Claude', { count: files.length });
    return files;
  }

  public async getFileMetadata(fileId: string): Promise<ClaudeFileMetadata> {
    this.logger.info('Getting file metadata from Claude', { fileId });

    const metadata = await this.claude.beta.files.retrieveMetadata(fileId);

    this.logger.info('File metadata retrieved successfully from Claude', {
      fileId,
      filename: metadata.filename,
      mimeType: metadata.mime_type
    });

    return metadata;
  }

  public async downloadFile(fileId: string): Promise<Buffer> {
    this.logger.info('Downloading file from Claude', { fileId });

    const response = await this.claude.beta.files.download(fileId);

    const buffer = Buffer.from(await response.arrayBuffer());

    this.logger.info('File downloaded successfully from Claude', {
      fileId,
      sizeBytes: buffer.length
    });

    return buffer;
  }

  // Check if file operations are supported
  public supportsFileOperations(): boolean {
    return true; // Now supported with Files API
  }

  // Bridge method to satisfy FileSupportAiModel interface
  public async createFile(params: { file: Uploadable; purpose?: FilePurpose }): Promise<FileObject> {
    const file = params.file as File;
    const result = await this.uploadFile({
      file: file,
      filename: file.name || 'uploaded_file'
    });

    // Return a FileObject-compatible structure
    return {
      id: result.id,
      bytes: 0, // Claude doesn't provide size in upload response
      created_at: Math.floor(Date.now() / 1000),
      filename: file.name || 'uploaded_file',
      object: 'file' as const,
      purpose: (params.purpose || 'assistants') as any,
      status: 'processed' as const
    };
  }

  // Claude doesn't use assistants, so these methods return appropriate responses
  public async createAssistant(): Promise<any> {
    // Return a dummy assistant object since Claude doesn't need assistants
    return {
      id: 'claude-no-assistant',
      object: 'assistant',
      created_at: Math.floor(Date.now() / 1000),
      name: 'Claude Direct File Access',
      model: CLAUDE_MODEL,
      instructions: 'Claude uses direct file references without assistants',
      tools: [],
      metadata: {}
    };
  }

  public async deleteAssistant(): Promise<any> {
    // No-op for Claude since it doesn't use assistants
    return { id: 'claude-no-assistant', object: 'assistant.deleted', deleted: true };
  }

  public async deleteFile(fileId: string): Promise<any> {
    // Claude Files API doesn't support file deletion yet
    this.logger.warn('Claude Files API does not support file deletion', { fileId });
    return { id: fileId, deleted: true };
  }

  public async retrieveFile(fileId: string): Promise<any> {
    // Claude Files API doesn't have a file retrieval endpoint yet
    // Return null to indicate file not found/not retrievable
    this.logger.debug('Claude Files API does not support file retrieval', { fileId });
    return null;
  }

  public async runThreadWithAssistant<Output extends z.ZodType>(params: {
    assistantId: string;
    message: any;
    jsonSchema: z.ZodType;
  }): Promise<z.infer<Output> | undefined> {
    // Claude doesn't use threads - redirect to executeWithFiles
    // Extract file IDs from message attachments if present
    const fileIds = params.message.attachments?.map((att: any) => att.file_id) || [];

    const result = await this.executeWithFiles({
      prompt: params.message.content,
      files: fileIds.map((id: string) => ({ fileId: id })),
      jsonSchema: params.jsonSchema
    });
    
    // Return just the data for backward compatibility with runThreadWithAssistant interface
    return result.data;
  }

  public async executeWithFiles<Output extends z.ZodType>(params: {
    prompt: string;
    files: AIFileInfo[];
    jsonSchema: z.ZodType;
    systemPrompt?: string;
    vectorStoreId?: string;
    modelName?: string;
  }): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    const { prompt, files, jsonSchema, systemPrompt, modelName } = params;
    // Note: vectorStoreId is ignored for Claude as it doesn't use vector stores

    // For Claude, we need to handle different file types differently
    // JSON files are not supported as documents, so we'll need to embed their content
    // For now, we'll add all files as document references and let Claude handle supported types

    // Build content array with text prompt and file references
    // For now, we cast to the MessageParam content type since Claude's document
    // handling is still evolving and our file_id approach may not be standard
    const content: Anthropic.Messages.MessageParam['content'] = [
      { type: 'text', text: prompt } as Anthropic.Messages.TextBlockParam
    ];

    // Add file references as document blocks
    // Note: Only PDF and plaintext files are supported as documents
    // JSON and other formats will need to be handled differently
    for (const fileInfo of files) {
      // For Claude, we're using a custom document format that may not match
      // the standard SDK types exactly. This is intentional as we're using
      // file references that were previously uploaded
      const documentBlock = {
        type: 'document' as const,
        source: {
          type: 'file' as const,
          file_id: fileInfo.fileId
        }
      };
      // We need to cast this since our file reference approach doesn't match
      // the standard Anthropic document source types (Base64, PlainText, URL)
      content.push(documentBlock as unknown as Anthropic.Messages.ContentBlockParam);
    }

    const messages: Anthropic.Messages.MessageParam[] = [
      {
        role: 'user',
        content
      }
    ];

    // Execute the query with file references and prefill for JSON structure
    // Prefilling with '{"result": [' forces Claude to return the correct JSON format
    const prefill = '{"result": [';
    const defaultSystemPrompt = 'You are an expert in sustainability reporting, ESG metrics, and document analysis. You analyze documents to find relevant information for sustainability tracking and reporting. Be precise and thorough in your analysis.';
    const response = await this.query({
      system: systemPrompt || defaultSystemPrompt,
      messages,
      maxTokens: 10000,
      prefill,
      model: modelName
    });

    // Extract text content from response
    const responseContent = response.content[0].type === 'text' ? response.content[0].text : '';

    // Extract token usage from response (Claude uses snake_case in API)
    const tokenUsage = response.usage ? {
      inputTokens: response.usage.input_tokens,
      outputTokens: response.usage.output_tokens,
      totalTokens: response.usage.input_tokens + response.usage.output_tokens
    } : undefined;

    // Parse JSON response - need to prepend the prefill to make valid JSON
    try {
      // Combine prefill with response to get complete JSON
      const fullJsonResponse = prefill + responseContent;
      const result = JSON.parse(fullJsonResponse);

      // Validate against schema
      const parsed = jsonSchema.safeParse(result);
      if (!parsed.success) {
        throw new ContextError('Failed to validate AI response against schema', {
          errors: parsed.error.errors,
          response: result
        });
      }

      return {
        data: parsed.data,
        tokenUsage
      };
    } catch (e) {
      // If it's a validation error, re-throw it
      if (e instanceof ContextError && e.message.includes('validate')) {
        throw e;
      }

      // For JSON parsing errors, log and return the raw response
      // This matches OpenAI's behavior when parsing fails
      this.logger.warn('Failed to parse Claude response as JSON, returning raw response', {
        responseContent: responseContent,
        prefill: prefill,
        error: e.message
      });

      // Simply return the empty result with raw response
      // The frontend can handle displaying or parsing the raw response
      return {
        data: {
          result: [],
          rawResponse: prefill + responseContent
        },
        tokenUsage
      };
    }
  }

  public supportsAssistants(): boolean {
    return false; // Claude doesn't support assistants
  }

  public getProvider(): AiProvider {
    return AiProvider.Claude;
  }
}

let instance: ClaudeAI;
export const getClaudeAI = () => {
  if (!instance) {
    instance = new ClaudeAI(wwgLogger);
  }
  return instance;
}
