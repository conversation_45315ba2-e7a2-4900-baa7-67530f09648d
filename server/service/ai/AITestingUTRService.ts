/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import UniversalTracker, { type UniversalTrackerValueListPlainLean } from '../../models/universalTracker';
import { getBlueprintRepository, type BlueprintRepository } from '../../repository/BlueprintRepository';
import { activeBlueprints } from '../../survey/blueprints';
import { getHiddenStandardCodes } from '../utr/utrUtil';
import type { SearchUtrsRequest, SearchUtrsResponse } from './ai-testing-types';
import BadRequestError from '../../error/BadRequestError';
import ContextError from '../../error/ContextError';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import type { KeysEnum } from '../../models/public/projectionUtils';

/**
 * Interface for the selected UTR fields used in AI testing search
 * This ensures type safety between the projection and the interface
 */
type AITestingUTRProjection = Pick<UniversalTrackerValueListPlainLean,
  | '_id'
  | 'code'
  | 'name'
  | 'type'
  | 'description'
  | 'valueLabel'
  | 'valueType'
  | 'valueValidation'
  | 'tags'
  | 'blueprintCodes'
  | 'alternatives'
  | 'valueListOptions'
>

/**
 * Service for managing UTR search operations for AI testing
 */
export class AITestingUTRService {
  private logger: LoggerInterface;

  constructor(
    private blueprintRepository: BlueprintRepository,
    logger: LoggerInterface
  ) {
    this.logger = logger.child({ service: 'AITestingUTRService' });
  }

  /**
   * Search UTRs with filtering and pagination
   */
  async searchUTRs(request: SearchUtrsRequest & {
    types?: string[];
    properties?: Array<{
      path: string;
      operator: string;
      value: any;
    }>;
  }): Promise<SearchUtrsResponse> {
    // Basic validation - allow empty query if types are provided
    if ((!request.query || typeof request.query !== 'string') && (!request.types || request.types.length === 0)) {
      throw new BadRequestError('Either query or types must be provided');
    }

    const limit = Math.min(request.limit || 50, 100); // Cap at 100
    const offset = request.offset || 0;

    // Build direct MongoDB query
    const query = request.query?.trim() || '';

    // Handle hidden standard codes (from existing UTR search pattern)
    const hiddenStandardCodes = getHiddenStandardCodes();

    // Get UTRs in scope (from blueprint system)
    const utrCodesInScope = await this.blueprintRepository.getSurveyUtrCodes(activeBlueprints);

    // Build MongoDB search query
    const mongoQuery: any = {
      $and: [
        { code: { $in: utrCodesInScope } }
      ]
    };

    // Add search conditions only if query is provided
    if (query) {
      mongoQuery.$and.push({
        $or: [
          { type: { $regex: query, $options: 'i' } },
          { alternatives: { $elemMatch: { $regex: query, $options: 'i' } } },
          { tags: { $in: [new RegExp(query, 'i')] } },
          { code: { $regex: query, $options: 'i' } },
          { name: { $regex: query, $options: 'i' } },
          { valueLabel: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ]
      });
    }

    // Handle legacy request format - types filter
    if (request.types && Array.isArray(request.types) && request.types.length > 0) {
      mongoQuery.$and.push({ type: { $in: request.types } });
    }

    // Add property filters
    if (request.properties && Array.isArray(request.properties) && request.properties.length > 0) {
      this.addPropertyFilters(mongoQuery, request.properties);
    }

    // Exclude hidden standard codes
    if (hiddenStandardCodes.length > 0) {
      mongoQuery.$and.push({ type: { $nin: hiddenStandardCodes } });
    }

    // Execute direct MongoDB search
    let serviceResponse;
    try {
      const directResults = await UniversalTracker.find(mongoQuery)
        .limit(limit)
        .skip(offset)
        .select({
            _id: 1,
            code: 1,
            name: 1,
            type: 1,
            description: 1,
            valueLabel: 1,
            valueType: 1,
            valueValidation: 1,
            tags: 1,
            blueprintCodes: 1,
            alternatives: 1,
            valueListOptions: 1,
          } satisfies KeysEnum<AITestingUTRProjection>)
        .lean();

      const totalCount = await UniversalTracker.countDocuments(mongoQuery);

      serviceResponse = {
        utrs: directResults,
        total: totalCount,
        cached: false
      };

    } catch (searchError) {
      this.logger.error(new ContextError('MongoDB search failed', { 
        cause: searchError 
      }));
      // Return empty results on error
      serviceResponse = {
        utrs: [],
        total: 0,
        hasMore: false
      };
    }

    // Transform results to match expected format
    const transformedUtrs = serviceResponse.utrs.map((utr) => this.transformUTR(utr));

    return {
      utrs: transformedUtrs,
      total: serviceResponse.total,
      cached: serviceResponse.cached || false
    };
  }

  /**
   * Add property filters to MongoDB query
   */
  private addPropertyFilters(mongoQuery: { $and: unknown[] }, properties: Array<{
    path: string;
    operator: string;
    value: unknown;
  }>): void {
    properties.forEach((prop) => {
      const propQuery: Record<string, unknown> = {};

      switch (prop.operator) {
        case 'equals':
          propQuery[prop.path] = prop.value;
          break;
        case 'contains':
          propQuery[prop.path] = { $regex: prop.value, $options: 'i' };
          break;
        case 'exists':
          propQuery[prop.path] = { $exists: true };
          break;
        case 'not_exists':
        case 'notExists':
          propQuery[prop.path] = { $exists: false };
          break;
        case 'in':
          propQuery[prop.path] = { $in: Array.isArray(prop.value) ? prop.value : [prop.value] };
          break;
        case 'notIn':
          propQuery[prop.path] = { $nin: Array.isArray(prop.value) ? prop.value : [prop.value] };
          break;
        case 'gt':
          propQuery[prop.path] = { $gt: prop.value };
          break;
        case 'lt':
          propQuery[prop.path] = { $lt: prop.value };
          break;
        default:
          propQuery[prop.path] = { $regex: prop.value, $options: 'i' };
      }

      mongoQuery.$and.push(propQuery);
    });
  }

  /**
   * Transform raw UTR data to response format
   */
  private transformUTR(utr: AITestingUTRProjection): SearchUtrsResponse['utrs'][0] {
    return {
      id: utr._id.toString(),
      name: utr.name || '',
      code: utr.code || '',
      type: utr.type || '',
      typeCode: utr.blueprintCodes?.[0] || utr.type,
      description: utr.description || '',
      valueLabel: utr.valueLabel || '',
      valueType: utr.valueType || '',
      properties: {
        valueValidation: utr.valueValidation,
        tags: utr.tags,
        blueprintCodes: utr.blueprintCodes,
        valueListOptions: utr.valueListOptions,
        alternatives: utr.alternatives,
      }
    };
  }
}

// Singleton instance
let instance: AITestingUTRService | undefined;

export const getAITestingUTRService = () => {
  if (!instance) {
    instance = new AITestingUTRService(
      getBlueprintRepository(),
      wwgLogger
    );
  }
  return instance;
};
