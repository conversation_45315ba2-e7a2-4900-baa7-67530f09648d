/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { z } from 'zod';
import { PromptTemplateCategory } from '../../../models/aiPromptTemplate';
import { TestExecutionStatus, TestExecutionType } from '../../../models/aiTestExecution';

/**
 * Validation schemas for AI testing endpoints
 */

// Schema for improve custom prompt request
export const improveCustomPromptSchema = z.object({
  promptContent: z.string().min(1).max(10000)
});

// Schema for generate prompt request
export const generatePromptSchema = z.object({
  description: z.string().min(1).max(1000),
  context: z.string().max(500).optional(),
  outputFormat: z.string().max(500).optional(),
  additionalRequirements: z.string().max(500).optional()
});

// Schema for prompt template creation/update
export const promptTemplateSchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  content: z.string().min(1).max(10000),
  category: z.enum(Object.values(PromptTemplateCategory) as [string, ...string[]]),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  parameters: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean(),
    description: z.string().optional(),
    defaultValue: z.string().optional()
  })).optional()
});

// Schema for UTR search request
export const utrSearchSchema = z.object({
  query: z.string().min(1),
  limit: z.number().min(1).max(100).optional().default(50),
  offset: z.number().min(0).optional().default(0),
  types: z.array(z.string()).optional(),
  properties: z.array(z.object({
    path: z.string(),
    operator: z.enum(['equals', 'contains', 'exists', 'not_exists', 'in', 'notIn', 'gt', 'lt']),
    value: z.any()
  })).optional()
});

// Schema for test execution request
export const testExecutionSchema = z.object({
  fileReference: z.string().optional(),
  uploadedFileIds: z.array(z.string()).optional(),
  utrSelection: z.object({
    filters: z.object({
      codes: z.array(z.string()).optional(),
      types: z.array(z.string()).optional()
    }).optional()
  }).optional(),
  utrs: z.array(z.string()).optional(),
  prompt: z.union([
    z.string(),
    z.object({
      template: z.string().optional(),
      customPrompt: z.string().optional(),
      variables: z.record(z.any()).optional(),
      templateId: z.string().optional()
    })
  ]),
  options: z.object({
    relevanceThreshold: z.number().min(0).max(1).optional().default(0.7),
    includeExplanation: z.boolean().optional().default(true),
    includeMatchedContent: z.boolean().optional().default(true),
    maxResults: z.number().min(1).max(1000).optional().default(50),
    aiModel: z.string().optional()
  }).optional(),
  testType: z.enum(Object.values(TestExecutionType) as [string, ...string[]]).optional()
});

// Schema for test history query
export const testHistoryQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  offset: z.coerce.number().min(0).optional().default(0),
  status: z.enum(Object.values(TestExecutionStatus) as [string, ...string[]]).optional(),
  testType: z.enum(Object.values(TestExecutionType) as [string, ...string[]]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  createdBy: z.string().optional()
});

// Schema for file upload metadata
export const fileUploadMetadataSchema = z.object({
  description: z.string().max(500).optional(),
  tags: z.array(z.string()).optional()
});


