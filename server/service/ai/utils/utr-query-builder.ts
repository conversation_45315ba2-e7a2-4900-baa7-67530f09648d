/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { FilterQuery } from 'mongoose';
import type { UniversalTrackerPlain } from '../../../models/universalTracker';
import type { TypeFilter } from '../ai-testing-types';

/**
 * MongoDB query condition for type matching
 * This type represents the structure of the query we build
 */
type UTRTypeQueryCondition = FilterQuery<UniversalTrackerPlain>;

/**
 * Utility function to build MongoDB query conditions for UTR type matching.
 * Checks both the 'type' field and 'alternatives' object for matching standard codes.
 * Also supports filtering by typeTags for sub-category matching.
 * 
 * @param types - Array of TypeFilter objects with type and optional typeTags
 * @returns MongoDB query condition object that can be added to a query, or null if no types provided
 */
export function buildUTRTypeConditions(types: TypeFilter[]): UTRTypeQueryCondition | null {
  if (!types || types.length === 0) {
    return null;
  }

  const typeConditions: any[] = [];
  
  types.forEach(typeFilter => {
    const { type, typeTags } = typeFilter;
    
    // Build condition for direct type match
    const directMatchCondition: any = { type };
    
    // If typeTags are specified, add them to the condition
    if (typeTags && typeTags.length > 0) {
      directMatchCondition.typeTags = { $in: typeTags };
    }
    
    typeConditions.push(directMatchCondition);
    
    // Build condition for alternatives match
    const alternativeCondition: any = {
      [`alternatives.${type}`]: { $exists: true }
    };
    
    // If typeTags are specified, check them in the alternative
    if (typeTags && typeTags.length > 0) {
      alternativeCondition[`alternatives.${type}.typeTags`] = { $in: typeTags };
    }
    
    typeConditions.push(alternativeCondition);
  });
  
  return { $or: typeConditions };
}