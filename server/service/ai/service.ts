/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import ScorecardFactory from '../scorecard/ScorecardFactory';
import type { ObjectId } from 'bson';
import type { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import type { UniversalTrackerValueExtended } from '../../models/universalTrackerValue';
import { NotApplicableTypes } from '../../models/universalTrackerValue';
import { getIndustryText } from '../reporting/FrameworkMapping';
import type { InitiativePlain, MaterialityMap } from '../../models/initiative';
import type { UniversalTrackerPlain } from '../../models/universalTracker';
import { getStandardName } from '../utr/standards';
import { standards } from '@g17eco/core';
import moment from 'moment';
import type { SurveyModelPlain } from '../../models/survey';
import ContextError from '../../error/ContextError';
import { Materiality } from '../../models/materiality';
import type { AIModelFactory } from './AIModelFactory';
import { AIModelType, getAIModelFactory } from './AIModelFactory';
import type { AIPrompt, AIResponse } from './models/AIModel';
import type { UtrvAssistantInputManager } from './utrv-assistant/UtrvAssistantInputManager';
import { getUtrvAssistantInputManager } from './utrv-assistant/UtrvAssistantInputManager';
import type { AdditionalContext, UtrvPromptInput } from './utrv-assistant/types';
import type { FurtherNotesDataContext, GetFurtherNotesParams } from './types';
import { RefineAction, type AIUtrvSuggestion, type RefineFurtherNotesParams } from './types';
import { UtrvAssistantPromptGenerator } from './utrv-assistant/UtrvAssistantPromptGenerator';
import { zodResponseFormat } from 'openai/helpers/zod';
import { utrvAssistantResponseDto } from '../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { DEFAULT_MAX_TOKEN } from './utrv-assistant/constants';
import { fallbackSuggestion, ToolType } from './constants';
import type { ChatGPT } from './models/ChatGPT';

let instance: AiService | undefined;

export class AiService {
  constructor(
    private logger: Logger,
    private aiFactory: AIModelFactory,
    private assistantInputManager: UtrvAssistantInputManager,
    private defaultModel: AIModelType = AIModelType.ChatGPT
  ) {}

  public async runCompletion({
    messages,
    maxTokens,
    modelType = this.defaultModel,
  }: {
    messages: AIPrompt[];
    maxTokens?: number;
    modelType: AIModelType;
  }) {
    return this.aiFactory.getAiModel(modelType).runCompletion(messages, maxTokens);
  }

  private getMateriality(sdgCode: string, materialityMap?: MaterialityMap) {
    if (materialityMap) {
      for (const key of [Materiality.Low, Materiality.Medium, Materiality.High]) {
        if (materialityMap[key].includes(sdgCode)) {
          return key;
        }
      }
    }
    return Materiality.None;
  }

  public async getReportText({
    question,
    modelType = this.defaultModel,
  }: {
    question: string;
    modelType?: AIModelType;
  }) {
    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: question,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getReportText', response.usage, { question });
    }

    return response;
  }

  public async getSummaryMaterialSDGContributions(
    initiative: Pick<InitiativePlain, '_id' | 'industry' | 'name' | 'materialityMap'>,
    survey: Pick<SurveyModelPlain, '_id'>,
    userId: ObjectId,
    modelType: AIModelType = this.defaultModel
  ) {
    const length = 300;
    const scorecardFactory = new ScorecardFactory();
    const scorecard = await scorecardFactory.getBySurveyId(survey._id);

    const userInput = scorecard.goals.map((g) => ({
      SDG: g.sdgCode,
      Materiality: this.getMateriality(g.sdgCode, initiative.materialityMap),
      Contribution: `${g.actual ?? 0}%`,
    }));

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content:
            `A company ${this.getSectorPrompt(initiative)} called "${initiative.name}" has reported ` +
            `the following SDG contributions and materiality in JSON format:\n\n` +
            `"${JSON.stringify(userInput)}"\n\n.` +
            'Your task is to create a short narrative about that data with subheadings for each SDG ' +
            'focussing only on the most material goals and indicating the contribution score.' +
            `Your narrative should be ${length} words or fewer. ` +
            'Use language that is clear, direct, and unambiguous. ' +
            'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".',
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getSummaryMaterialSDGContributions', response.usage, {
        initiativeId: initiative._id,
        surveyId: survey._id,
        userId: userId.toString(),
      });
    }

    return response;
  }

  public async getAnswerToQuestion({
    initiative,
    survey,
    question,
    userId,
    length = 150,
    modelType = this.defaultModel,
  }: {
    initiative: Pick<InitiativePlain, '_id' | 'industry' | 'materialityMap'>;
    survey: Pick<SurveyModelPlain, '_id'>;
    question: string;
    userId: ObjectId;
    length?: number;
    modelType?: AIModelType;
  }) {
    const scorecardFactory = new ScorecardFactory();
    const scorecard = await scorecardFactory.getBySurveyId(survey._id);

    const userInput = scorecard.goals.map((g) => ({
      SDG: g.sdgCode,
      Materiality: this.getMateriality(g.sdgCode, initiative.materialityMap),
      Contribution: `${Math.round(10 * (g.actual ?? 0)) / 10}%`,
    }));

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content:
            `The user wants to know about their company ${this.getSectorPrompt(initiative)}. ` +
            'The company has reported the following SDG contributions and materiality in JSON format:\n\n' +
            `"${JSON.stringify(userInput)}"\n\n.` +
            'Your task is to answer the user question while referring to the data.' +
            `Your answer should be ${length} words or fewer. ` +
            'Use language that is clear, direct, and unambiguous. ',
        },
        {
          role: 'user',
          content: question,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getAnswerToQuestion', response.usage, {
        initiativeId: initiative._id,
        surveyId: survey._id,
        question,
        userId: userId.toString(),
      });
    }

    return response;
  }

  private getSectorPrompt(initiative: Pick<InitiativePlain, 'industry'>): string {
    if (!initiative.industry) {
      return '';
    }
    const sector = getIndustryText(initiative.industry);
    return `in the ${sector} sector `;
  }

  private getStandardsContextPrompt(utr: Pick<UniversalTrackerPlain, 'type'>): string {
    if (!standards[utr.type]) {
      // @TODO - need to support user's currently selected standard on the frontend
      return '';
    }

    const standard = getStandardName(utr.type);
    return (
      `Include any relevant background information such as "${standard}" to support your response, ` +
      `but avoid referencing it explicitly in your explanation. `
    );
  }

  private async prepareFurtherNotesContext({
    utrv,
    draftData,
    initiative,
  }: Pick<GetFurtherNotesParams, 'utrv' | 'draftData' | 'initiative'>) {
    const reportingPeriod = moment(utrv.effectiveDate).format('MMMM YYYY');
    const { previousUtrvs = [] } = await this.assistantInputManager.prepareUtrvPromptInput({ initiative, utrv });

    const convertTableData = () => {
      const tableData = draftData.valueData?.table ?? [[]];
      return Object.fromEntries(tableData.entries());
    };

    const naType =
      draftData.valueData?.notApplicableType === NotApplicableTypes.NA ? NotApplicableTypes.NA : NotApplicableTypes.NR;

    const context: FurtherNotesDataContext = {
      title: utrv.universalTracker.name,
      question: utrv.universalTracker.valueLabel,
      description: utrv.universalTracker.description,
      answers: draftData.valueData?.notApplicableType
        ? naType
        : {
            value: draftData.value ?? utrv.value ?? null,
            unit: draftData.unit,
            numberScale: draftData.numberScale,
            description: draftData.valueData?.data === 'string' ? draftData.valueData.data : null,
            tableData: convertTableData(),
            ...(previousUtrvs.length > 0 ? { previousUtrvs } : {}),
          },
    };

    return { reportingPeriod, previousUtrvs, context };
  }

  private getFurtherNotesPrompt({
    utrv,
    initiative,
    reportingPeriod,
    context,
  }: {
    utrv: UniversalTrackerValueExtended;
    initiative: InitiativePlain;
    reportingPeriod: string;
    context: FurtherNotesDataContext;
  }) {
    const length = 150;
    const utr = utrv.universalTracker;

    const systemPrompt =
      `As a company ${this.getSectorPrompt(
        initiative
      )}, you have answered the following sustainability question: \n\n` +
      `"${context.question}: ${context.description}".\n\n` +
      `Your answer was for the reporting period of ${reportingPeriod}: ` +
      `${JSON.stringify(context.answers)}.\n\n` +
      'Provide explanatory notes about that answer, utilizing all the data provided. ' +
      `${this.getStandardsContextPrompt(utr)} ` +
      `Your explanation should be ${length} words or fewer. ` +
      'Use language that is clear, direct, and unambiguous. ' +
      'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".';

    return systemPrompt;
  }

  public async getFurtherNotesDraft({
    utrv,
    initiative,
    draftData,
    userId,
    modelType = this.defaultModel,
  }: GetFurtherNotesParams) {
    // Check if metric is private and return appropriate response
    if (utrv.isPrivate) {
      this.logger.info('Skipping AI further notes generation for private metric', { utrvId: utrv._id, initiativeId: initiative._id });
      return {
        content: 'This metric has been marked as private and is excluded from AI processing.',
        usage: undefined,
      } satisfies AIResponse;
    }

    const { reportingPeriod, context } = await this.prepareFurtherNotesContext({ utrv, draftData, initiative });

    const systemPrompt = this.getFurtherNotesPrompt({ utrv, initiative, reportingPeriod, context });

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getFurtherNotesDraft', response.usage, {
        initiativeId: initiative._id.toString(),
        utrId: utrv.universalTracker._id.toString(),
        userId: userId.toString(),
      });
    }

    return response;
  }

  public async refineFurtherNotes({
    utrv,
    initiative,
    draftData,
    textToRefine,
    action,
    additionalContext,
    userId,
    modelType = this.defaultModel,
  }: RefineFurtherNotesParams) {
    // Check if metric is private and return appropriate response
    if (utrv.isPrivate) {
      this.logger.info('Skipping AI further notes refinement for private metric', { utrvId: utrv._id, initiativeId: initiative._id });
      return {
        content: 'This metric has been marked as private and is excluded from AI processing.',
        usage: undefined,
      } satisfies AIResponse;
    }

    let instruction = '';
    switch (action) {
      case RefineAction.Rewrite:
        instruction = "I'd like to see another approach to this question. Please provide an alternative response.";
        break;
      case RefineAction.Shorten:
        instruction =
          'Make the text 10%-30% shorter with the following instructions:\n- Maintain style and tone\n- Reduce repetition\n- Preserve key information';
        break;
      default:
        throw new ContextError('Invalid refine action', { action });
    }
    if (additionalContext) {
      const userInstruction = `Additional instructions from the user: ${additionalContext}`;
      instruction = instruction ? `${instruction}\n\n${userInstruction}` : userInstruction;
    }

    const { reportingPeriod, context } = await this.prepareFurtherNotesContext({ utrv, draftData, initiative });

    const systemPrompt =
      `You are an expert sustainability report writer. ` +
      `A user has provided you with a piece of text and an instruction to refine it. ` +
      `The text is an explanatory note for a sustainability metric. ` +
      `Your refined version should maintain the persona of a company reporting on its activities. ` +
      `Use language that is clear, direct, and unambiguous. ` +
      `Refer to yourself as “we” or “our”, not "Our company" or "Our organization".\n\n` +
      `The original context for the text was:\n` +
      this.getFurtherNotesPrompt({ utrv, initiative, reportingPeriod, context });

    const userPrompt = `Please refine the following text based on the instruction.\n\nInstruction: ${instruction}\n\nText to refine:\n"""\n${textToRefine}\n"""`;

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: userPrompt,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('refineFurtherNotes', response.usage, {
        userId: userId.toString(),
        action,
        additionalContext,
      });
    }

    return response;
  }

  public async getInputTextDraft(utrId: ObjectId, userId: ObjectId, modelType: AIModelType = this.defaultModel) {
    const length = 150;

    const utr = await UniversalTrackerRepository.findById(utrId);
    if (!utr) {
      throw new ContextError('Invalid Universal Tracker Id', { utrId });
    }

    const context = {
      // title: utr.name,
      question: utr.valueLabel,
      description: utr.description,
    };

    const systemPrompt =
      'As a company, you have answered the following sustainability question: \n\n' +
      `"${context.question}: ${context.description}".\n\n` +
      'Create an answer for this question from the point of view of the company ' +
      `Provide and answer for this question, in less than ${length} words. ` +
      'Use language that is clear, direct, and unambiguous. ' +
      'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".';

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getInputTextDraft', response.usage, { utrId, userId: userId.toString() });
    }

    return response;
  }

  private trackUsage(serviceName: string, usage: AIResponse['usage'], contextData: {}) {
    // @TODO We should keep track of usage in a better place in the future...
    this.logger.info('AI Service usage', {
      serviceName,
      usage,
      contextData,
    });
  }

  public async getUtrvAssistantResponse({
    initiative,
    utrv,
    additionalContext,
    aiModelType = this.defaultModel,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    additionalContext?: AdditionalContext;
    aiModelType?: AIModelType;
  }): Promise<{ modelVersion: string; promptInput: UtrvPromptInput; content: AIUtrvSuggestion }> {
    const promptInput = await this.assistantInputManager.prepareUtrvPromptInput({
      initiative,
      utrv,
      additionalContext,
    });

    // Check if metric is private and return fallback if true
    if (utrv.isPrivate) {
      this.logger.warn('Skipping AI processing for private metric', { utrvId: utrv._id, initiativeId: initiative._id });
      return {
        modelVersion: 'N/A - Private Metric',
        promptInput,
        content: {
          ...fallbackSuggestion,
          questionExplanation: 'This metric has been marked as private and is excluded from AI processing.',
        },
      };
    }

    const prompt = new UtrvAssistantPromptGenerator(promptInput).generatePrompt();
    const responseFormat = zodResponseFormat(utrvAssistantResponseDto, 'utrvAssistantExtraction');
    const aiModel = this.aiFactory.getAiModel(aiModelType);
    const response = await aiModel.parseCompletion<AIUtrvSuggestion>([prompt], DEFAULT_MAX_TOKEN, responseFormat);
    return { modelVersion: aiModel.getModelVersion(), promptInput, content: response.content || fallbackSuggestion };
  }

  public async getDocumentUtrvAssistantResponse({
    initiative,
    utrv,
    assistantId,
    relatedDocumentIds,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    assistantId: string;
    relatedDocumentIds: string[];
  }) {
    this.logger.info('Asking AI to answer UTR...', { utrvId: utrv._id, initiativeId: initiative._id });
    const promptInput = await this.assistantInputManager.prepareUtrvPromptInput({ initiative, utrv });
    const aiModel = this.aiFactory.getAiModel(AIModelType.ChatGPT) as ChatGPT;
    const prompt = new UtrvAssistantPromptGenerator(promptInput).generatePrompt();
    const threadMessageContent = `
      The attached files are the documents to analyze.
      Please answer the UTR based on the content of the uploaded documents if have any and the previousUtrvs.
      Return only structured data (no explanations or commentary).
      `
      .trim()
      .concat(prompt.content);
    const result = await aiModel.runThreadWithAssistant({
      assistantId,
      message: {
        role: 'user',
        content: threadMessageContent,
        attachments: relatedDocumentIds.map((id) => ({
          file_id: id,
          tools: [{ type: ToolType.CodeInterpreter }],
        })),
      },
      jsonSchema: utrvAssistantResponseDto,
    });
    return { modelVersion: aiModel.getModelVersion(), promptInput, content: result || fallbackSuggestion };
  }
}

export const getAiService = () => {
  if (!instance) {
    instance = new AiService(wwgLogger, getAIModelFactory(), getUtrvAssistantInputManager(), AIModelType.ChatGPT);
  }
  return instance;
};
