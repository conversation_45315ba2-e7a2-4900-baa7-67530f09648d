/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

// Health check response
export interface DataIntegrationHealthCheckResult {
  airbyte: {
    [key: string]: string | number | boolean;
  };
  snowflake: {
    [key: string]: string | number | boolean;
  };
  fivetran: {
    [key: string]: string | number | boolean;
  };
}

// Fivetran Group (maps to initiativeId)
export interface FivetranGroup {
  id: string;
  name: string;
  created_at: string;
  last_updated: string;
}

// Fivetran Destination
export interface FivetranDestination {
  id: string;
  group_id: string;
  service: string;
  region: string;
  time_zone_offset: string;
  setup_status: 'incomplete' | 'connected' | 'broken';
  config: Record<string, any>;
  created_at: string;
  last_updated: string;
}

// Fivetran Connection/Connector
export interface FivetranConnection {
  id: string;
  group_id: string;
  service: string;
  service_version: number;
  schema: string;
  connected_by: string;
  created_at: string;
  succeeded_at: string | null;
  failed_at: string | null;
  sync_frequency: number;
  schedule_type: 'auto' | 'manual';
  status: {
    setup_state: 'incomplete' | 'connected' | 'broken';
    sync_state: 'scheduled' | 'syncing' | 'paused' | 'rescheduled';
    update_state: 'on_schedule' | 'delayed';
    is_historical_sync: boolean;
    tasks: Array<{
      code: string;
      message: string;
    }>;
    warnings: Array<{
      code: string;
      message: string;
    }>;
  };
  config: Record<string, any>;
}

// Data Integration Models
export interface DataIntegrationConnector {
  id: string;
  name: string;
  service: string;
  icon_url?: string;
  description?: string;
  supported_features: {
    supported_sync_modes: string[];
    supported_destination_sync_modes: string[];
    supports_db_sync: boolean;
    supports_log_based_replication: boolean;
  };
  configuration_schema: Record<string, any>;
}

export interface CreateConnectCardRequest {
  connectorId: string;
  redirectUri: string;
}

export interface CreateConnectCardResponse {
  message?: string;
  connect_card?: {
    uri?: string;
    token?: string;
  };
  connector_id?: string;
  connect_card_config?: {
    redirect_uri?: string;
  };
}
