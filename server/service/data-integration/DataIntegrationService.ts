/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import axios from 'axios';
import config from '../../config';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import {
  DataIntegrationHealthCheckResult,
  FivetranConnection,
  FivetranDestination,
  FivetranGroup,
  DataIntegrationConnector,
  CreateConnectCardRequest,
  CreateConnectCardResponse,
} from './types';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';

export class DataIntegrationService {
  private baseUrl: string;

  constructor() {
    const { host, port } = config.dataIntegrationMicroservice;
    this.baseUrl = `http://${host}:${port}`;
  }

  // Health and Info
  async getHealth(): Promise<DataIntegrationHealthCheckResult> {
    return this.makeRequest<DataIntegrationHealthCheckResult>('GET', '/health');
  }

  async isEnabled(initiativeId: string): Promise<{ enabled: boolean }> {
    return this.makeRequest<{ enabled: boolean }>('GET', `/fivetran/${initiativeId}/enabled`);
  }

  async enable(initiativeId: string): Promise<{ enabled: boolean, message: string }> {
    return this.makeRequest<{ enabled: boolean, message: string }>('POST', `/fivetran/${initiativeId}/enabled`);
  }

  async getFivetranInfo(): Promise<{
    groups: FivetranGroup[],
    destinations: FivetranDestination[],
    connections: FivetranConnection[]
  }> {
    return this.makeRequest<{
      groups: FivetranGroup[],
      destinations: FivetranDestination[],
      connections: FivetranConnection[]
    }>('GET', '/fivetran/info');
  }

  // Connection Management
  async getConnections(initiativeId: string): Promise<{ connections: DataIntegrationConnector[] }> {
    return this.makeRequest<{ connections: DataIntegrationConnector[] }>('GET', `/fivetran/${initiativeId}/connections`);
  }

  async deleteConnection(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('DELETE', `/fivetran/${initiativeId}/connections/${connectionId}`);
  }

  async pauseConnection(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('PATCH', `/fivetran/${initiativeId}/connections/${connectionId}/pause`, {});
  }

  async resumeConnection(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('PATCH', `/fivetran/${initiativeId}/connections/${connectionId}/resume`, {});
  }

  async syncConnection(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('POST', `/fivetran/${initiativeId}/connections/${connectionId}/sync`, {});
  }

  async resyncConnection(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('POST', `/fivetran/${initiativeId}/connections/${connectionId}/resync`, {});
  }

  async testConnection(initiativeId: string, connectionId: string): Promise<{ success: boolean, message: string }> {
    return this.makeRequest<{ success: boolean, message: string }>('POST', `/fivetran/${initiativeId}/connections/${connectionId}/test`, {});
  }

  async getConnectionInfo(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('GET', `/fivetran/${initiativeId}/connections/${connectionId}`);
  }

  async getConnectionSchemas(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('GET', `/fivetran/${initiativeId}/connections/${connectionId}/schemas`);
  }

  async toggleConnectionSchemaTable(initiativeId: string, connectionId: string, schemaName: string, tableName: string, enabled: boolean): Promise<void> {
    return this.makeRequest<void>('PATCH', `/fivetran/${initiativeId}/connections/${connectionId}/schemas/${schemaName}/tables/${tableName}`, { enabled });
  }

  async getConnectors(initiativeId: string): Promise<{ connectors: DataIntegrationConnector[] }> {
    return this.makeRequest<{ connectors: DataIntegrationConnector[] }>('GET', `/fivetran/${initiativeId}/connectors`);
  }

  // Embedded Session Management (PBF)
  async createConnectCard(initiativeId: string, request: CreateConnectCardRequest): Promise<CreateConnectCardResponse> {
    return this.makeRequest<CreateConnectCardResponse>('POST', `/fivetran/${initiativeId}/connect-card`, {
      connector_id: request.connectorId,
      redirect_uri: request.redirectUri
    });
  }

  // Semantic Model Management
  async getDatabaseSchemas(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('GET', `/fivetran/${initiativeId}/connections/${connectionId}/snowflake/schemas`);
  }

  async getSemanticModel(initiativeId: string, connectionId: string): Promise<void> {
    return this.makeRequest<void>('GET', `/fivetran/${initiativeId}/connections/${connectionId}/snowflake/semantic-model`);
  }

  async setDatabaseSemanticModel(initiativeId: string, connectionId: string, yamlContent: object): Promise<void> {
    return this.makeRequest<void>('POST', `/fivetran/${initiativeId}/connections/${connectionId}/snowflake/semantic-model`, { yaml_content: yamlContent });
  }

  async getSemanticModelChat(initiativeId: string, message: string, conversationId?: string): Promise<void> {
    return this.makeRequest<void>('POST', `/snowflake/${initiativeId}/chat`, { message, conversation_id: conversationId });
  }

  async getSemanticModelChatUtrContext(
    initiativeId: string,
    context: {
      utr: Pick<UniversalTrackerPlain, 'name' | 'valueLabel' | 'description' | 'instructions' | 'type'>,
      utrv: Pick<UniversalTrackerValuePlain, 'effectiveDate'>
    },
    conversationId?: string
  ): Promise<void> {
    const message = `
      You are a sustainability assistant helping the user to find answers to questions about the sustainability of the company.
      The data is stored in a Snowflake database.

      The context for the question that needs answering is:
      Date: ${context.utrv.effectiveDate}
      Instructions: ${context.utr.instructions}
      Question: ${context.utr.valueLabel}
      Description: ${context.utr.description}
      Label: ${context.utr.name}

      This information will be passed to a Cortex model to answer the question. I need you to create a context object that will be passed to the Cortex model to optimize the answer.
    `;

    return this.makeRequest<void>('POST', `/snowflake/${initiativeId}/chat`, { message, conversation_id: conversationId });
  }

  private async makeRequest<T>(method: 'GET' | 'POST' | 'PATCH' | 'DELETE', path: string, data?: any): Promise<T> {
    if (!config.dataIntegrationMicroservice.host) {
      throw new ContextError('Data integration microservice is not configured', {
        host: config.dataIntegrationMicroservice.host,
        port: config.dataIntegrationMicroservice.port,
      });
    }

    const url = `${this.baseUrl}${path}`;

    try {
      const response = await axios({
        method,
        url,
        data,
      });

      return response.data.data as T
    } catch (error) {
      wwgLogger.error(new ContextError(`Data integration service error: ${method} ${path}`, {
        url,
        method,
        error: error instanceof Error ? error.message : 'Unknown error',
        data,
      }));

      // Re-throw with original error structure for consistent API responses
      if (axios.isAxiosError(error) && error.response) {
        const axiosError = new Error(error.message);
        (axiosError as any).status = error.response.status;
        (axiosError as any).data = error.response.data;
        throw axiosError;
      }

      throw error;
    }
  }

}

let instance: DataIntegrationService;
export const getDataIntegrationService = () => {
  if (!instance) {
    instance = new DataIntegrationService();
  }
  return instance;
};
