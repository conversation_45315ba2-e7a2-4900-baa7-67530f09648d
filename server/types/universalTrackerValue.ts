import { SurveyUserRoles } from '../types/roles';
import { ActionList } from '../service/utr/constants';
import { ObjectId } from 'bson';
import { Types } from 'mongoose';

/**
 * Shared interfaces to avoid circular dependencies
 */
export interface Tags {
  pic: string[];
  ese: string[];
  sdg: string[];
  [key: string]: string[];
}

export type EvidenceData<T = Types.ObjectId> = {
  documentId: T;
  description?: string;
};

/** @todo: This is copied from InsightDashboard, need to clean up UtrvFilter in the future */
export enum UtrvFilter {
  /** @deprecated **/
  All = 'all',
  Verified = 'verified',
  Assured = 'assured',
  AllAnswered = 'allAnswered',
}

export const UtrvStatuses = {
  [UtrvFilter.AllAnswered]: [ActionList.Verified, ActionList.Updated],
  [UtrvFilter.Verified]: [ActionList.Verified],
  [UtrvFilter.Assured]: [ActionList.Verified],
};

export interface UtrvIds {
  [SurveyUserRoles.Stakeholder]: ObjectId[];
  [SurveyUserRoles.Verifier]: ObjectId[];
}
