/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

/**
 * Converts an unknown error to a string message
 * @param error - The error to convert
 * @returns A string representation of the error
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

/**
 * Gets a safe string representation of an error for logging
 * @param error - The error to convert
 * @returns An object with error details safe for logging
 */
export function getErrorDetails(error: unknown): { message: string; stack?: string } {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack
    };
  }
  return {
    message: String(error)
  };
}