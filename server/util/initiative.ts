import type { InitiativePlain} from '../models/initiative';
import { PERMISSION_GROUPS } from '../models/initiative';
import { AppCode } from '../service/app/AppConfig';

export const isCTStarter = (initiative: Pick<InitiativePlain, 'permissionGroup' | 'appConfigCode'>) => {
  const { permissionGroup, appConfigCode } = initiative;
  return (
    permissionGroup === PERMISSION_GROUPS.COMPANY_TRACKER_STARTER ||
    [AppCode.CompanyTrackerStarter, AppCode.TOLI].includes(appConfigCode as AppCode)
  );
};

export const isSGXESGenome = (initiative: Pick<InitiativePlain, 'permissionGroup' | 'appConfigCode'>) => {
  const { permissionGroup, appConfigCode } = initiative;
  return permissionGroup === PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT && appConfigCode === AppCode.SGXESGenome;
};

export const hasTag = (initiative: Pick<InitiativePlain, 'tags'>, tag: string) => {
  return !!initiative.tags?.includes(tag);
}

export const getAddressText = (address: InitiativePlain['address']) => {
  if (!address) {
    return '-';
  }
  const parts = [
    address.line1,
    address.line2,
    address.city,
    address.postcode
  ].filter(part => part && part.trim());
  
  return parts.join(', ');
}
