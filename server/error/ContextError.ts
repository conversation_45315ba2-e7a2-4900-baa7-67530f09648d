/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export interface ErrorDetails extends ErrorOptions {
  /** developer message, when actual message is targeting users **/
  debugMessage?: string

  /**
   * Must be current user
   * it will be merged with request.user properties based on configured sentry options
   **/
  user? : { id?: string, [k: string]: unknown }

  /** tags will be applied as sentry tags **/
  tags?: string[]

  /**
   * HTTP status code: 400, 401, 500 etc.
   * Allow to set the response error if applicable
   * Note: status < 500 will be ignored by Sentry error handler
   * **/
  status?: number

  /**
   * Response data from the API call
   */
  responseData?: unknown;

  /**
   * Everything else that provide context
   * Note that any ObjectId property will only be converted to string on
   * root level of the error details, inner ones will remain as ObjectId's
   **/
  [k: string]: unknown
}

export default class ContextError extends Error {

  /**
   * API based error response data
   * It is only available if the error is caused by an API call
   * and the API call returned a response data
   * This is useful for better error handling
   */
  responseData?: ErrorDetails['responseData'];

  context?: Omit<ErrorDetails, 'cause' | 'responseData'>;

  constructor(m: string, context?: ErrorDetails) {
    const cause = context?.cause;
    super(m, { cause });
    delete context?.cause;

    if (context?.responseData) {
      this.responseData = context.responseData;
      delete context.responseData;
    }

    this.context = context;
    Object.setPrototypeOf(this, ContextError.prototype);

    // Prioritize the response data from the cause
    if (cause instanceof ContextError && cause.responseData) {
      this.responseData = cause.responseData;
    }
  }

  addContext(context: Partial<ErrorDetails>) {
    this.context = { ...this.context, ...context };
  }
}
