#!/usr/bin/env ts-node

import { wwgLogger } from '../service/wwgLogger';
import ContextError from '../error/ContextError';
import { readCSVFile } from '../service/file/writer/CsvFileWriter';
import { writeFileSync } from 'fs';
import { join } from 'path';
import type { DefinitionMapItem, ReferenceItem, ReferenceRow, ReportDefinition } from '../service/reporting/types';
import type { IFRSDefinitionRow, IFRSReferencesDefinitionRow } from '../service/reporting/issb/types';

const [, , ...args] = process.argv;
const now = Date.now();

const cleanUp = (exitCode: 0 | 1) => {
  wwgLogger.on('finish', function () {
    console.info(`wwgLogger finished draining events, exiting now with code ${exitCode}`);
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.on('error', function (err) {
    console.error(new ContextError('Error while draining wwgLogger events', { cause: err }));
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.end();
  // Let logger and other things close properly, but in case it still going kill it
  setTimeout(() => {
    console.info(`Timeout reached, exiting now with code ${exitCode}`);
    process.exit(exitCode);
  }, 5000);
};

/**
 * Details of latest taxonomy:
 * @link: https://www.ifrs.org/issued-standards/ifrs-taxonomy/ifrs-taxonomy-illustrated/
 *
 * Data is taken from Excel sheet provided in the link below:
 * @link: https://www.ifrs.org/content/dam/ifrs/project/ifrs-sustainability-disclosure-taxonomy/proposed-taxonomy/ifrs-sds-proposed-iti-en-r-2023-07-27.xlsx
 *
 * Exported combined_entry_point sheet to CSV to use as entry source
 * Exported List of references sheet to CSV to use as reference source
 */
const entrySource = join(__dirname, '../../__tmp/ifrs-sds-proposed-iti-en-r-2023-07-27-entry.csv');
const referenceSource = join(__dirname, '../../__tmp/ifrs-sds-proposed-iti-en-r-2023-07-27-references.csv');
const outputDir = join(__dirname, '../service/reporting/issb/IssbDefinitions.ts');
const [entrySrc = entrySource, referenceSrc = referenceSource, output = outputDir] = args;

const shouldSkipNames = ['member', 'axis', 'https'];
const shouldSkipRefs = ['ghg protocol', 'kyoto protocol'];

const getRefType = (refType: string) => {
  return ['Disclosure', 'Example'].find((type) => refType.toLowerCase().includes(type.toLowerCase()));
};

const generateDataPointId = (refRow: IFRSReferencesDefinitionRow) => {
  return [
    refRow['ref:Name'],
    [refRow['ref:Number'], refRow['ref:Paragraph']].join('.'),
    refRow['ref:Subparagraph'],
    refRow['ref:Clause'] ? `(${refRow['ref:Clause']})` : undefined,
    getRefType(refRow['Reference type']),
  ]
    .filter(Boolean)
    .join(' ');
};

const buildRefsFromDefinition = (refRows: IFRSReferencesDefinitionRow[]) => {
  return refRows.reduce((acc, refRow) => {
    if (shouldSkipRefs.some((skipText) => refRow['ref:Name'].toLowerCase().includes(skipText))) {
      return acc;
    }
    acc.push([
      ['Name', refRow['ref:Name']],
      ['Number', refRow['ref:Number']],
      /**
       * Create a custom section for sorting purposes.
       * @todo: Since the purpose of section is to determine major section which is Number, so should refactor the code to use Number directly
       */
      ['Section', refRow['ref:Number']],
      /**
       * Create a custom dataPointId for sorting purposes, which is the actual combined reference string
       * @example: IFRS S1.44 a (ii) Disclosure
       */
      ['dataPointId', generateDataPointId(refRow)],
      ...(refRow['ref:Paragraph'] ? [['Paragraph', refRow['ref:Paragraph']] satisfies ReferenceItem] : []),
      ...(refRow['ref:Subparagraph'] ? [['Subparagraph', refRow['ref:Subparagraph']] satisfies ReferenceItem] : []),
      ...(refRow['ref:Clause'] ? [['Clause', refRow['ref:Clause']] satisfies ReferenceItem] : []),
      ...(refRow['ref:Section'] ? [['OriginalSection', refRow['ref:Section']] satisfies ReferenceItem] : []),
      ...(refRow['ref:Subsection'] ? [['Subsection', refRow['ref:Subsection']] satisfies ReferenceItem] : []),
      /** @todo: Create a link to a category of ISSB for this tag, used for filtering tags in sections
       * Suggestion: Leverage AI to generate it
       * */
    ]);
    return acc;
  }, [] as ReferenceRow[]);
};

const main = async ({ entrySrc, referenceSrc }: { entrySrc: string; referenceSrc: string }) => {
  const data = (await readCSVFile(entrySrc)) as IFRSDefinitionRow[];
  const refData = (await readCSVFile(referenceSrc)) as IFRSReferencesDefinitionRow[];
  wwgLogger.info('Generating International Financial Reporting Standards (IFRS) definitions', {
    rows: data.length,
    refRows: refData.length,
  });

  let duplicateKeys = 0;
  const cleanedUpData = data.reduce((acc, row) => {
    const key = `ifrs-sds:${row['Concept name']}`;
    if (shouldSkipNames.some((skipText) => key.toLowerCase().includes(skipText))) {
      return acc;
    }

    if (acc[key]) {
      duplicateKeys++;
      wwgLogger.info(`Duplicate key found: ${key}`);
    }

    const refRows = refData.filter((refRow) => refRow['Concept prefix/name'] === key);
    const references = buildRefsFromDefinition(refRows);

    acc[key] = {
      label: row['Standard label'],
      technicalName: key,
      references,
    } satisfies DefinitionMapItem;
    return acc;
  }, {} as ReportDefinition);

  if (duplicateKeys) {
    wwgLogger.warn(`Found ${duplicateKeys} duplicate keys`);
  }

  // Generate
  const fileContent = `
// Generated file, do not edit
import type { ReportDefinition } from "../types";

export const ISSBDefinitions: ReportDefinition = ${JSON.stringify(cleanedUpData, null, 2)};
  `;
  wwgLogger.info(`Writing to ${output}`, { keys: Object.keys(cleanedUpData).length });

  writeFileSync(output, fileContent);
  return {
    data: cleanedUpData,
    output,
  };
};

main({ entrySrc, referenceSrc })
  .then(() => {
    const durationInMilliseconds = Date.now() - now;
    console.info(`Generated IFRS definitions work run in ${durationInMilliseconds}ms`, { durationInMilliseconds });
  })
  .then(() => {
    console.info('About to close the process with success');
    cleanUp(0);
  })
  .catch((err) => {
    wwgLogger.error(err);
    cleanUp(1);
  });
