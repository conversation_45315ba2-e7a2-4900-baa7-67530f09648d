#!/usr/bin/env node
import '../setup';
import cron from 'node-cron';
import UniversalTrackerScheduleCommand from '../../command/UniversalTrackerScheduleCommand';
import moment from 'moment';

const [ , , ...args] = process.argv;

/** Not sure if this CRON is still being used or not */
const cmd = new UniversalTrackerScheduleCommand();
const start = async () => {
    const result = await cmd.execute();
    console.log(JSON.stringify(result));
    return result;
};

let resetDate = new Date() ;
// node dist/scripts/utr/universalTrackersScheduleCron.js --reset --scheduleId=5c10fe5390aafa22ce52b8f8
if (args.includes('--reset')) {
  let scheduleId;

  for (const arg of args) {
    if (arg.indexOf('--scheduleId', 0) !== -1) {
      const [, value] =  arg.split('=');
      scheduleId = value;
    }

    if (arg.startsWith('--resetDate')) {
      const [, dateValue] = arg.split('=');
      resetDate = moment(dateValue).endOf('day').toDate();
    }
  }

  const text = scheduleId ? 'with schedule Id' + scheduleId : '';
  console.log(`Running reset ${text}`);

  cmd.reset(resetDate, scheduleId)
    .then((result) => console.log('DONE Reset', result))
    .then(() => process.exit(0))
    .catch(console.log);
} else {
  cron.schedule('10 5 * * *', () => {
    start().then(() => console.log('DONE')).catch(console.log);
  });
}
