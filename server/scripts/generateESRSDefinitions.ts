#!/usr/bin/env ts-node

import { wwgLogger } from '../service/wwgLogger';
import ContextError from '../error/ContextError';
import { readCSVFile } from '../service/file/writer/CsvFileWriter';
import { writeFileSync } from 'fs';
import { join } from 'path';
import type { ESRSDefinitionRow } from '../service/reporting/csrd/inline-viewer';
import type { DefinitionMapItem, ReportDefinition } from '../service/reporting/types';

const [, , ...args] = process.argv;
const now = Date.now();

const cleanUp = (exitCode: 0 | 1) => {
  wwgLogger.on('finish', function () {
    console.info(`wwgLogger finished draining events, exiting now with code ${exitCode}`);
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.on('error', function (err) {
    console.error(new ContextError('Error while draining wwgLogger events', { cause: err }));
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.end();
  // Let logger and other things close properly, but in case it still going kill it
  setTimeout(() => {
    console.info(`Timeout reached, exiting now with code ${exitCode}`);
    process.exit(exitCode);
  }, 5000);
}

/**
 * Details of latest taxonomy:
 * @link:  https://www.efrag.org/en/projects/esrs-xbrl-taxonomy/concluded
 *
 * Data is taken from Excel sheet provided in the link below:
 * @link: https://xbrl.efrag.org/downloads/Annex-1-ESRS-Set1-XBRL-Taxonomy-illustrated-in-Excel.xlsx
 * Exported DefinitionLinkbase sheet to CSV and used as source input for the script
 */
const defaultSource = join(__dirname, '../../__tmp/ESRS-Set1-DefinitionLinkbase.csv');
const outputDir = join(__dirname, '../service/reporting/csrd/CsrdDefinitions.ts');
const [source = defaultSource, output = outputDir] = args;

const shouldSkipNames = ['member', 'axis'];
const shouldSkipRoles = ['disaggregation', 'country list', 'axis', 'breakdown', 'enumeration'];

const main = async (source: string) => {
  const data = await readCSVFile(source) as ESRSDefinitionRow[];
  wwgLogger.info('Generating European Sustainability Reporting Standard (CSRD) definitions', {
    source,
    rows: data.length,
  });

  let duplicateKeys = 0
  const cleanedUpData = data.reduce((acc, row) => {

    const key = row['Technical Name'];
    if (shouldSkipNames.some(skipText => key.toLowerCase().includes(skipText))) {
      return acc;
    }

    if (shouldSkipRoles.some(skipText => row['Role'].toLowerCase().includes(skipText))) {
      return acc;
    }


    if (acc[key]) {
      duplicateKeys++;
      wwgLogger.info(`Duplicate key found: ${key}`);
    }

    const references = row['References'].split(';').filter(Boolean);
    acc[key] = {
      label: row['Label en'],
      technicalName: key,
      references: references.length ? [
        references.map((ref: string) => {
          const [label, ...value] = ref.split(':');
          return [label.trim(), value.join(':').trim()];
        })
      ] : undefined,
    } satisfies DefinitionMapItem;
    return acc;
  }, {} as ReportDefinition);


  if (duplicateKeys) {
    wwgLogger.warn(`Found ${duplicateKeys} duplicate keys`);
  }

  // Generate
  const fileContent = `
// Generated file, do not edit
import type { ReportDefinition } from "../types";

export const CSRDDefinitions: ReportDefinition = ${JSON.stringify(cleanedUpData, null, 2)};
  `
  wwgLogger.info(`Writing to ${output}`, { keys: Object.keys(cleanedUpData).length });


  writeFileSync(output, fileContent);
  return {
    data: cleanedUpData,
    output
  }
}


main(source)
  .then(() => {
    const durationInMilliseconds = Date.now() - now;
    console.info(`Generated CSRD definitions work run in ${durationInMilliseconds}ms`, { durationInMilliseconds })
  })
  .then(() => {
    console.info('About to close the process with success')
    cleanUp(0);
  })
  .catch(err => {
    wwgLogger.error(err)
    cleanUp(1)
  });
