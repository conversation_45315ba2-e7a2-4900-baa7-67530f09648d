# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Modular Documentation Reference

This codebase uses modular documentation for better maintainability and reuse across AI tools. The following files are automatically included using Claude <PERSON>'s @ import syntax:

### Core System Information
@.agent-docs/core/architecture.md
@.agent-docs/core/commands.md

### Testing Guidelines  
@.agent-docs/testing/framework.md
@.agent-docs/patterns/service-testing.md
@.agent-docs/patterns/error-handling.md
@.agent-docs/patterns/test-data.md

### Architecture & Code Patterns
@.agent-docs/patterns/dependency-injection.md
@.agent-docs/patterns/type-safety.md
@.agent-docs/patterns/architecture.md

## Important Instructions

NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

## Quick Reference

### Development Commands
- `npm start` - Start development server with TypeScript watch mode and auto-restart
- `npm test` - Run all unit tests with Mocha
- `npm run test-coverage` - Generate test coverage report
- `npm run build-ts` - Build TypeScript to dist/

### Key Configuration Files
- `server/config.ts` - Environment-based configuration
- `nodemon.json` - Development server configuration
- `tsconfig.json` / `tsconfig-build.json` - TypeScript compilation settings
- `nyc.config.js` - Test coverage configuration

## Modular Documentation Structure

The comprehensive documentation is organized into reusable modules in `.agent-docs/`:

```
.agent-docs/
├── core/                    # Core system information
│   ├── architecture.md      # System architecture and layers
│   └── commands.md         # Development and testing commands
├── testing/                # Testing documentation
│   └── framework.md        # Testing stack and organization
├── patterns/               # Code and testing patterns
│   ├── service-testing.md  # Service testing templates
│   ├── error-handling.md   # Error handling patterns
│   └── test-data.md        # Test data management
└── tools/                  # AI tool-specific instructions
    ├── CURSOR.md          # Cursor IDE instructions
    ├── OPENAI.md          # OpenAI/ChatGPT instructions
    └── GEMINI.md          # Google Gemini instructions
```

This modular approach allows for:
- **Reduced duplication** across different AI tools
- **Easy maintenance** of documentation
- **Tool-specific optimization** for different AI assistants
- **Consistent context** across all development workflows

### Using Modular Documentation
- Files above are **automatically included** using Claude Code's @ import syntax
- All architectural context and testing patterns are loaded automatically
- The modular approach allows for easy maintenance and consistency across AI tools

## Coding Standards and Anti-Patterns

### ✅ DO
- **Use constructor dependency injection**: `new Service(dep1, dep2)`
- **Keep stub references in tests** to avoid casting
- **Use proper TypeScript types** (avoid `any`)
- **Use `expect().to.be.rejectedWith`** for async test assertions
- **Follow one-way dependency flow** between services

### ❌ DON'T  
- **Don't use factory methods inside services** (only in other factories)
- **Don't access private properties in tests**
- **Don't use try/catch in tests**
- **Don't use `!` operator** to skip type checks
- **Don't create interfaces** until there's a clear need
- **Don't use barrel exports** unless they provide clear benefits
