{"permissions": {"allow": ["mcp__atlassian__getAccessibleAtlassianResources", "mcp__atlassian__getJiraIssue", "mcp__atlassian__editJiraIssue", "Bash(grep:*)", "Bash(npx madge:*)", "Bash(rg:*)", "Bash(npm test)", "Bash(npm run build-ts:*)", "Bash(find:*)", "Bash(npx tsc:*)", "Bash(npm test:*)", "mcp__atlassian__searchJiraIssuesUsingJql", "Bash(git add:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run:*)", "WebFetch(domain:docs.augmentcode.com)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker compose:*)", "mcp__atlassian__atlassianUserInfo"], "deny": []}, "includeCoAuthoredBy": false, "cleanupPeriodDays": 30}