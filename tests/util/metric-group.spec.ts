import { ObjectId } from 'bson';
import { expect } from 'chai';
import { MetricGroupPlain } from '../../server/models/metricGroup';
import { buildHierarchy } from '../../server/util/metric-group';

describe('buildHierarchy', () => {
  const createMetricGroup = (parentId?: ObjectId) => {
    return {
      _id: new ObjectId().toString(),
      parentId,
    } as unknown as MetricGroupPlain;
  };

  it('should build hierarchy with one level', () => {
    const metricGroupOne = createMetricGroup();
    const metricGroupTwo = createMetricGroup();
    const metricGroupThree = createMetricGroup();

    const result = buildHierarchy([metricGroupOne, metricGroupTwo, metricGroupThree]);
    expect(result).to.deep.equal([metricGroupOne, metricGroupTwo, metricGroupThree]);
  });

  it('should build hierarchy with two level', () => {
    const metricGroupOne = createMetricGroup();
    const metricGroupOneChild = createMetricGroup(metricGroupOne._id);
    const metricGroupTwo = createMetricGroup();
    const metricGroupTwoChild = createMetricGroup(metricGroupTwo._id);
    const metricGroupThree = createMetricGroup();

    const result = buildHierarchy([
      metricGroupOne,
      metricGroupOneChild,
      metricGroupTwo,
      metricGroupTwoChild,
      metricGroupThree,
    ]);

    expect(result).to.deep.equal([
      { ...metricGroupOne, subgroups: [metricGroupOneChild] },
      { ...metricGroupTwo, subgroups: [metricGroupTwoChild] },
      metricGroupThree,
    ]);
  });

  it('should build hierarchy with three level', () => {
    const metricGroupOne = createMetricGroup();
    const metricGroupOneChild = createMetricGroup(metricGroupOne._id);
    const metricGroupOneGrandChild = createMetricGroup(metricGroupOneChild._id);
    const metricGroupTwo = createMetricGroup();
    const metricGroupThree = createMetricGroup();

    const result = buildHierarchy([
      metricGroupOne,
      metricGroupOneChild,
      metricGroupOneGrandChild,
      metricGroupTwo,
      metricGroupThree,
    ]);

    expect(result).to.deep.equal([
      {
        ...metricGroupOne,
        subgroups: [{ ...metricGroupOneChild, subgroups: [metricGroupOneGrandChild] }],
      },
      metricGroupTwo,
      metricGroupThree,
    ]);
  });
});
