import { VALID_DOMAIN_REGEX } from '../../server/util/url';
import { z } from 'zod';
import { expect } from 'chai';

describe('util/url', () => {
  describe('VALID_DOMAIN_REGEX', () => {
    const urlSchema = z.string().regex(VALID_DOMAIN_REGEX);

    it('should match valid domain', () => {
      const testUrls = [
        'http://localhost',
        'http://localhost:4001',
        'https://monitoring.g17.eco',
        'https://nightly.g17.eco',
        'https://sg.staging.g17.eco',
        'https://worldwidegeneration.co',
        'https://api.worldwidegeneration.co',
        'https://uae.g17.eco/',
        'https://ksa.g17.eco/marketplace',
        'https://monitoring.g17.eco/emissions-calculator',
        'https://singapore.g17.eco/',
        'https://singapore.g17.eco/sgx-esgenome/reports/625d7f140da3730007c277d1/631ee9b9eaaf450007f6de5e/overview',
      ];

      testUrls.forEach((url) => {
        expect(urlSchema.safeParse(url).success).to.be.true;
      });
    });
    it('should unmatch valid domain', () => {
      const testUrls = [
        'https://localhost',
        'http://sub.g17.eco',
        'http://worldwidegeneration.co',
        'http://random.com',
        'https://notallowed.g18.eco',
        'https://ksa.g18.eco/marketplace',
        'https://monitoring.g18.eco/emissions-calculator',
        'https://singapore.g18.eco/',
        'https://singapore.g18.eco/sgx-esgenome/reports/625d7f140da3730007c277d1/631ee9b9eaaf450007f6de5e/overview',
      ];

      testUrls.forEach((url) => {
        expect(urlSchema.safeParse(url).success).to.be.false;
      });
    });
  });
});
