import { expect } from 'chai';
import { type Scope } from '../../server/models/survey';
import { type BaseScopeChange } from '../../server/service/survey/model/DelegationScope';
import { getGroupCodesFromScopeGroups } from '../../server/util/survey';

describe('getGroupCodesFromScopeGroups', () => {
  const testCases: {
    desc: string;
    input: BaseScopeChange['scopeGroups'];
    scopeType: keyof Scope;
    expected: string[];
  }[] = [
    {
      desc: 'should return empty array when scopeGroups is empty',
      input: [],
      scopeType: 'standards',
      expected: [],
    },
    {
      desc: 'should return array of standards scope codes',
      input: [{ scopeType: 'standards', code: 'gri', scopeTags: ['gri-1', 'gri-2'] }],
      scopeType: 'standards',
      expected: ['gri'],
    },
    {
      desc: 'should return array of custom scope codes',
      input: [
        { scopeType: 'standards', code: 'gri', scopeTags: [] },
        { scopeType: 'custom', code: 'group1', scopeTags: ['subgroup-1'] },
        { scopeType: 'custom', code: 'group2', scopeTags: ['subgroup-2'] },
        { scopeType: 'custom', code: 'group3', scopeTags: ['subgroup-3'] },
      ],
      scopeType: 'custom',
      expected: ['group1', 'group2', 'group3'],
    },
  ];
  testCases.forEach(({ desc, input, scopeType, expected }) => {
    it(desc, () => {
      expect(getGroupCodesFromScopeGroups(input, scopeType)).to.deep.equal(expected);
    });
  });
});
