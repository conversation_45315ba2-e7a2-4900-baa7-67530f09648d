import { UtrValueType } from '../../server/models/public/universalTrackerType';
import type { UtrvData, XBRLMapping } from '../../server/service/reporting/types';
import { NumberScale } from '../../server/types/units';
import { createExtendedUtrv } from './compositeUTRVFixtures';
import { utrOneId, utrTwoId } from './universalTrackerFixtures';
import { utrValueOneId, utrValueTwoId } from './universalTrackerValueFixtures';

export const factOne = 'esrs:BasisForPreparationOfSustainabilityStatement'; // Section: "BP-1", dataPointId: "BP-1_01"
export const factTwo = 'esrs:PercentageOfTotalEmissionsOfPollutantsToSoilOccurringInAreasAtWaterRisk'; // Section: "E2-4", dataPointId: "E2-4_13"
export const factThree = 'esrs:GeneralBasisForPreparationOfSustainabilityStatementAbstract'; // No section, dataPointId
export const noRefFact = 'esrs:NoRefFact';

// Types from types.ts
export const xbrlMapping: XBRLMapping = {
  [factOne]: { factName: factOne, utrCode: 'utr1' },
  [factTwo]: { factName: factTwo, utrCode: 'utr2' },
  [factThree]: { factName: factThree, utrCode: 'utr3' },
};

export const utrCodeToUtrvMap: Map<string, UtrvData> = new Map([
  [
    'utr1',
    createExtendedUtrv({
      id: utrValueTwoId,
      utrId: utrOneId,
      value: 123,
      overridesUtr: { code: 'utr1', valueType: UtrValueType.Number, numberScale: NumberScale.Hundreds, unit: 'm3' },
    }),
  ],
  [
    'utr2',
    createExtendedUtrv({
      id: utrValueOneId,
      utrId: utrTwoId,
      overrides: { valueData: { data: 'abc' } },
      overridesUtr: { code: 'utr2', valueType: UtrValueType.Text, numberScale: undefined, unit: undefined },
    }),
  ],
]);
