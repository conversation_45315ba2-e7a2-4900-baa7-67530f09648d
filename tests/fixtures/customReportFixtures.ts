import { ObjectId } from 'bson';
import { UtrValueType } from '../../server/models/public/universalTrackerType';
import { ValueHistory } from '../../server/models/universalTrackerValue';
import { CustomReportData } from '../../server/service/custom-report/ReportDataResolver';
import { SupportedMeasureUnits } from '../../server/service/units/unitTypes';
import { ActionList } from '../../server/service/utr/constants';
import { createCombinedFromCode } from '../factories/universalTrackerValue';
import { utrTableOneWithAggregationRow, utrTableOneWithOverrides } from './universalTrackerFixtures';
import { createValueValidation, numericValueListTest, valueListTestTable, yesNoList } from './valueListFixtures';
import { DataScopeAccess } from '../../server/models/dataShare';
import { ReportDataGeneratorParams } from '../../server/service/custom-report/type';
import { InputRowData, RowData } from '../../server/models/public/universalTrackerValueType';
import { UniversalTrackerPlain } from '../../server/models/universalTracker';

const userId = new ObjectId();
const initiativeId = new ObjectId();
export const surveyId = new ObjectId();
export const numberScale = {
  default: 'millions',
  input: 'hundreds',
  override: 'thousands',
};
export const unit = {
  default: 'm3', // 1 Ml = 1 000 m3
  input: 'Ml',
  override: 'Mcm', // 1 Ml = 0.001 Mcm
};

const defaultDownloadScope = {
  access: DataScopeAccess.Full,
  scope: {},
  statuses: [ActionList.Created, ActionList.Updated, ActionList.Verified, ActionList.Rejected],
  displayMetricOverrides: false,
  displayUserInput: false,
};

export const downloadScopeWithUserInput = {
  ...defaultDownloadScope,
  displayUserInput: true,
};

export const downloadScopeWithOverrides = {
  ...defaultDownloadScope,
  displayMetricOverrides: true,
  displayUserInput: true,
};

export const defaultDownloadParams: ReportDataGeneratorParams = {
  surveys: [],
  downloadScope: defaultDownloadScope,
  initiativeId,
  columns: [],
};

const createUtrvHistoryItem = (overrides: Partial<ValueHistory> = {}) => {
  return {
    _id: new ObjectId(),
    evidence: [],
    userId,
    date: new Date(),
    value: undefined,
    action: ActionList.Created,
    ...overrides,
  };
};

const createUtrvHistory = (oldestUpdatedData: Partial<ValueHistory>, latestUpdatedData: Partial<ValueHistory>) => {
  return [
    createUtrvHistoryItem({
      action: ActionList.Created,
      userId,
      date: new Date('2024-12-25T00:00:00.000Z'),
    }),
    createUtrvHistoryItem({
      action: ActionList.Updated,
      date: new Date('2024-12-25T01:00:00.000Z'),
      ...oldestUpdatedData,
    }),
    createUtrvHistoryItem({
      action: ActionList.Updated,
      date: new Date('2024-12-25T02:00:00.000Z'),
      ...latestUpdatedData,
    }),
    createUtrvHistoryItem({
      action: ActionList.Verified,
      date: new Date('2024-12-25T02:00:00.000Z'),
    }),
  ];
};

export const createNumberUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'number-one';
  const utrData = {
    valueType: UtrValueType.Number,
    valueLabel: 'Number value label',
    numberScaleInput: numberScale.override,
  };
  const oldestUpdatedData = {
    value: 123,
    valueData: { input: { value: 123, unit: undefined, numberScale: undefined } },
  };
  const latestUpdatedData = {
    value: 456,
    valueData: { input: { value: 456, unit: undefined, numberScale: undefined } },
  };
  const utrvData = {
    ...latestUpdatedData,
    status: ActionList.Verified,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createUnitNumberUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'number-with-unit-one';
  const utrData = {
    valueType: UtrValueType.Number,
    valueLabel: 'Number with unit value label',
    unit: unit.default,
    unitType: SupportedMeasureUnits.volume,
    unitInput: unit.override,
  };
  const oldestUpdatedData = {
    value: 123000,
    unit: unit.default,
    valueData: { input: { value: 123, unit: unit.input, numberScale: undefined } },
  };
  const latestUpdatedData = {
    value: 456000,
    unit: unit.default,
    valueData: { input: { value: 456, unit: unit.input, numberScale: undefined } },
  };
  const utrvData = {
    ...latestUpdatedData,
    status: ActionList.Verified,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createCurrencyNumberUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'number-with-currency-one';
  const utrData = {
    valueType: UtrValueType.Number,
    valueLabel: 'Number with currency/numberScale value label',
    unit: 'USD',
    unitType: SupportedMeasureUnits.currency,
    numberScale: numberScale.default,
    numberScaleInput: numberScale.override,
  };
  const oldestUpdatedData = {
    value: 123,
    unit: 'USD',
    numberScale: numberScale.default,
    valueData: { input: { value: 1230, unit: 'USD', numberScale: numberScale.input } },
  };
  const latestUpdatedData = {
    value: 0.0456,
    unit: 'USD',
    numberScale: numberScale.default,
    valueData: { input: { value: 456, unit: 'USD', numberScale: numberScale.input } },
  };
  const utrvData = {
    ...latestUpdatedData,
    status: ActionList.Verified,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createPercentageUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'percentage-one';
  const utrData = {
    valueType: UtrValueType.Percentage,
    valueLabel: 'Percentage value label',
    numberScaleInput: numberScale.override,
  };
  const [oldestUpdatedData, latestUpdatedData] = [10, 20].map((value) => ({ value, valueData: { input: { value, unit: undefined, numberScale: undefined } } }));
  const utrvData = {
    ...latestUpdatedData,
    status: ActionList.Verified,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createNumericValueListUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'numeric-value-list-one';
  const utrData = {
    valueType: UtrValueType.NumericValueList,
    valueLabel: 'Numeric value list value label',
    valueValidation: createValueValidation(numericValueListTest),
    unitType: SupportedMeasureUnits.volume,
    unit: unit.default,
    unitInput: unit.override,
  };

  const oldestUpdatedData = {
    value: 3000,
    unit: unit.default,
    valueData: {
      data: { surface_water: '1000', ground_water: '2000' },
      input: { data: { surface_water: '1', ground_water: '2' }, value: 3, unit: unit.input, numberScale: undefined },
    },
  };
  const latestUpdatedData = {
    value: 9000,
    unit: unit.default,
    valueData: {
      data: { surface_water: '4000', ground_water: '5000' },
      input: { data: { surface_water: '4', ground_water: '5' }, value: 9, unit: unit.input, numberScale: undefined },
    },
  };

  const utrvData = {
    ...latestUpdatedData,
    status: ActionList.Verified,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

const valueDataInputTable = [
  [
    { code: 'col-num', value: '1', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '1', unit: 'USD', numberScale: numberScale.input },
    { code: 'col-unit', value: '1', unit: unit.input, numberScale: undefined },
    { code: 'col-percentage', value: '1', unit: undefined, numberScale: undefined },
  ],
  [
    { code: 'col-num', value: '2', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '2', unit: 'USD', numberScale: numberScale.input },
    { code: 'col-unit', value: '2', unit: unit.input, numberScale: undefined },
    { code: 'col-percentage', value: '2', unit: undefined, numberScale: undefined },
  ],
];

const valueDataTable = [
  [
    { code: 'col-num', value: '1' },
    { code: 'col-currency', value: '0.0001', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '1000', unit: unit.default },
    { code: 'col-percentage', value: '1' },
  ],
  [
    { code: 'col-num', value: '2' },
    { code: 'col-currency', value: '0.0002', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '2000', unit: unit.default },
    { code: 'col-percentage', value: '2' },
  ],
];

// use for testing table's row matching unit/number scale
export const valueDataTableWithSameUnitType = [
  [
    { code: 'col-1', value: '1', unit: unit.default },
    { code: 'col-2', value: '11', unit: unit.default },
    { code: 'col-3', value: '12', unit: unit.default },
  ],
  [
    { code: 'col-1', value: '2', unit: unit.default },
    { code: 'col-2', value: '21', unit: unit.default },
    { code: 'col-3', value: '22', unit: unit.default },
  ],
  [
    { code: 'col-1', value: '3', unit: unit.default },
    { code: 'col-2', value: undefined, unit: unit.default },
    { code: 'col-3', value: undefined, unit: unit.default },
  ],
  [
    { code: 'col-1', value: '4', unit: unit.default },
    { code: 'col-2', value: '41', unit: unit.default },
    { code: 'col-3', value: '42', unit: unit.default },
  ],
  [
    { code: 'col-1', value: '5', unit: unit.default, numberScale: 'single' },
    { code: 'col-2', value: '51', unit: unit.default, numberScale: 'single' },
    { code: 'col-3', value: '52', unit: unit.default, numberScale: 'single' },
  ],
];

// use for testing table's row matching unit/number scale
export const valueDataInputTableWithSameUnitType = [
  [
    { code: 'col-1', value: '1000', unit: unit.override, numberScale: undefined },
    { code: 'col-2', value: '1001', unit: unit.input, numberScale: undefined },
    { code: 'col-3', value: '1002', unit: unit.default, numberScale: undefined },
  ],
  [
    { code: 'col-1', value: '2000', unit: unit.input, numberScale: undefined },
    { code: 'col-2', value: '2001', unit: unit.input, numberScale: undefined },
    { code: 'col-3', value: '2002', unit: unit.input, numberScale: undefined },
  ],
  [
    { code: 'col-1', value: '3000', unit: unit.input, numberScale: undefined },
    { code: 'col-2', value: undefined, unit: unit.default, numberScale: undefined },
    { code: 'col-3', value: undefined, unit: unit.override, numberScale: undefined },
  ],
  [
    { code: 'col-1', value: '4000', unit: unit.input, numberScale: undefined },
    { code: 'col-2', value: '4001', unit: unit.input, numberScale: undefined },
    { code: 'col-3', value: '4002', unit: unit.override, numberScale: undefined },
  ],
  [
    { code: 'col-1', value: '5000', unit: unit.input, numberScale: 'hundreds' },
    { code: 'col-2', value: '5001', unit: unit.input, numberScale: 'thousands' },
    { code: 'col-3', value: '5002', unit: unit.input, numberScale: 'single' },
  ],
];

const valueDataTableWithAggregatedRows = [
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code },
    { code: 'col-text', value: 'Male' },
    { code: 'col-currency', value: '1', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '1000', unit: unit.default },
    { code: 'col-percentage', value: '1' },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[1].code },
    { code: 'col-text', value: 'Female' },
    { code: 'col-currency', value: '2', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '2000', unit: unit.default },
    { code: 'col-percentage', value: '2' },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[1].code },
    { code: 'col-text', value: 'Female' },
    { code: 'col-currency', value: '3', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '3000', unit: unit.default },
    { code: 'col-percentage', value: '3' },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code },
    { code: 'col-text', value: 'Female' },
    { code: 'col-currency', value: '4', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '4000', unit: unit.default },
    { code: 'col-percentage', value: '4' },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code },
    { code: 'col-text', value: 'Male' },
    { code: 'col-currency', value: '5', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '5000', unit: unit.default },
    { code: 'col-percentage', value: '5' },
  ],
];

const inputValueDataTableWithAggregatedRows = [
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code, unit: undefined, numberScale: undefined },
    { code: 'col-text', value: 'Male', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '1', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '1000', unit: unit.default, numberScale: undefined },
    { code: 'col-percentage', value: '1', unit: undefined, numberScale: undefined },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[1].code, unit: undefined, numberScale: undefined },
    { code: 'col-text', value: 'Female', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '2', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '2000', unit: unit.default, numberScale: undefined },
    { code: 'col-percentage', value: '2', unit: undefined, numberScale: undefined },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[1].code, unit: undefined, numberScale: undefined },
    { code: 'col-text', value: 'Female', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '3', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '3000', unit: unit.default, numberScale: undefined },
    { code: 'col-percentage', value: '3', unit: undefined, numberScale: undefined },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code, unit: undefined, numberScale: undefined },
    { code: 'col-text', value: 'Female', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '4', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '4000', unit: unit.default, numberScale: undefined },
    { code: 'col-percentage', value: '4', unit: undefined, numberScale: undefined },
  ],
  [
    { code: 'col-value-list', value: valueListTestTable.options[0].code, unit: undefined, numberScale: undefined },
    { code: 'col-text', value: 'Male', unit: undefined, numberScale: undefined },
    { code: 'col-currency', value: '5', unit: 'USD', numberScale: numberScale.default },
    { code: 'col-unit', value: '5000', unit: unit.default, numberScale: undefined },
    { code: 'col-percentage', value: '5', unit: undefined, numberScale: undefined },
  ],
];

export const createSingleRowTableUtrv = (
  props: {
    overrides?: Partial<CustomReportData>;
    utr?: UniversalTrackerPlain,
    data?: RowData[][];
    inputData?: InputRowData[][];
  } = {}
) => {
  const {
    overrides = {},
    utr = utrTableOneWithOverrides,
    data = valueDataTable,
    inputData = valueDataInputTable,
  } = props;
  const utrCode = 'single-row-table-one';
  const columns = utr.valueValidation?.table?.columns ?? [];
  const utrData = {
    ...utr,
    valueValidation: {
      table: { columns, validation: { maxRows: 1 } },
    },
  };

  const oldestUpdatedData = { valueData: { table: [data[0]], input: { table: [inputData[0]], unit: undefined, numberScale: undefined } } };

  const latestUpdatedData = { valueData: { table: [data[1]], input: { table: [inputData[1]], unit: undefined, numberScale: undefined } } };

  const utrvData = {
    ...latestUpdatedData,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    status: ActionList.Verified,
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createMultiRowTableUtrv = (
  props: {
    overrides?: Partial<CustomReportData>;
    utr?: UniversalTrackerPlain,
    data?: RowData[][];
    inputData?: InputRowData[][];
  } = {}
) => {
  const {
    overrides = {},
    utr = utrTableOneWithOverrides,
    data = valueDataTable,
    inputData = valueDataInputTable,
  } = props;
  const utrCode = 'multi-row-table-one';

  const oldestUpdatedData = { valueData: { table: [data[0]], input: { table: [inputData[0]], unit: undefined, numberScale: undefined } } };

  const latestUpdatedData = { valueData: { table: data, input: { table: inputData, unit: undefined, numberScale: undefined } } };

  const utrvData = {
    ...latestUpdatedData,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    status: ActionList.Verified,
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utr);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createMultiRowTableUtrvWithAggregatedRow = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'multi-row-table-one';
  const utrData = { ...utrTableOneWithAggregationRow };

  const latestUpdatedData = {
    valueData: {
      table: valueDataTableWithAggregatedRows,
      input: {
        table: inputValueDataTableWithAggregatedRows,
        unit: undefined,
        numberScale: undefined,
      },
    },
  };

  const utrvData = {
    ...latestUpdatedData,
    lastUpdated: new Date('2024-12-25T02:02:00.000Z'),
    status: ActionList.Verified,
    history: [
      createUtrvHistoryItem({
        action: ActionList.Updated,
        date: new Date('2024-12-25T01:00:00.000Z'),
        ...latestUpdatedData,
      })
    ],
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);
  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createTextUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'text-one';
  const utrData = { valueType: UtrValueType.Text };
  const oldestUpdatedData = {
    valueData: { data: 'text oldest' },
  };
  const latestUpdatedData = {
    valueData: { data: 'text latest' },
  };
  const utrvData = {
    ...latestUpdatedData,
    lastUpdated: new Date('2024-12-25T02:00:00.000Z'),
    status: ActionList.Verified,
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);

  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};

export const createValueListUtrv = (overrides: Partial<CustomReportData> = {}) => {
  const utrCode = 'value-list-one';
  const utrData = { valueType: UtrValueType.ValueList, valueValidation: createValueValidation(yesNoList) };
  const oldestUpdatedData = {
    valueData: { data: 'no' },
  };
  const latestUpdatedData = {
    valueData: { data: 'yes' },
  };
  const utrvData = {
    ...latestUpdatedData,
    lastUpdated: new Date('2024-12-25T02:00:00.000Z'),
    status: ActionList.Verified,
    history: createUtrvHistory(oldestUpdatedData, latestUpdatedData),
  };

  const { universalTracker, ...utrv } = createCombinedFromCode(utrCode, utrvData, utrData);

  return { ...universalTracker, utrvs: [{ ...utrv, surveyId }], ...overrides };
};
