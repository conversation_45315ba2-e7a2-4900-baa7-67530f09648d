import { ObjectId } from 'bson';
import { type EntryEmissions, type GreenlyCompanyMinimal, type GreenlyCompany, GreenlyRegulatoryMethodology } from '../../server/service/integration/greenly/greenlyTypes';
import IntegrationConnection from '../../server/models/integrationConnection';

export const getGreenlyConnection = (initiativeId = new ObjectId()) => {
  const connection = new IntegrationConnection({
    initiativeId,
    integrationCode: 'greenly',
    status: 'pending',
    data: {
      answers: [
        {
          utrCode: 'greenly-registration-details',
          valueData: {
            table: [
              [
                { code: 'greenly-employees', value: 100 },
                { code: 'greenly-data-frequency', value: 'Quarterly' },
                { code: 'greenly-revenue', value: 1000000, unit: 'USD' }
              ]
            ]
          }
        }
      ],
      address: {
        line1: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        postcode: '12345'
      }
    },
    createdBy: new ObjectId()
  });

  // Mock mongoose methods
  connection.save = async () => connection;
  connection.markModified = () => {};

  return connection;
};

export const createEmissionData = (): EntryEmissions[] => {
  return [
    {
      category: 'scope1',
      emissionsInKgCO2: 100,
      description: {
        en: 'Scope 1 direct emissions'
      },
      kgCO2: 100,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 1,
      scope: 1
    },
    {
      category: 'scope2',
      emissionsInKgCO2: 200,
      description: {
        en: 'Scope 2 electricity emissions'
      },
      kgCO2: 200,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 2,
      scope: 2
    },
    {
      category: 'scope3',
      emissionsInKgCO2: 300,
      description: {
        en: 'Scope 3 purchased goods emissions'
      },
      kgCO2: 300,
      kgCH4fossilAsCO2e: 0,
      kgCH4biogenicAsCO2e: 0,
      kgN2OAsCO2e: 0,
      kgOtherAsCO2e: 0,
      kgCO2Biogenic: 0,
      frenchOfficialCategory: 3,
      scope: 3
    }
  ];
};

export function createGreenlyFixtures() {
  const greenlyCompanyListItem: GreenlyCompanyMinimal = {
    id: 'company-123',
    companyName: 'Test Company',
    identifier: {
      type: 'operatorId',
      value: new ObjectId().toString(),
    }
  };

  return {
    greenlyCompanyListItem,
    getGreenlyConnection,
    createEmissionData
  };
}

export function getMockGreenlyCompany(overrides: Partial<GreenlyCompany> = {}): GreenlyCompany {
  const now = new Date().toISOString();
  return {
    id: 'fake-company-id',
    createdAt: now,
    lastUpdatedAt: now,
    industry: 'Test Industry',
    companyName: 'Test Company',
    companyLanguage: 'en',
    countryId: 'GB',
    type: 'standalone',
    companyLogoUrl: 'logo.png',
    settings: { notifications: { enableTaskNotifications: true } },
    identifier: { type: 'operatorId', value: 'test-initiative' },
    organisationBoundaries: 'Test Description',
    stringifiedFirstFiscalMonth: 'JANUARY',
    fiscalYearIsYearOfFirstMonth: true,
    defaultRegulatoryMethodology: GreenlyRegulatoryMethodology.GHGProtocol,
    parentIndustry: 'Parent Industry',
    hasAcceptedToShareCarbonData: true,
    hasAccessToSupplierQuestionnaire: true,
    companyAccountOwnerId: undefined,
    invitedByCompanyId: undefined,
    invitedByUserId: undefined,
    matchedSupplierId: '',
    isParentCompany: false,
    isChildCompany: false,
    groupCompanyId: '',
    dashboardIntegrationId: undefined,
    validityStatus: 'DEMO',
    managedByExternalConsultant: false,
    mfaEnabled: false,
    likelyLanguage: 'en',
    firstFiscalMonth: 'JANUARY',
    profiles: [],
    ownedCompanyHasCompanyList: [],
    groupCompany: {
      id: 'group-1',
      createdAt: now,
      lastUpdatedAt: now,
      industry: 'Test Industry',
      companyName: 'Group Company',
      companyLanguage: 'en',
      countryId: 'GB',
      type: 'standalone',
      companyLogoUrl: 'logo.png',
      settings: { notifications: { enableTaskNotifications: true } },
      identifier: {},
      organisationBoundaries: 'Test',
      stringifiedFirstFiscalMonth: 'JANUARY',
      fiscalYearIsYearOfFirstMonth: true,
      defaultRegulatoryMethodology: GreenlyRegulatoryMethodology.GHGProtocol,
      parentIndustry: 'Parent Industry',
      hasAcceptedToShareCarbonData: true,
      hasAccessToSupplierQuestionnaire: true,
      companyAccountOwnerId: undefined,
      invitedByCompanyId: undefined,
      invitedByUserId: undefined,
      matchedSupplierId: '',
      isParentCompany: false,
      isChildCompany: false,
      groupCompanyId: '',
      dashboardIntegrationId: undefined,
      validityStatus: 'DEMO',
      managedByExternalConsultant: false,
      mfaEnabled: false,
      name: 'Group Company',
      likelyLanguage: 'en',
      firstFiscalMonth: 'JANUARY',
    },
    ...overrides,
  };
}
