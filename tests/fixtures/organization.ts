import { OrganizationPartnerTypes, OrganizationPlain } from '../../server/models/organization';
import { ObjectId } from 'bson';

export const organizationOneId = new ObjectId();
export const organizationOne = {
  _id: organizationOneId,
  code: 'org1',
  name: 'Organization One',
  organizationType: 'public',
  partnerTypes: [OrganizationPartnerTypes.Assurer],
} satisfies OrganizationPlain;

export const organizationTwoId = new ObjectId();
export const organizationTwo = {
  _id: organizationTwoId,
  code: 'org2',
  name: 'Organization Two',
  organizationType: 'public',
  partnerTypes: [OrganizationPartnerTypes.Assurer],
} satisfies OrganizationPlain;