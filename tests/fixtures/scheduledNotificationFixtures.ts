import { ObjectId } from 'bson';
import { ScheduledType } from '../../server/models/scheduledNotification';
import type { UserDailyNotificationPlain } from '../../server/models/userDailyNotification';

/**
 * Factory function for creating UserDailyNotification test data
 * Uses the plain interface to ensure type safety
 */
export function createUserDailyNotificationData(
  overrides: Partial<UserDailyNotificationPlain> = {}
): UserDailyNotificationPlain {
  const defaults: UserDailyNotificationPlain & { _id?: ObjectId } = {
    _id: new ObjectId(),
    userId: new ObjectId(),
    type: ScheduledType.SurveyDeadline,
    scheduledDate: new Date(),
    data: {
      scheduledNotificationIds: [],
      templateData: {
        surveyIds: [],
        total: 0,
        stakeholderCount: 0,
        verifierCount: 0
      }
    },
    created: new Date(),
    completedDate: undefined,
    erroredDate: undefined,
    skippedDate: undefined
  };

  return {
    ...defaults,
    ...overrides,
    data: {
      ...defaults.data,
      ...(overrides.data || {}),
      templateData: {
        ...defaults.data.templateData,
        ...(overrides.data?.templateData || {})
      }
    }
  };
}

/**
 * Creates a survey deadline notification with common defaults
 */
export function createSurveyDeadlineNotificationData({
  userId,
  surveyIds,
  total,
  stakeholderCount = 0,
  verifierCount = 0,
  ...overrides
}: {
  userId: ObjectId;
  surveyIds: ObjectId[];
  total: number;
  stakeholderCount?: number;
  verifierCount?: number;
} & Partial<Omit<UserDailyNotificationPlain, 'userId' | 'data'>>): UserDailyNotificationPlain {
  return createUserDailyNotificationData({
    userId,
    data: {
      scheduledNotificationIds: [],
      templateData: {
        surveyIds,
        total,
        stakeholderCount,
        verifierCount
      }
    },
    ...overrides
  });
}
