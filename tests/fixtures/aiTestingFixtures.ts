/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import type { ExecuteTestRequest, ExecuteTestRequestInternal, ExecuteTestResponse, PromptConfiguration, TestResult } from '../../server/service/ai/ai-testing-types';
import type { AITestExecutionPlain } from '../../server/models/aiTestExecution';
import { TestExecutionType, TestExecutionStatus } from '../../server/models/aiTestExecution';
import type { AIPromptTemplatePlain } from '../../server/models/aiPromptTemplate';
import { PromptTemplateCategory } from '../../server/models/aiPromptTemplate';
import type { AiUploadedFilePlain } from '../../server/models/aiUploadedFile';

/**
 * AI Testing Fixtures
 *
 * All fixture functions use the `satisfies` operator to ensure type safety.
 * This pattern ensures:
 * 1. No extra properties can be added accidentally
 * 2. All required properties are present
 * 3. Property types match exactly
 * 4. TypeScript provides better intellisense
 */

/**
 * Create a mock ExecuteTestRequest
 */
export const createExecuteTestRequest = (overrides: Partial<ExecuteTestRequest> = {}): ExecuteTestRequest => {
  const baseRequest = {
    testType: 'utr-matching',
    uploadedFileIds: [new ObjectId().toString(), new ObjectId().toString()],
    utrSelection: {
      filters: {
        codes: ['UTR001', 'UTR002']
      }
    },
    prompt: {
      customPrompt: 'Test prompt for UTR matching'
    },
    options: {
      aiModel: 'claude-sonnet',
      includeExplanation: true,
      includeMatchedContent: false,
      relevanceThreshold: 0.7
    }
  } satisfies ExecuteTestRequest;

  return { ...baseRequest, ...overrides };
};


/**
 * Create a mock ExecuteTestResponse
 */
export const createExecuteTestResponse = (overrides: Partial<ExecuteTestResponse> = {}): ExecuteTestResponse => {
  const baseResponse = {
    executionId: 'exec-' + new ObjectId().toString(),
    timestamp: new Date(),
    duration: 1500,
    results: [
      {
        utrCode: 'UTR001',
        relevanceScore: 0.85,
        explanation: 'Highly relevant to sustainability metrics',
        matchedContent: ['The company reduced emissions by 15%...']
      }
    ],
    metrics: {
      tokenUsage: {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.015
      },
      cost: 0.015,
      executionTime: 1500
    }
  } satisfies ExecuteTestResponse;

  return { ...baseResponse, ...overrides };
};

/**
 * Create a mock PromptConfiguration
 */
export const createPromptConfiguration = (overrides: Partial<PromptConfiguration> = {}): PromptConfiguration => {
  const baseConfig = {
    customPrompt: 'Analyze this document for sustainability metrics'
  } satisfies PromptConfiguration;

  return { ...baseConfig, ...overrides };
};

/**
 * Create a mock TestResult
 */
export const createTestResult = (overrides: Partial<TestResult> = {}): TestResult => {
  const baseResult = {
    utrCode: 'UTR001',
    relevanceScore: 0.9,
    explanation: 'Content directly addresses this metric',
    matchedContent: ['Relevant content snippet...']
  } satisfies TestResult;

  return { ...baseResult, ...overrides };
};

/**
 * Create a mock AITestExecution document
 */
export const createAITestExecution = (overrides: Partial<AITestExecutionPlain> = {}): AITestExecutionPlain => {
  const now = new Date();
  const baseExecution = {
    _id: new ObjectId(),
    sessionId: `test-session-${Date.now()}`,
    testType: TestExecutionType.UTRMatching,
    status: TestExecutionStatus.Completed,
    prompt: 'Test prompt for UTR matching',
    aiModel: 'claude-sonnet', // Add the AI model field
    uploadedFileIds: [new ObjectId().toString(), new ObjectId().toString()],
    parameters: {
      options: {
        aiModel: 'claude-sonnet',
        relevanceThreshold: 0.7,
        includeExplanation: true,
        includeMatchedContent: true
      },
      utrSelection: {
        filters: {
          codes: ['UTR001', 'UTR002']
        }
      }
    },
    results: [createTestResult()],
    metrics: {
      executionTime: 1500,
      tokenUsage: {
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500,
        cost: 0.015
      },
      cost: 0.015
    },
    createdBy: new ObjectId(),
    created: now,
    updated: now,
    completedAt: new Date(now.getTime() + 1500)
  } satisfies AITestExecutionPlain;

  return { ...baseExecution, ...overrides };
};

/**
 * Create a mock AIPromptTemplate document
 */
export const createAIPromptTemplate = (overrides: Partial<AIPromptTemplatePlain> = {}): AIPromptTemplatePlain => {
  const now = new Date();
  const baseTemplate = {
    _id: new ObjectId(),
    name: 'ESG Document Analysis',
    content: 'Analyze the following document for ESG metrics: {{documentName}}. Focus on: {{focusAreas}}',
    category: PromptTemplateCategory.General,
    variables: ['documentName', 'focusAreas'],
    tags: ['esg', 'analysis', 'document'],
    isActive: true,
    isPublic: false,
    usageCount: 0,
    version: 1,
    createdBy: new ObjectId(),
    created: now,
    updated: now
  } satisfies AIPromptTemplatePlain;

  return { ...baseTemplate, ...overrides };
};

/**
 * Create a mock AIUploadedFile document
 */
export const createAIUploadedFile = (overrides: Partial<AiUploadedFilePlain> = {}): AiUploadedFilePlain => {
  const now = new Date();
  const timestamp = Date.now();
  const originalName = 'sustainability-report-2024.pdf';

  const baseFile = {
    _id: new ObjectId(),
    originalName,
    prefixedName: `${timestamp}_${originalName}`,
    metadata: {
      size: 1024 * 1024 * 2, // 2MB
      mimetype: 'application/pdf',
      extension: 'pdf',
      uploadedAt: now
    },
    tags: ['sustainability', '2024', 'report'],
    description: 'Annual sustainability report for 2024',
    isActive: true,
    providerFiles: {
      openai: {
        fileId: 'file-openai-' + new ObjectId().toString(),
        uploadedAt: now,
      },
      claude: {
        fileId: 'file-claude-' + new ObjectId().toString(),
        uploadedAt: now,
      },
      gemini: {
        fileId: 'file-gemini-' + new ObjectId().toString(),
        uploadedAt: now,
      }
    },
    fileStoragePath: `ai-files/${now.getFullYear()}/${now.getMonth() + 1}/${timestamp}_${originalName}`,
    uploadedBy: new ObjectId(),
    created: now,
    updated: now
  } satisfies AiUploadedFilePlain;

  return { ...baseFile, ...overrides };
};

/**
 * Create a populated AITestExecution with related documents
 */
export const createPopulatedAITestExecution = (overrides: Partial<AITestExecutionPlain> = {}): AITestExecutionPlain => {
  const userId = new ObjectId();
  const promptTemplateId = new ObjectId();

  const files = [
    createAIUploadedFile({
      _id: new ObjectId(),
      originalName: 'report1.pdf',
      metadata: {
        size: 1024 * 512,
        mimetype: 'application/pdf',
        extension: 'pdf',
        uploadedAt: new Date()
      }
    }),
    createAIUploadedFile({
      _id: new ObjectId(),
      originalName: 'report2.pdf',
      metadata: {
        size: 1024 * 768,
        mimetype: 'application/pdf',
        extension: 'pdf',
        uploadedAt: new Date()
      }
    })
  ];

  const baseExecution = createAITestExecution({
    createdBy: userId,
    promptTemplateId,
    uploadedFileIds: files.map(f => f._id.toString())
  });

  // Note: This returns a plain object, not a populated document
  // If you need populated references, use them separately
  return {
    ...baseExecution,
    ...overrides
  };
};

/**
 * Create mock AI testing request
 */
export const createAITestingRequest = (overrides: Partial<ExecuteTestRequest> = {}): ExecuteTestRequest => {
  const baseRequest = {
    prompt: {
      customPrompt: 'Test prompt',
      ...overrides.prompt
    },
    options: {
      aiModel: 'gpt-5',
      ...overrides.options
    }
  } satisfies ExecuteTestRequest;

  return { ...baseRequest, ...overrides };
};

/**
 * Create mock AI testing request internal (with required executionId)
 */
export const createAITestingRequestInternal = (overrides: Partial<ExecuteTestRequestInternal> = {}): ExecuteTestRequestInternal => {
  const base = createAITestingRequest(overrides);
  return {
    ...base,
    executionId: overrides.executionId || new ObjectId().toString()
  };
};

/**
 * Create a list of mock prompt templates
 */
export const createPromptTemplateList = (count: number = 3): AIPromptTemplatePlain[] => {
  return Array.from({ length: count }, (_, i) =>
    createAIPromptTemplate({
      name: `Template ${i + 1}`,
      category: [PromptTemplateCategory.General, PromptTemplateCategory.UTRMatching, PromptTemplateCategory.DataExtraction][i % 3],
      usageCount: Math.floor(Math.random() * 100)
    })
  );
};

interface FileType {
  mimetype: string;
  extension: string;
  name: string;
}

/**
 * Create a list of mock uploaded files
 */
export const createUploadedFileList = (count: number = 3): AiUploadedFilePlain[] => {
  const fileTypes: FileType[] = [
    { mimetype: 'application/pdf', extension: 'pdf', name: 'report' },
    { mimetype: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', extension: 'docx', name: 'document' },
    { mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', extension: 'xlsx', name: 'spreadsheet' }
  ];

  return Array.from({ length: count }, (_, i) => {
    const fileType = fileTypes[i % fileTypes.length];
    return createAIUploadedFile({
      originalName: `${fileType.name}-${i + 1}.${fileType.extension}`,
      metadata: {
        mimetype: fileType.mimetype,
        extension: fileType.extension,
        size: Math.floor(Math.random() * 5 * 1024 * 1024), // Random size up to 5MB
        uploadedAt: new Date()
      }
    });
  });
};
