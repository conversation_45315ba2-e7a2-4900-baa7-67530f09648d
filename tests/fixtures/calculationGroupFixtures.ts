import { UtrCodeUtrMap } from '../../server/service/utr/connection/types';
import { CalculationGroupPlain, CalculationGroupValueType, CalculationType } from '../../server/models/calculationGroup';
import { ObjectId } from 'bson';
import { ColumnType, UtrValueType } from '../../server/models/public/universalTrackerType';

export const calGroupCode1 = 'cal-group-code-1';
export const mockCalculationGroup1: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCode1,
  name: calGroupCode1,
  valueType: CalculationGroupValueType.Numeric,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      type: CalculationType.Direct,
      name: 'cal1',
      direct: 'a',
      variables: {
        a: { code: 'utr1' },
      },
    },
  ],
};

export const calGroupCode2 = 'cal-group-code-2';
export const mockCalculationGroup2: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCode2,
  name: calGroupCode2,
  valueType: CalculationGroupValueType.Text,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      name: 'cal-2',
      type: CalculationType.Direct,
      direct: 'a',
      variables: {
        a: { code: 'utr2' },
      },
    },
  ],
};

export const calGroupCode3 = 'cal-group-code-3';
export const mockCalculationGroup3: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCode3,
  name: calGroupCode3,
  valueType: CalculationGroupValueType.Numeric,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      name: '',
      type: CalculationType.Direct,
      direct: 'a',
      variables: {
        a: { code: 'utr3', valueListCode: 'col1' },
      },
    },
  ],
};

export const calGroupCodeInvalid = 'cal-group-code-invalid';
export const mockCalculationGroupInvalid: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCodeInvalid,
  name: calGroupCodeInvalid,
  valueType: CalculationGroupValueType.Numeric,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      name: 'cal-4',
      type: CalculationType.Direct,
      direct: 'a',
      variables: {
        a: { code: 'invalid-utr', valueListCode: 'col1' },
      },
    },
  ],
};

export const mockIntegratedCalculationGroup: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCode1,
  name: calGroupCode1,
  valueType: CalculationGroupValueType.Numeric,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      type: CalculationType.Direct,
      name: 'cal1',
      direct: 'a',
      variables: {
        a: { code: 'utr1', valueListCode: 'col1', integrationCode: 'integration-code-1' },
      },
    },
  ],
};

export const mockFormulaCalculationGroup: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: calGroupCode1,
  name: calGroupCode1,
  valueType: CalculationGroupValueType.Numeric,
  oktaId: '',
  created: new Date(),
  calculations: [
    {
      _id: new ObjectId(),
      type: CalculationType.Formula,
      name: 'cal1',
      formula: '{a} + {b}',
      variables: {
        a: { code: 'utr1', valueListCode: 'col1' },
        b: { code: 'utr2', valueListCode: 'col2' },
      },
    },
  ],
};

// Mock data for UtrCodeUtrMap
export const mockUtrCodeToUtrMap: UtrCodeUtrMap = new Map([
  ['utr1', { valueType: UtrValueType.Number, unit: 'kg', numberScale: '1' }],
  ['utr2', { valueType: UtrValueType.Text }],
  [
    'utr3',
    {
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          columns: [
            { code: 'col1', name: 'col1', type: ColumnType.Number },
            { code: 'col2', name: 'col2', type: ColumnType.Text },
            { code: 'col3', name: 'col3', type: ColumnType.ValueList },
          ],
        },
      },
    },
  ],
]);
