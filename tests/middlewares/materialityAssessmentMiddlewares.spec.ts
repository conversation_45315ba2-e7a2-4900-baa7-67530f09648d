import { Request, Response } from 'express';
import { createSandbox } from 'sinon';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import { userOne } from '../fixtures/userFixtures';
import { SurveyRepository } from '../../server/repository/SurveyRepository';
import ContextError from '../../server/error/ContextError';
import { canGenerateScore } from '../../server/middleware/materialityAssessmentMiddlewares';
import User from '../../server/models/user';
import Survey from '../../server/models/survey';
import UserError from '../../server/error/UserError';

describe('materialityAssessmentMiddlewares', () => {
  describe('canGenerateScore', () => {
    const sandbox = createSandbox();
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let nextFunction: sinon.SinonStub;

    const user = new User(userOne);
    const assessmentId = new ObjectId();

    beforeEach(() => {
      nextFunction = sandbox.stub();
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should call next with error when mustFindById throws an error', async () => {
      mockRequest = { params: { assessmentId: assessmentId.toString() }, user };
      mockResponse = { locals: {} };
      sandbox.stub(SurveyRepository, 'mustFindById').throws(new ContextError('Mongo Error'));
      await canGenerateScore(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0] instanceof ContextError).to.be.true;
      expect(nextFunction.args[0][0].message).to.match(/Mongo Error/);
    });

    it('should call next with error when assessment is not completed', async () => {
      mockRequest = { params: { assessmentId: assessmentId.toString() }, user };
      mockResponse = { locals: {} };
      sandbox
        .stub(SurveyRepository, 'mustFindById')
        .resolves(new Survey({ _id: assessmentId, completedDate: undefined }));
      await canGenerateScore(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0] instanceof UserError).to.be.true;
      expect(nextFunction.args[0][0].message).to.match(/This assessment has not been completed yet/);
    });

    it('should call next without error when assessment is completed', async () => {
      mockRequest = { params: { assessmentId: assessmentId.toString() }, user };
      mockResponse = { locals: {} };
      sandbox
        .stub(SurveyRepository, 'mustFindById')
        .resolves(new Survey({ _id: assessmentId, completedDate: new Date() }));
      await canGenerateScore(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0]).to.eq(undefined);
    });
  });
});
