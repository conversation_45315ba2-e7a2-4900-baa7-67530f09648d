import { Request, Response } from 'express';
import sinon, { createSandbox } from 'sinon';
import { userOne } from '../fixtures/userFixtures';
import { initiativeOneSimple, initiativeTwo } from '../fixtures/initiativeFixtures';
import { canAccessInsights, canAccessInsightsWithStaff, isMaterialitySurvey, isScoresCalculated } from '../../server/middleware/surveyMiddlewares';
import { expect } from 'chai';
import User from '../../server/models/user';
import { UserRoles } from '../../server/service/user/userPermissions';
import { SurveyRepository } from '../../server/repository/SurveyRepository';
import { SurveyPermissions } from '../../server/service/survey/SurveyPermissions';
import Survey, { SurveyType } from '../../server/models/survey';
import UserError from '../../server/error/UserError';
import { surveyOne } from '../fixtures/survey';
import { ObjectId } from 'bson';
import BackgroundJob, { JobStatus } from '../../server/models/backgroundJob';
import BadRequestError from '../../server/error/BadRequestError';
import { createMongooseModel } from '../setup';
import { AssessmentResultType } from '../../server/service/materiality-assessment/types';

describe('surveyMiddlewares', () => {
  const sandbox = createSandbox();
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: sinon.SinonStub;

  describe('canAccessInsights', async () => {
    let canAccessAllDataStub: sinon.SinonStub;
    let notPermittedStub: sinon.SinonStub;

    beforeEach(() => {
      nextFunction = sandbox.stub();
      sandbox.stub(SurveyRepository, 'mustFindById');
      notPermittedStub = sandbox.stub();
      mockResponse = { locals: {}, NotPermitted: notPermittedStub };
    });

    afterEach(() => sandbox.restore());

    it('should reject when user not available', async () => {
      mockRequest = { params: { initiativeId: initiativeOneSimple._id.toString() } };
      await canAccessInsights(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(notPermittedStub.calledOnce).to.be.true;
    });

    it('should return PermissionDeniedError when user has no access', async () => {
      const userModel = new User({
        ...userOne,
        permissions: [],
      });
      mockRequest = {
        params: { initiativeId: initiativeTwo._id.toString() },
        user: userModel,
      };
      canAccessAllDataStub = sandbox.stub(SurveyPermissions, 'canAccessAllData').resolves(false);

      await canAccessInsights(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(canAccessAllDataStub.calledOnce).to.be.true;
      expect(notPermittedStub.calledOnce).to.be.true;
    });

    it('should return next when user has access', async () => {
      const userModel = new User({
        ...userOne,
        permissions: [UserRoles.Viewer],
      });
      mockRequest = {
        params: { initiativeId: initiativeTwo._id.toString() },
        user: userModel,
      };
      nextFunction = sinon.stub();
      canAccessAllDataStub = sandbox.stub(SurveyPermissions, 'canAccessAllData').resolves(true);

      await canAccessInsights(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(canAccessAllDataStub.calledOnce).to.be.true;
      expect(notPermittedStub.calledOnce).not.to.be.true;
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0]).to.be.undefined;
    });
  });

  describe('canAccessInsightsWithStaff', async () => {
    let canAccessAllDataStub: sinon.SinonStub;
    let notPermittedStub: sinon.SinonStub;

    beforeEach(() => {
      nextFunction = sandbox.stub();
      sandbox.stub(SurveyRepository, 'mustFindById');
      notPermittedStub = sandbox.stub();
      mockResponse = { locals: {}, NotPermitted: notPermittedStub };
    });

    afterEach(() => sandbox.restore());

    it('should reject when user not available', async () => {
      mockRequest = { params: { initiativeId: initiativeOneSimple._id.toString() } };
      await canAccessInsightsWithStaff(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(notPermittedStub.calledOnce).to.be.true;
    });

    it('should return next when user has no access but is staff', async () => {
      const userModel = new User({
        ...userOne,
        isStaff: true,
        permissions: [],
      });
      mockRequest = {
        params: { initiativeId: initiativeTwo._id.toString() },
        user: userModel,
      };
      canAccessAllDataStub = sandbox.stub(SurveyPermissions, 'canAccessAllData');

      await canAccessInsightsWithStaff(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(canAccessAllDataStub.callCount).to.equal(0);
      expect(notPermittedStub.callCount).to.equal(0);
      expect(nextFunction.calledOnce).to.be.true;
    });

    it('should return PermissionDeniedError when user has no access and is not staff', async () => {
      const userModel = new User({
        ...userOne,
        permissions: [],
      });
      mockRequest = {
        params: { initiativeId: initiativeTwo._id.toString() },
        user: userModel,
      };
      canAccessAllDataStub = sandbox.stub(SurveyPermissions, 'canAccessAllData').resolves(false);

      await canAccessInsightsWithStaff(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(canAccessAllDataStub.calledOnce).to.be.true;
      expect(notPermittedStub.calledOnce).to.be.true;
    });

    it('should return next when user has access and is not staff', async () => {
      const userModel = new User({
        ...userOne,
        permissions: [UserRoles.Viewer],
      });
      mockRequest = {
        params: { initiativeId: initiativeTwo._id.toString() },
        user: userModel,
      };
      nextFunction = sinon.stub();
      canAccessAllDataStub = sandbox.stub(SurveyPermissions, 'canAccessAllData').resolves(true);

      await canAccessInsightsWithStaff(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(canAccessAllDataStub.calledOnce).to.be.true;
      expect(notPermittedStub.callCount).to.equal(0);
      expect(nextFunction.calledOnce).to.be.true;
    });
  });

  describe('isMaterialitySurvey', () => {
    beforeEach(() => {
      nextFunction = sandbox.stub();
      mockResponse = { locals: { survey: undefined } };
      mockRequest = { params: { surveyId: surveyOne._id.toString() } };
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should call next() if survey is already set and is a materiality survey', async () => {
      mockResponse = { locals: { survey: { type: SurveyType.Materiality } } };
      await isMaterialitySurvey(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0]).to.be.undefined;
    });

    it('should retrieve survey from database and call next() if it is a materiality survey', async () => {
      sandbox.stub(SurveyRepository, 'mustFindById').resolves(new Survey({ type: SurveyType.Materiality }));
      await isMaterialitySurvey(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0]).to.be.undefined;
    });

    it('should return error if survey is not a materiality survey', async () => {
      sandbox.stub(SurveyRepository, 'mustFindById').resolves(new Survey({ type: SurveyType.Default }));
      await isMaterialitySurvey(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0] instanceof UserError).to.be.true;
      expect(nextFunction.args[0][0].message).to.equal('This assessment is not a materiality assessment');
      expect(nextFunction.args[0][0].context).to.eqls({ surveyId: surveyOne._id.toString() });
    });
  });

  describe('isScoresCalculated', () => {
    const scoreJobId = new ObjectId().toString();

    beforeEach(() => {
      nextFunction = sandbox.stub();
      mockResponse = { locals: { survey: undefined } };
      mockRequest = { params: { scoreJobId } };
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should call next with BadRequestError when job does not exist', async () => {
      sandbox.stub(BackgroundJob, 'findOne').returns(createMongooseModel(null));
      await isScoresCalculated(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0] instanceof BadRequestError).to.be.true;
      expect(nextFunction.args[0][0].message).to.equal('Materiality scores are not available. Please try again later.');
      expect(nextFunction.args[0][0].errors).to.eqls({ scoreJobId });
    });

    it('should call next with BadRequestError when job status is not Completed', async () => {
      const job = {
        status: JobStatus.Pending,
        tasks: [
          {
            data: {
              result: {
                [AssessmentResultType.Financial]: [],
                [AssessmentResultType.Impact]: [],
              },
            },
          },
        ],
      };
      sandbox.stub(BackgroundJob, 'findOne').returns(createMongooseModel(job));
      await isScoresCalculated(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.args[0][0] instanceof BadRequestError).to.be.true;
      expect(nextFunction.args[0][0].message).to.equal('Materiality scores are not available. Please try again later.');
      expect(nextFunction.args[0][0].errors).to.eqls({ scoreJobId });
    });

    it('should call next without errors when job is found and completed', async () => {
      const job = {
        status: JobStatus.Completed,
        tasks: [
          {
            data: {
              result: {
                [AssessmentResultType.Financial]: [],
                [AssessmentResultType.Impact]: [],
              },
            },
          },
        ],
      };
      sandbox.stub(BackgroundJob, 'findOne').returns(createMongooseModel(job));
      await isScoresCalculated(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction.calledOnce).to.be.true;
      expect(nextFunction.args[0][0]).to.be.undefined;
    });
  });
});
