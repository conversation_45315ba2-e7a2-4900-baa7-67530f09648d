import { expect } from 'chai';
import { DashboardMediaFilesManager } from '../../../server/service/insight-dashboard/DashboardMediaFilesManager';
import { disconnect, connect } from '../../setup/mongoInMemory';
import { setupDocument, setupInsightDashboard } from '../../setup/mongoInMemoryFixtures';
import sinon from 'sinon';
import { ObjectId } from 'bson';
import { createSimpleDashboardItem } from '../../fixtures/dashboardFixtures';
import { DocumentOwnerType } from '../../../server/models/document';
import Document from '../../../server/models/document';
import { InsightDashboardItemType } from '../../../server/models/insightDashboard';
import { initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';

before(connect);
after(disconnect);

describe('DashboardMediaFilesManager', () => {
  let dashboardMediaFilesManager: DashboardMediaFilesManager;
  let storageMock: any;

  beforeEach(() => {
    storageMock = {
      upload: sinon.stub(),
      remove: sinon.stub(),
      getExtensionFromMimeType: sinon.stub(),
    };
    dashboardMediaFilesManager = new DashboardMediaFilesManager(storageMock);
  });
  afterEach(() => {
    sinon.restore();
  });

  describe('deleteFilesAndReassignOwnership', () => {
    it('deletes dashboard with no media item', async () => {
      const dashboard = await setupInsightDashboard({ items: [] });
      const removeStub = storageMock.remove.resolves([]);
      const result = await dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
        dashboardId: dashboard._id,
        initiativeId: dashboard.initiativeId,
        deletedDashboardDocumentIds: [],
      });
      expect(removeStub.callCount).to.equal(0);
      expect(result).eqls([]);
    });

    it('deletes dashboard with 1 media item that not belongs to any other dashboard', async () => {
      const deletedDocumentId = new ObjectId();

      const dashboard = await setupInsightDashboard({
        _id: new ObjectId(),
        items: [createSimpleDashboardItem({ files: [{ documentId: deletedDocumentId }] })],
      });
      const document = await setupDocument({
        _id: deletedDocumentId,
        ownerId: dashboard._id,
        ownerType: DocumentOwnerType.InsightDashboard,
      });
      const mockResults = [{ path: document.path, statusCode: 200 }];
      const removeStub = storageMock.remove.resolves(mockResults);
      const result = await dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
        dashboardId: dashboard._id,
        initiativeId: dashboard.initiativeId,
        deletedDashboardDocumentIds: [document._id.toString()],
      });
      expect(removeStub.callCount).to.equal(1);
      expect(result).eqls([{ status: 'fulfilled', value: mockResults }]);
    });

    it('deletes dashboard with 1 media item that belongs to other dashboard', async () => {
      const deletedDocumentId = new ObjectId();

      const otherDashboard = await setupInsightDashboard({
        _id: new ObjectId(),
        initiativeId: initiativeOneSimpleId,
        items: [
          createSimpleDashboardItem({
            files: [{ documentId: deletedDocumentId }],
            type: InsightDashboardItemType.Media,
          }),
        ],
      });
      const dashboard = await setupInsightDashboard({
        _id: new ObjectId(),
        initiativeId: initiativeOneSimpleId,
        items: [
          createSimpleDashboardItem({
            files: [{ documentId: deletedDocumentId }],
            type: InsightDashboardItemType.Media,
          }),
        ],
      });
      const document = await setupDocument({
        _id: deletedDocumentId,
        ownerId: dashboard._id,
        ownerType: DocumentOwnerType.InsightDashboard,
      });

      const removeStub = storageMock.remove.resolves([]);
      await dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
        dashboardId: dashboard._id,
        initiativeId: dashboard.initiativeId,
        deletedDashboardDocumentIds: [document._id.toString()],
      });

      const doc = await Document.findById(deletedDocumentId).exec();
      expect(doc?.ownerId?.toString()).eq(otherDashboard._id.toString());
      expect(removeStub.callCount).to.equal(0);
    });

    it('deletes a dashboard with two media items: one exclusive and one shared with another dashboard', async () => {
      const sharedDocumentId = new ObjectId();
      const exclusiveDocumentId = new ObjectId();

      const otherDashboard = await setupInsightDashboard({
        _id: new ObjectId(),
        initiativeId: initiativeOneSimpleId,
        items: [
          createSimpleDashboardItem({
            files: [{ documentId: sharedDocumentId }],
            type: InsightDashboardItemType.Media,
          }),
        ],
      });

      const dashboard = await setupInsightDashboard({
        _id: new ObjectId(),
        initiativeId: initiativeOneSimpleId,
        items: [
          createSimpleDashboardItem({
            files: [{ documentId: sharedDocumentId }],
            type: InsightDashboardItemType.Media,
          }),
          createSimpleDashboardItem({
            files: [{ documentId: exclusiveDocumentId }],
            type: InsightDashboardItemType.Media,
          }),
        ],
      });

      await setupDocument({
        _id: sharedDocumentId,
        ownerId: dashboard._id,
        ownerType: DocumentOwnerType.InsightDashboard,
      });

      const exclusiveDocument = await setupDocument({
        _id: exclusiveDocumentId,
        ownerId: dashboard._id,
        ownerType: DocumentOwnerType.InsightDashboard,
      });

      const mockResults = [{ path: exclusiveDocument.path, statusCode: 200 }];
      const removeStub = storageMock.remove.resolves(mockResults);

      const result = await dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
        dashboardId: dashboard._id,
        initiativeId: dashboard.initiativeId,
        deletedDashboardDocumentIds: [sharedDocumentId.toString(), exclusiveDocumentId.toString()],
      });

      const doc = await Document.findById(sharedDocumentId).exec();
      expect(doc?.ownerId?.toString()).eq(otherDashboard._id.toString());
      expect(removeStub.callCount).to.equal(1);
      expect(result).eqls([{ status: 'fulfilled', value: mockResults }]);
    });
  });
});
