import { getExportImportManager, InitiativeSheetRow } from '../../../server/service/organization/ExportImportManager';
import { expect } from 'chai';
import sinon from 'sinon';
import { InitiativeRepository } from '../../../server/repository/InitiativeRepository';
import { allChildrenById } from '../../fixtures/surveyEngagementManagerFixtures';
import * as fileUtils from './../../../server/service/file/writer/CsvFileWriter';
import Initiative, { DEFAULT_USAGE } from '../../../server/models/initiative';
import { initiativeTwo } from '../../fixtures/initiativeFixtures';
import { createMongooseModel } from '../../setup';
import UserError from '../../../server/error/UserError';

type BulkWriteResult = Awaited<ReturnType<typeof Initiative.bulkWrite>>;

// root, 11, 121
const [rootInitiative, existingChildOne, existingChildTwo] = allChildrenById.map((initiative) => ({
  ...initiative,
  tags: undefined,
}));

const invalidExistingInitiative = { ...initiativeTwo, tags: undefined };
const filePath = 'fake/file.csv';

describe('ExportImportManager', () => {
  const service = getExportImportManager();

  describe('importFile', () => {
    beforeEach(() => {
      sinon.stub(InitiativeRepository, 'getAllChildrenById').resolves(allChildrenById);
    });

    afterEach(() => {
      sinon.restore();
    });

    describe('invalid cases', () => {

      before(() => {
        sinon.stub(Initiative, 'bulkWrite')
          .resolves({ upsertedCount: 0, modifiedCount: 0 } as BulkWriteResult)
      })

      it('should throw error if wrong root node import', async () => {
        const rawInitiatives = [invalidExistingInitiative];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel([invalidExistingInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 1 errored row: Row 1/
        );
      });

      it('should throw error if node to import exists but belonged to another tree', async () => {
        const rawInitiatives = [
          rootInitiative,
          {
            ...invalidExistingInitiative,
            parentInitiativeCode: rootInitiative.code,
          },
        ];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel([rootInitiative, invalidExistingInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 1 errored row: Row 2/
        );
      });

      it('should throw error if parentInitiativeCode exists but not in the current tree', async () => {
        const rawInitiatives = [
          rootInitiative,
          { ...existingChildOne, parentInitiativeCode: invalidExistingInitiative.code },
        ];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon
          .stub(Initiative, 'find')
          .returns(createMongooseModel([rootInitiative, existingChildOne, invalidExistingInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 1 errored row: Row 2/
        );
      });

      it('should throw error if parentInitiativeCode is not existed & no import for it', async () => {
        const rawInitiatives = [
          rootInitiative,
          { ...existingChildOne, parentInitiativeCode: 'new-parent-code' },
          // no import for new node 'new-parent-code'
        ];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon
          .stub(Initiative, 'find')
          .returns(createMongooseModel([rootInitiative, existingChildOne, invalidExistingInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 1 errored row: Row 2/
        );
      });

      it('should throw error if existing node is assigned to its child', async () => {
        const rawInitiatives = [rootInitiative, { ...existingChildOne, parentInitiativeCode: existingChildTwo.code }];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel(allChildrenById));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 1 errored row: Row 2/
        );
      });

      it('should throw error if new node is assigned to its new child node', async () => {
        const rawInitiatives = [
          {
            code: 'new-parent',
            name: 'New Parent',
            parentInitiativeCode: 'new-child',
            usage: DEFAULT_USAGE,
          },
          {
            code: 'new-child',
            name: 'New Child',
            parentInitiativeCode: 'new-parent',
            usage: DEFAULT_USAGE,
          },
        ];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel([rootInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 2 errored rows: Row 1, 2/
        );
      });

      it('Ensure deeply nested tree is not recursive', async () => {
        const rawInitiatives = [
          {
            code: 'parent',
            name: 'New Parent',
            parentInitiativeCode: rootInitiative.code,
            usage: DEFAULT_USAGE,
          },
          {
            code: 'child',
            name: 'New Child',
            parentInitiativeCode: 'grand-child-child',
            usage: DEFAULT_USAGE,
          },
          {
            code: 'grand-child',
            name: 'Grand Child',
            parentInitiativeCode: 'child',
            usage: DEFAULT_USAGE,
          },
          {
            code: 'grand-child-child',
            name: 'Grand Child',
            parentInitiativeCode: 'grand-child',
            usage: DEFAULT_USAGE,
          },
        ];
        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel([rootInitiative]));
        await expect(service.importFile(rootInitiative, filePath)).to.eventually.be.rejectedWith(
          UserError,
          /Found 3 errored rows: Row 2, 3, 4/
        );
      });

    });

    describe('valid cases', () => {
      it('should update valid nodes', async () => {
        const rawInitiatives: InitiativeSheetRow[] = [
          rootInitiative,
          // new node is attached to root
          {
            code: 'new-branch-a',
            name: 'New Branch A',
            parentInitiativeCode: rootInitiative.code,
            usage: DEFAULT_USAGE,
          },
          // new node is attached to existing node tree
          {
            code: 'new-branch-B',
            name: 'New Branch B',
            parentInitiativeCode: existingChildOne.code,
            usage: DEFAULT_USAGE,
          },
          // new node is attached to below new node in the import list, regardless of the order
          {
            code: 'new-branch-c-1',
            name: 'New Branch C1',
            parentInitiativeCode: 'new-branch-c',
            usage: DEFAULT_USAGE,
          },
          // new node that has children created above
          {
            code: 'new-branch-c',
            name: 'New Branch C',
            parentInitiativeCode: existingChildTwo.code,
            usage: DEFAULT_USAGE,
          },
          // existing node tree now been attached to new node
          {
            code: existingChildOne.code,
            name: existingChildOne.name,
            parentInitiativeCode: 'new-branch-c', // used to be rootInitiative
            usage: DEFAULT_USAGE,
          },
          // existing node now been attached to another node in tree
          {
            code: existingChildTwo.code,
            name: existingChildTwo.name,
            parentInitiativeCode: rootInitiative.code, // used to be existingChildOne
            usage: DEFAULT_USAGE,
          },
        ];

        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon
          .stub(Initiative, 'find')
          .returns(createMongooseModel([rootInitiative, existingChildOne, invalidExistingInitiative]));
        const bulkUpdateStub = sinon
          .stub(Initiative, 'bulkWrite')
          .resolves({ upsertedCount: 4, modifiedCount: 2 } as BulkWriteResult);

        await service.importFile(rootInitiative, filePath);
        const updates = (bulkUpdateStub.args[0][0] as { updateOne: { update: any } }[]).map(
          ({ updateOne }) => updateOne.update
        );
        expect(updates.length).to.equal(6);
      });

      it('should update existing nodes with swapping parent', async () => {
        const rawInitiatives = [
          {
            code: existingChildTwo.code,
            name: existingChildTwo.name,
            parentInitiativeCode: rootInitiative.code, // used to be existingChildOne
            usage: DEFAULT_USAGE,
          },
          {
            code: existingChildOne.code,
            name: existingChildOne.name,
            parentInitiativeCode: existingChildTwo.code, // attached to its child, but its child is attached to other node above
            usage: DEFAULT_USAGE,
          },
        ];

        sinon.stub(fileUtils, 'readCSVFile').resolves(rawInitiatives);
        sinon.stub(Initiative, 'find').returns(createMongooseModel(allChildrenById));
        const bulkUpdateStub = sinon
          .stub(Initiative, 'bulkWrite')
          .resolves({ upsertedCount: 0, modifiedCount: 2 } as BulkWriteResult);

        await service.importFile(rootInitiative, filePath);
        const updates = (bulkUpdateStub.args[0][0] as { updateOne: { update: any } }[]).map(({ updateOne }) => ({
          code: updateOne.update.code,
          parentId: updateOne.update.parentId,
        }));

        const expectedUpdates = [
          { code: existingChildTwo.code, parentId: rootInitiative._id },
          { code: existingChildOne.code, parentId: existingChildTwo._id },
        ];

        expect(updates).to.deep.equal(expectedUpdates);
      });
    });
  });
});
