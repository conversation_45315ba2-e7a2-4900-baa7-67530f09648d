import { expect } from 'chai';
import {
  AggregationMode,
  UtrValueType,
  ValueAggregation,
  type AggregationConfig,
} from '../../../../server/models/public/universalTrackerType';
import { getAggregatorByUniversalTrackerFn } from '../../../../server/service/utr/aggregation/utrTypeAggregator';
import type { UniversalTrackerPlain } from '../../../../server/models/universalTracker';

describe('utrTypeAggregator', () => {

  describe('getAggregatorByUniversalTrackerFn', () => {
    it('should return ValueAggregation.EmptyAggregator if it is set on the UTR', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.EmptyAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
      const result2 = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);

      expect(result).eq(ValueAggregation.EmptyAggregator);
      expect(result2).eq(ValueAggregation.EmptyAggregator);
    });

    it('should use the right compatibility list based on the aggregation mode and return the default aggregator if the one set on the UTR is not compatible', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueAverageAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);

      expect(result).eq(ValueAggregation.ValueSumAggregator);
    });

    it('should use the right compatibility list based on the aggregation mode and return the utr aggregator if it is compatible', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueAverageAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
      expect(result).eq(ValueAggregation.ValueAverageAggregator);
    });

    describe('with aggregationConfig mode-specific overrides', () => {
      it('should use children mode override when in Children aggregation mode', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: ValueAggregation.ValueSumAggregator,
              [AggregationMode.Combined]: ValueAggregation.LatestAggregator,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use combined mode override when in Combined aggregation mode', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: ValueAggregation.ValueSumAggregator,
              [AggregationMode.Combined]: ValueAggregation.LatestAggregator,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.LatestAggregator);
      });

      it('should fall back to valueAggregation when mode-specific config is not compatible', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              // TableColumnAggregator is not compatible with Number type
              [AggregationMode.Children]: ValueAggregation.TableColumnAggregator as ValueAggregation,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should fall back to valueAggregation since mode-specific is incompatible
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use mode-specific config even when valueAggregation is set', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: ValueAggregation.LatestAggregator,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        // Mode-specific config takes precedence
        expect(result).to.equal(ValueAggregation.LatestAggregator);
      });
    });

    describe('backward compatibility', () => {
      it('should use valueAggregation when no aggregationConfig is present', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });

      it('should use valueAggregation when aggregationConfig has empty modes', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {},
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use default aggregation when neither aggregationConfig nor valueAggregation is compatible', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          // TableColumnAggregator is not compatible with Number type
          valueAggregation: ValueAggregation.TableColumnAggregator as ValueAggregation,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should fall back to default for Number type in Children mode
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use default aggregation when no aggregation config is provided', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Percentage,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should use default for Percentage type in Children mode
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });
    });

    describe('different value types', () => {
      it('should handle Text type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Text,
          valueAggregation: ValueAggregation.LatestAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: ValueAggregation.ValueConcatenateAggregator,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueConcatenateAggregator);
      });

      it('should handle Table type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Table,
          valueAggregation: ValueAggregation.TableColumnAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: ValueAggregation.TableRowGroupAggregator,
              [AggregationMode.Children]: ValueAggregation.TableColumnAggregator,
            },
          },
        };

        let result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.TableRowGroupAggregator);

        result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.TableColumnAggregator);
      });

      it('should handle NumericValueList type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.NumericValueList,
          valueAggregation: ValueAggregation.NumericValueListSumAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: ValueAggregation.NumericValueListAverageAggregator,
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.NumericValueListAverageAggregator);
      });
    });

    describe('edge cases', () => {
      it('should handle undefined aggregationConfig gracefully', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: undefined,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should handle undefined modes in aggregationConfig gracefully', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: undefined,
          } as AggregationConfig,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should handle partial mode configuration', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              // Only combined mode is configured with a compatible aggregation
              [AggregationMode.Combined]: ValueAggregation.ValueAverageAggregator,
            },
          },
        };

        // Should fall back to valueAggregation for children (no mode-specific config)
        let result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);

        // Should use mode-specific for combined
        result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });
    });
  });
});
