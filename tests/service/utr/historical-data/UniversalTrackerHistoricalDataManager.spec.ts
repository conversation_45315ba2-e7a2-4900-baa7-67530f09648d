import { expect } from 'chai';
import { createSandbox } from 'sinon';
import {
  getUniversalTrackerHistoricalDataManager,
  UniversalTrackerHistoricalDataManager,
} from '../../../../server/service/utr/historical-data/UniversalTrackerHistoricalDataManager';
import UniversalTrackerValueHistoryService from '../../../../server/service/utr/UniversalTrackerValueHistoryService';
import AggregatorDataService from '../../../../server/service/utr/aggregation/aggregatorDataService';
import { ObjectId } from 'bson';
import { initiativeOneSimple, initiativeTwo } from '../../../fixtures/initiativeFixtures';
import { universalTrackerOne, utrOneId, utrTwoId } from '../../../fixtures/universalTrackerFixtures';
import { utrvOne } from '../../../fixtures/universalTrackerValueFixtures';
import { HistoricalUtrs, UtrvFilter } from '../../../../server/models/insightDashboard';
import { InitiativeRepository } from '../../../../server/repository/InitiativeRepository';

describe('UniversalTrackerHistoricalDataManager', () => {
  const sandbox = createSandbox();

  it('should create instance', () => {
    const instance = getUniversalTrackerHistoricalDataManager();
    expect(instance).to.be.instanceOf(UniversalTrackerHistoricalDataManager);
  });

  const manager = getUniversalTrackerHistoricalDataManager();
  const utrIds = [utrOneId.toString(), utrTwoId.toString()];
  const utrvFilters = {
    isCompletedData: true,
    assuredOnly: true,
    isPublicOnly: true,
    showBaselines: true,
    showTargets: true,
    period: undefined,
    dateRange: undefined,
    surveyType: undefined,
    status: undefined,
  };
  const mockedResult: HistoricalUtrs[] = [
    {
      utr: universalTrackerOne,
      utrvs: [utrvOne],
    },
  ];

  describe('getUtrsHistoricalData', () => {
    afterEach(() => sandbox.restore());

    it('should return correct historical utrs data', async () => {
      const initiativeId = initiativeOneSimple._id.toString();
      const getHistoricalUtrDataStub = sandbox.stub().resolves(mockedResult);

      const utrService = {
        initiativeId,
        dataService: new AggregatorDataService(),
        period: undefined,
        dateRange: undefined,
        getHistoricalUtrData: getHistoricalUtrDataStub,
        get: sandbox.stub(),
        getUniversalTrackers: sandbox.stub(),
        getTargets: sandbox.stub(),
      } as unknown as UniversalTrackerValueHistoryService;

      const createStub = sandbox.stub(UniversalTrackerValueHistoryService, 'create').resolves(utrService);

      const utrsData = await manager.getUtrsHistoricalData({
        initiativeId,
        utrIds,
        utrvFilters,
      });

      expect(createStub.calledOnce).to.be.true;
      expect(getHistoricalUtrDataStub.calledOnce).to.be.true;
      expect(utrsData).to.deep.equal(mockedResult);
    });

    it('should pass provided status to UniversalTrackerValueHistoryService.create', async () => {
      const initiativeId = initiativeOneSimple._id.toString();
      const getHistoricalUtrDataStub = sandbox.stub().resolves(mockedResult);

      const utrService = {
        initiativeId,
        dataService: new AggregatorDataService(),
        period: undefined,
        dateRange: undefined,
        getHistoricalUtrData: getHistoricalUtrDataStub,
        get: sandbox.stub(),
        getUniversalTrackers: sandbox.stub(),
        getTargets: sandbox.stub(),
      } as unknown as UniversalTrackerValueHistoryService;

      const createStub = sandbox.stub(UniversalTrackerValueHistoryService, 'create').resolves(utrService);

      const utrsData = await manager.getUtrsHistoricalData({
        initiativeId,
        utrIds,
        utrvFilters: {...utrvFilters, status: UtrvFilter.Assured},
      });

      expect(createStub.calledOnce).to.be.true;
      expect(createStub.args[0][0].status).to.equal(UtrvFilter.Assured);
      expect(getHistoricalUtrDataStub.calledOnce).to.be.true;
      expect(utrsData).to.deep.equal(mockedResult);
    });
  });

  describe('getSubsidiariesUtrsData', () => {
    afterEach(() => sandbox.restore());

    it('should return empty array when no subsidiaries are passed', async () => {
      const initiativeId = initiativeOneSimple._id;
      const initiativeIds: ObjectId[] = [];
      const utrsData = await manager.getSubsidiariesUtrsData({
        initiativeId,
        initiativeIds,
        utrIds,
        utrvFilters,
      });
      expect(utrsData).to.deep.equal([]);
    });

    it('should return correct subsidiaries data', async () => {
      const initiativeId = initiativeOneSimple._id;
      const initiativeIds = [initiativeOneSimple._id, initiativeTwo._id];

      const getChildrenStub = sandbox
        .stub(InitiativeRepository, 'getAllChildrenById')
        .resolves([initiativeOneSimple, initiativeTwo]);

      const createStub = sandbox.stub(UniversalTrackerValueHistoryService, 'create').callsFake(({ initiativeId }) =>
        Promise.resolve({
          initiativeId,
          dataService: new AggregatorDataService(),
          period: undefined,
          dateRange: undefined,
          getHistoricalUtrData: getHistoricalUtrDataStub,
          get: sandbox.stub(),
          getUniversalTrackers: sandbox.stub(),
          getTargets: sandbox.stub(),
        } as unknown as UniversalTrackerValueHistoryService)
      );
      const getHistoricalUtrDataStub = sandbox.stub().resolves(mockedResult);

      const utrsData = await manager.getSubsidiariesUtrsData({
        initiativeId,
        initiativeIds,
        utrIds,
        utrvFilters,
      });

      expect(createStub.callCount).to.equal(initiativeIds.length);
      expect(getChildrenStub.calledOnce).to.be.true;
      expect(getHistoricalUtrDataStub.callCount).to.equal(initiativeIds.length);
      expect(utrsData).to.deep.equal([
        {
          utr: universalTrackerOne,
          utrvs: [
            { ...utrvOne, initiative: initiativeOneSimple },
            { ...utrvOne, initiative: initiativeTwo },
          ],
        },
      ]);
    });
  });
});
