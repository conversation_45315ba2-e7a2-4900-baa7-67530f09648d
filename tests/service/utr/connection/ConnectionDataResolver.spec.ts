import { expect } from 'chai';
import {
  Calculation,
  CalculationGroupValueType,
  CalculationType,
} from '../../../../server/models/calculationGroup';
import {
  ConnectionDataResolver,
  ConnectionDataResolverParams,
} from '../../../../server/service/utr/connection/ConnectionDataResolver';
import { ColumnType, UtrValueType } from '../../../../server/models/public/universalTrackerType';
import {
  CalculationUtrv,
  DEFAULT_UNIT_NUMBER_SCALE,
  Utr,
  UtrCodeCalculationUtrvMap,
  Utrv,
} from '../../../../server/service/utr/connection/types';
import { UtrCodeUtrvMap } from '../../../../server/service/utr/connection/types';
import { MetricGroupMin } from '../../../../server/repository/MetricGroupRepository';
import { ObjectId } from 'bson';
import {
  mockCalculationGroup1,
  mockCalculationGroup2,
  mockUtrCodeToUtrMap,
  calGroupCode1,
  calGroupCode2,
  calGroupCode3,
  mockCalculationGroupInvalid,
} from '../../../fixtures/calculationGroupFixtures';
import { universalTrackerOne } from '../../../fixtures/universalTrackerFixtures';
import { NumberScale } from '../../../../server/service/units/unitTypes';

// Mock data for UtrCodeCalculationUtrvMap
const mockIntegrationUtrCodeUtrvMap: UtrCodeCalculationUtrvMap = new Map([
  ['utr1', { _id: new ObjectId(), valueType: UtrValueType.Number, value: 100 }],
]);

// Mock data for surveyIdToUtrCodeUtrvMap
const mockUtrvId1 = new ObjectId();
const mockUtrvId2 = new ObjectId();
const mockSurveyIdToUtrCodeUtrvMap: Map<string, UtrCodeUtrvMap> = new Map([
  ['survey1', new Map([['utr1', { _id: mockUtrvId1, valueType: UtrValueType.Number, value: 100 } as Utrv]])],
  [
    'survey2',
    new Map([['utr2', { _id: mockUtrvId2, valueType: UtrValueType.Text, valueData: { data: 'Sample Text' } } as Utrv]]),
  ],
]);

// Mock data for utrCodeToGroupMap
const mockUtrCodeToGroupMap: Map<string, MetricGroupMin> = new Map([
  ['utr1', { groupName: 'Group1', groupData: { colour: 'red', link: 'link1', icon: 'icon1' }, universalTrackers: [] }],
]);

// Mock params for ConnectionDataResolver
const mockParams: ConnectionDataResolverParams = {
  calculationGroups: [mockCalculationGroup1, mockCalculationGroup2],
  utrCodeToUtrMap: mockUtrCodeToUtrMap,
  integrationUtrCodeUtrvMap: mockIntegrationUtrCodeUtrvMap,
  surveyIdToUtrCodeUtrvMap: mockSurveyIdToUtrCodeUtrvMap,
  utrCodeToGroupMap: mockUtrCodeToGroupMap,
};

describe('ConnectionDataResolver', () => {
  let resolver: ConnectionDataResolver;

  beforeEach(() => {
    resolver = new ConnectionDataResolver(mockParams);
  });

  describe('populateConnectionsData function', () => {
    it('should populate numeric connection data correctly', () => {
      const result = resolver.populateConnectionsData();
      const numericGroup = result.find((group) => group.code === calGroupCode1);

      expect(numericGroup).to.exist;
      expect(numericGroup?.valueType).to.equal(CalculationGroupValueType.Numeric);
      expect(numericGroup?.calculations[0].data).to.deep.equal([
        {
          value: 100,
          variables: {
            a: {
              utrvId: mockUtrvId1,
              value: 100,
              unit: 'kg',
              numberScale: '1',
            },
          },
          surveyId: 'survey1',
        },
      ]);
    });

    it('should populate text connection data correctly', () => {
      const result = resolver.populateConnectionsData();
      const textGroup = result.find((group) => group.code === calGroupCode2);

      expect(textGroup).to.exist;
      expect(textGroup?.valueType).to.equal(CalculationGroupValueType.Text);
      expect(textGroup?.calculations[0].data).to.deep.equal([
        {
          value: 'Sample Text',
          variables: {
            a: {
              value: 'Sample Text',
              utrvId: mockUtrvId2,
            },
          },
          surveyId: 'survey2',
        },
      ]);
    });

    it('should update surveyIds correctly', () => {
      resolver.populateConnectionsData();
      const surveyIds = resolver.getSurveyIds();

      expect(surveyIds).to.deep.equal(['survey1', 'survey2']);
    });

    it('should handle empty calculation groups', () => {
      const emptyResolver = new ConnectionDataResolver({
        ...mockParams,
        calculationGroups: [],
      });

      const result = emptyResolver.populateConnectionsData();
      expect(result).to.deep.equal([]);
    });

    it('should handle missing variables in calculation', () => {


      const missingVariableResolver = new ConnectionDataResolver({
        ...mockParams,
        calculationGroups: [mockCalculationGroupInvalid],
      });

      const result = missingVariableResolver.populateConnectionsData();
      const missingVarGroup = result.find((group) => group.code === calGroupCode3);

      expect(missingVarGroup).to.undefined;
    });
  });

  describe('resolveGroup function', () => {
    const mockCalculationId = new ObjectId();

    it('should return CalculationVariableGroup when given valid Direct calculation', () => {
      const calculation: Calculation = {
        _id: mockCalculationId,
        name: '',
        type: CalculationType.Direct,
        direct: 'a',
        variables: {
          a: {
            code: 'utr1',
          },
        },
      };
      const result = resolver.resolveGroup(calculation);
      expect(result).to.deep.equal({
        name: 'Group1',
        colour: 'red',
        link: 'link1',
        icon: 'icon1',
      });
    });

    it('should return undefined when direct variable reference is missing', () => {
      const calculation: Calculation = {
        _id: mockCalculationId,
        name: '',
        type: CalculationType.Direct,
        direct: 'missing_var',
        variables: {},
      };
      const result = resolver.resolveGroup(calculation);
      expect(result).to.be.undefined;
    });

    it('should return undefined when calculation type is not Direct', () => {
      const calculation: Calculation = {
        _id: mockCalculationId,
        name: '',
        type: CalculationType.Formula,
        formula: '{a} + {b}',
        variables: {},
      };
      const result = resolver.resolveGroup(calculation);
      expect(result).to.be.undefined;
    });

    it('should return undefined when variable is not found in calculation.variables', () => {
      const calculation: Calculation = {
        _id: mockCalculationId,
        name: '',
        type: CalculationType.Direct,
        variables: {
          a: {
            code: 'utr1',
          },
        },
        direct: 'nonExistentVariable',
      };
      const result = resolver.resolveGroup(calculation);
      expect(result).to.be.undefined;
    });
  });

  describe('getDataPointUnitNumberScale function', () => {
    const mockUtr: Utr = {
      ...universalTrackerOne,
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          columns: [
            {
              code: 'col1',
              name: 'col1',
              type: ColumnType.Number,
              unit: 'kg',
              numberScale: NumberScale.Single,
            },
          ],
        },
      },
    };
    it('should return column unit and numberScale when table column exists', () => {
      const result = resolver.getDataPointUnitNumberScale({
        utr: mockUtr,
        columnCode: 'col1',
      });
      expect(result).to.deep.equal({
        unit: 'kg',
        numberScale: NumberScale.Single,
      });
    });

    it('should return default unit and numberScale when columnCode is missing', () => {
      const result = resolver.getDataPointUnitNumberScale({
        utr: mockUtr,
      });
      expect(result).to.deep.equal(DEFAULT_UNIT_NUMBER_SCALE);
    });

    it(`should return unit and numberScale for Number type using UTR's values`, () => {
      const utr = {
        ...mockUtr,
        valueType: UtrValueType.Number,
        unit: 'kg',
        numberScale: NumberScale.Hundreds,
        valueValidation: undefined,
      };
      const result = resolver.getDataPointUnitNumberScale({ utr });
      expect(result).to.deep.equal({ unit: 'kg', numberScale: NumberScale.Hundreds });
    });

    it(`should return unit and numberScale for ValueList type using UTR's values`, () => {
      const utr = {
        ...mockUtr,
        valueType: UtrValueType.ValueList,
        unit: 'mt',
        numberScale: NumberScale.Thousands,
        valueValidation: undefined,
      };
      const result = resolver.getDataPointUnitNumberScale({ utr });
      expect(result).to.deep.equal({ unit: 'mt', numberScale: NumberScale.Thousands });
    });

    it('should return DEFAULT_UNIT_NUMBER_SCALE when Table type has no tableData', () => {
      const utr = {
        ...mockUtr,
        valueType: UtrValueType.Table,
        unit: 'mt',
        numberScale: NumberScale.Single,
        valueValidation: undefined,
      };
      const result = resolver.getDataPointUnitNumberScale({ utr });
      expect(result).to.deep.equal(DEFAULT_UNIT_NUMBER_SCALE);
    });

    it('should return DEFAULT_UNIT_NUMBER_SCALE when column numberScale is null', () => {
      const utr: Utr = {
        ...mockUtr,
        valueType: UtrValueType.Table,
        unit: 'mt',
        numberScale: NumberScale.Single,
        valueValidation: {
          table: {
            columns: [{ code: 'col1', name: 'col1', type: ColumnType.Number, unit: 'mt', numberScale: undefined }],
          },
        },
      };
      const result = resolver.getDataPointUnitNumberScale({ utr, columnCode: 'col1' });
      expect(result.unit).to.equal('mt');
      expect(result.numberScale).to.equal(DEFAULT_UNIT_NUMBER_SCALE.numberScale);
    });

    it('should extract column data correctly from table columns array', () => {
      const utr: Utr = {
        ...mockUtr,
        valueType: UtrValueType.Table,
        unit: undefined,
        numberScale: undefined,
        valueValidation: {
          table: {
            columns: [
              { code: 'col1', name: 'col1', type: ColumnType.Number, unit: 'm', numberScale: NumberScale.Hundreds },
              { code: 'col2', name: 'col2', type: ColumnType.Number, unit: 'cm', numberScale: NumberScale.Millions },
            ],
          },
        },
      };
      const result = resolver.getDataPointUnitNumberScale({ utr, columnCode: 'col2' });
      expect(result.unit).to.equal('cm');
      expect(result.numberScale).to.equal(NumberScale.Millions);
    });
  });

  describe('getNumericValue function', () => {
    const mockUtrv: CalculationUtrv = {
      valueType: UtrValueType.Table,
      valueData: {
        table: [
          [
            { code: 'col1', value: '10' },
            { code: 'col2', value: '20' }
          ],
          [
            { code: 'col1', value: '30' },
            { code: 'col2', value: '40' }
          ]
        ]
      }
    };

    it('should return sum of column values for table type when valid columnCode is provided', () => {
      const result = resolver.getNumericValue(mockUtrv, 'col1');
      expect(result).to.equal(40);
    });

    it('should return 0 when table data is missing for Table type', () => {
      const utrv: CalculationUtrv = {
        ...mockUtrv,
        valueData: {
          table: undefined
        }
      };
      const result = resolver.getNumericValue(utrv, 'col1');
      expect(result).to.equal(0);
    });

    it('should return total value for NumericValueList type when columnCode is "total"', () => {
      const utrv: CalculationUtrv = {
        valueType: UtrValueType.NumericValueList,
        value: 100,
      };
      const result = resolver.getNumericValue(utrv, 'total');
      expect(result).to.equal(100);
    });

    it('should return specific column value for NumericValueList type when columnCode is provided', () => {
      const utrv: CalculationUtrv = {
        valueType: UtrValueType.NumericValueList,
        valueData: { data: { 'column1': '50' } },
      };
      const result = resolver.getNumericValue(utrv, 'column1');
      expect(result).to.equal(50);
    });

    it('should return numeric value for ValueList, Number and Percentage types', () => {
      const utrv: CalculationUtrv = {
        valueType: UtrValueType.ValueList,
        value: 75,
      };
      const result = resolver.getNumericValue(utrv);
      expect(result).to.equal(75);
    });
  })

  describe('getTextValue function', () => {
    const utrv: CalculationUtrv = {
      valueType: UtrValueType.Table,
      valueData: {
        table: [
          [{ code: 'col1', value: 'value1' }],
          [{ code: 'col1', value: 'value2' }]
        ]
      }
    };

    it('should concatenate table column values when valueType is Table and columnCode matches', () => {
      const result = resolver.getTextValue(utrv, 'col1');
      expect(result).to.equal('value1 value2');
    });

    it('should return empty string when table data is missing for Table type', () => {
      const utrv: CalculationUtrv = {
        valueType: UtrValueType.Table,
        valueData: {
          table: undefined
        }
      };
      const result = resolver.getTextValue(utrv, 'col1');
      expect(result).to.equal('');
    });

    it('should return specific column data for TextValueList type when columnCode is provided', () => {
      const utrv = {
        valueType: UtrValueType.TextValueList,
        valueData: {
          data: {
            'column1': 'expectedValue'
          }
        }
      };
      const result = resolver.getTextValue(utrv, 'column1');
      expect(result).to.equal('expectedValue');
    });

    it('should return direct data or input data for Text type', () => {
      const utrvText = {
        valueType: UtrValueType.Text,
        valueData: {
          input: {
            data: 'textData',
            unit: undefined,
            numberScale: undefined,
          }
        }
      };
      const resultText = resolver.getTextValue(utrvText);
      expect(resultText).to.equal('textData');
    });

    it('should handle default case by returning data or input data value', () => {
      const utrvDefault = {
        valueType: UtrValueType.ValueList,
        valueData: {
          input: {
            data: 'valueListData',
            unit: undefined,
            numberScale: undefined,
          }
        }
      };
      const resultDefault = resolver.getTextValue(utrvDefault);
      expect(resultDefault).to.equal('');
    });
  })
});
