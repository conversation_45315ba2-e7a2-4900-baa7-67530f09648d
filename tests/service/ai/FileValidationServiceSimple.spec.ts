/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { FileValidationService } from '../../../server/service/ai/FileValidationService';
import { testLogger } from '../../factories/logger';

describe('FileValidationService - Core Methods', () => {
  const sandbox = sinon.createSandbox();
  let service: FileValidationService;

  beforeEach(() => {
    service = new FileValidationService(testLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('validateFile', () => {
    it('should validate PDF file successfully', async () => {
      // Create a valid PDF with proper structure
      const pdfHeader = Buffer.from('%PDF-1.4\n');
      const pdfBody = Buffer.from('1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n');
      const padding = Buffer.alloc(500, 0x20); // Add some padding
      const pdfEnd = Buffer.from('%%EOF');
      
      const pdfContent = Buffer.concat([pdfHeader, pdfBody, padding, pdfEnd]);
      
      const result = await service.validateFile(
        pdfContent,
        'application/pdf',
        'test-document.pdf'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('application/pdf');
      expect(result.errors).to.be.empty;
    });

    it('should validate PNG image successfully', async () => {
      const pngBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG magic number
        ...Array(1000).fill(0x00) // Padding
      ]);
      
      const result = await service.validateFile(
        pngBuffer,
        'image/png',
        'image.png'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('image/png');
      expect(result.errors).to.be.empty;
    });

    it('should validate JPEG image successfully', async () => {
      const jpegBuffer = Buffer.from([
        0xFF, 0xD8, 0xFF, // JPEG magic number
        ...Array(1000).fill(0x00) // Padding
      ]);
      
      const result = await service.validateFile(
        jpegBuffer,
        'image/jpeg',
        'image.jpg'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('image/jpeg');
      expect(result.errors).to.be.empty;
    });

    it('should validate text file successfully', async () => {
      const textBuffer = Buffer.from('This is a plain text file content for testing purposes');
      
      const result = await service.validateFile(
        textBuffer,
        'text/plain',
        'document.txt'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('text/plain');
      expect(result.errors).to.be.empty;
    });

    it('should reject empty file', async () => {
      const emptyBuffer = Buffer.alloc(0);
      
      const result = await service.validateFile(
        emptyBuffer,
        'application/pdf',
        'empty.pdf'
      );
      
      expect(result.valid).to.be.false;
      expect(result.errors).to.have.length(2);
      expect(result.errors[0]).to.include('File content does not match declared type');
      expect(result.errors[1]).to.include('PDF file appears to be truncated or corrupted');
    });

    it('should validate large file successfully', async () => {
      // Create a valid PDF with proper structure
      const pdfHeader = Buffer.from('%PDF-1.4\n');
      const pdfContent = Buffer.from('1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n');
      const pdfMiddle = Buffer.alloc(100 * 1024 * 1024 - pdfHeader.length - pdfContent.length - 5); // Fill to make 100MB
      const pdfEnd = Buffer.from('%%EOF');
      
      const largeBuffer = Buffer.concat([pdfHeader, pdfContent, pdfMiddle, pdfEnd]);
      
      const result = await service.validateFile(
        largeBuffer,
        'application/pdf',
        'large.pdf'
      );
      
      expect(result.valid).to.be.true;
      expect(result.errors).to.be.empty;
    });

    it('should reject unsupported MIME type', async () => {
      const buffer = Buffer.from('test content');
      
      const result = await service.validateFile(
        buffer,
        'application/x-executable',
        'test.exe'
      );
      
      expect(result.valid).to.be.false;
      expect(result.errors).to.include("File type 'application/x-executable' is not allowed");
    });

    it('should reject file with mismatched magic numbers', async () => {
      const invalidBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, // PNG magic number
        ...Array(1000).fill(0x00)
      ]);
      
      const result = await service.validateFile(
        invalidBuffer,
        'application/pdf',
        'fake.pdf'
      );
      
      expect(result.valid).to.be.false;
      expect(result.errors[0]).to.include('File content does not match declared type');
    });

    it('should handle MIME type aliases correctly', async () => {
      const jpegBuffer = Buffer.from([
        0xFF, 0xD8, 0xFF,
        ...Array(1000).fill(0x00)
      ]);
      
      const result = await service.validateFile(
        jpegBuffer,
        'image/jpg', // Alias for image/jpeg
        'image.jpg'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('image/jpg'); // Keeps the original type
    });

    it('should validate DOCX file', async () => {
      const docxBuffer = Buffer.from([
        0x50, 0x4B, 0x03, 0x04, // ZIP magic number (DOCX is a ZIP)
        ...Array(1000).fill(0x00)
      ]);
      
      const result = await service.validateFile(
        docxBuffer,
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'document.docx'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal(
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      );
      expect(result.errors).to.be.empty;
    });

    it('should detect potentially malicious content', async () => {
      const maliciousContent = Buffer.from('<script>alert("xss")</script>');
      const maliciousBuffer = Buffer.concat([
        Buffer.from([0x25, 0x50, 0x44, 0x46]), // PDF magic number
        maliciousContent, // Malicious content
        Buffer.alloc(500, 0x20) // Padding
      ]);
      
      const result = await service.validateFile(
        maliciousBuffer,
        'application/pdf',
        'malicious.pdf'
      );
      
      expect(result.valid).to.be.false;
      expect(result.errors).to.have.length(2);
      expect(result.errors[0]).to.include('potentially malicious content');
      expect(result.errors[0]).to.include('script');
      expect(result.errors[1]).to.include('PDF file appears to be truncated or corrupted');
    });

    it('should validate files with warning for extension mismatch', async () => {
      // Create a valid PDF with proper structure
      const pdfHeader = Buffer.from('%PDF-1.4\n');
      const pdfBody = Buffer.from('1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n');
      const padding = Buffer.alloc(500, 0x20);
      const pdfEnd = Buffer.from('%%EOF');
      
      const pdfContent = Buffer.concat([pdfHeader, pdfBody, padding, pdfEnd]);
      
      const result = await service.validateFile(
        pdfContent,
        'application/pdf',
        'document.txt' // Wrong extension
      );

      expect(result.valid).to.be.true;
      expect(result.warnings).to.have.length.greaterThan(0);
      expect(result.warnings[0]).to.include('extension');
    });
  });

  describe('generateFileHash', () => {
    it('should generate consistent SHA-256 hash', () => {
      const buffer = Buffer.from('test content for hashing');
      
      const hash1 = service.generateFileHash(buffer);
      const hash2 = service.generateFileHash(buffer);

      expect(hash1).to.equal(hash2);
      expect(hash1).to.be.a('string');
      expect(hash1.length).to.equal(64); // SHA-256 produces 64 hex characters
    });

    it('should generate different hashes for different content', () => {
      const buffer1 = Buffer.from('content one');
      const buffer2 = Buffer.from('content two');
      
      const hash1 = service.generateFileHash(buffer1);
      const hash2 = service.generateFileHash(buffer2);

      expect(hash1).to.not.equal(hash2);
    });

    it('should handle empty buffer', () => {
      const emptyBuffer = Buffer.alloc(0);
      
      const hash = service.generateFileHash(emptyBuffer);

      expect(hash).to.be.a('string');
      expect(hash.length).to.equal(64);
      // SHA-256 of empty string is known
      expect(hash).to.equal('e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855');
    });
  });

  describe('sanitizeFilename', () => {
    it('should sanitize unsafe characters', () => {
      const unsafe = 'file<>:|?*/\\name.pdf';
      
      const sanitized = service.sanitizeFilename(unsafe);

      expect(sanitized).to.equal('file_name.pdf');
      expect(sanitized).to.not.match(/[<>:|?*\/\\]/);
    });

    it('should replace multiple underscores with single', () => {
      const filename = 'file___name____test.pdf';
      
      const sanitized = service.sanitizeFilename(filename);

      expect(sanitized).to.equal('file_name_test.pdf');
      expect(sanitized).to.not.include('__');
    });

    it('should remove leading and trailing special characters', () => {
      const filename = '...___file-name___.pdf___';
      
      const sanitized = service.sanitizeFilename(filename);

      expect(sanitized).to.equal('file-name_.pdf');
    });

    it('should limit filename length to 255 characters', () => {
      const longName = 'a'.repeat(300) + '.pdf';
      
      const sanitized = service.sanitizeFilename(longName);

      expect(sanitized.length).to.be.at.most(255);
      expect(sanitized).to.equal('a'.repeat(255));
    });

    it('should preserve allowed characters', () => {
      const validFilename = 'valid-file_name.123.pdf';
      
      const sanitized = service.sanitizeFilename(validFilename);

      expect(sanitized).to.equal(validFilename);
    });
  });

  describe('private method behaviors', () => {
    it('should handle buffer validation edge cases', async () => {
      // Test with minimal buffer that meets size requirements
      const minimalBuffer = Buffer.from('Plain text content\nMore content to make it realistic');
      
      const result = await service.validateFile(
        minimalBuffer,
        'text/plain',
        'minimal.txt'
      );

      expect(result.valid).to.be.true;
      expect(result.warnings).to.be.an('array');
    });

    it('should detect CSV files as text/csv', async () => {
      const csvBuffer = Buffer.from('name,age,city\nJohn,30,NYC\nJane,25,LA');
      
      const result = await service.validateFile(
        csvBuffer,
        'text/csv',
        'data.csv'
      );

      expect(result.valid).to.be.true;
      expect(result.detectedType).to.equal('text/csv');
    });
  });
});