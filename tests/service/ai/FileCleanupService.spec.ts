/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { FileCleanupService } from '../../../server/service/ai/FileCleanupService';
import AiUploadedFile from '../../../server/models/aiUploadedFile';
import type { FileStorageService } from '../../../server/service/ai/FileStorageService';
import type { ProviderFileManager } from '../../../server/service/ai/ProviderFileManager';
import { testLogger } from '../../factories/logger';
import { createTestDouble } from '../../utils/test-doubles';
import { getStubCallArgs } from '../../utils/sinon-helpers';

describe('FileCleanupService', () => {
  const sandbox = sinon.createSandbox();
  let service: FileCleanupService;
  let mockFileStorage: FileStorageService;
  let mockProviderFileManager: ProviderFileManager;

  // Helper to create query chain for find stubbing
  const createQueryChain = (data: any[]) => {
    const leanStub = sandbox.stub().resolves(data);
    const limitStub = sandbox.stub().returns({ lean: leanStub });
    return limitStub;
  };

  beforeEach(() => {
    mockFileStorage = createTestDouble<FileStorageService>({
      deleteFile: sandbox.stub().resolves()
    });

    mockProviderFileManager = createTestDouble<ProviderFileManager>({
      deleteFile: sandbox.stub().resolves()
    });

    service = new FileCleanupService(
      mockFileStorage,
      mockProviderFileManager,
      testLogger
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('cleanupFiles', () => {
    it('should clean up inactive files successfully', async () => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 30);
      
      const mockInactiveFiles = [
        {
          _id: new ObjectId(),
          originalName: 'old-file1.pdf',
          isActive: false,
          updated: new Date(cutoffDate.getTime() - 1000),
          fileStoragePath: 'path/to/old-file1.pdf',
          providerFiles: {},
          metadata: { size: 1024 }
        },
        {
          _id: new ObjectId(),
          originalName: 'old-file2.pdf',
          isActive: false,
          updated: new Date(cutoffDate.getTime() - 2000),
          fileStoragePath: 'path/to/old-file2.pdf',
          providerFiles: { openai: 'file-123' },
          metadata: { size: 2048 }
        }
      ];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      
      findStub
        .onFirstCall()
        .returns({ limit: createQueryChain(mockInactiveFiles) } as any)
        .onSecondCall()
        .returns({ limit: createQueryChain([]) } as any)
        .onThirdCall()
        .returns({ limit: createQueryChain([]) } as any)
        .onCall(3)
        .returns({ limit: createQueryChain([]) } as any);

      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false,
        batchSize: 10,
        inactiveDays: 30
      });

      expect(result.processedCount).to.equal(2);
      expect(result.deletedFiles).to.equal(2);
      expect(result.deletedBytes).to.equal(3072);
      expect(result.errors).to.be.empty;
      expect((mockFileStorage.deleteFile as sinon.SinonStub).callCount).to.equal(2);
      expect((mockProviderFileManager.deleteFile as sinon.SinonStub).callCount).to.equal(2);
    });

    it('should perform dry run without deleting files', async () => {
      const mockInactiveFiles = [
        {
          _id: new ObjectId(),
          originalName: 'test-file.pdf',
          isActive: false,
          updated: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
          metadata: { size: 1024 }
        }
      ];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      
      findStub
        .onFirstCall()
        .returns({ limit: createQueryChain(mockInactiveFiles) } as any)
        .onSecondCall()
        .returns({ limit: createQueryChain([]) } as any)
        .onThirdCall()
        .returns({ limit: createQueryChain([]) } as any)
        .returns({ limit: createQueryChain([]) } as any);

      const deleteOneStub = sandbox.stub(AiUploadedFile, 'deleteOne');

      const result = await service.cleanupFiles({
        dryRun: true,
        batchSize: 10,
        inactiveDays: 30
      });

      expect(result.processedCount).to.equal(1);
      expect(result.deletedFiles).to.equal(1);
      expect(result.deletedBytes).to.equal(1024);
      expect(result.errors).to.be.empty;
      expect((mockFileStorage.deleteFile as sinon.SinonStub).called).to.be.false;
      expect((mockProviderFileManager.deleteFile as sinon.SinonStub).called).to.be.false;
      expect(deleteOneStub.called).to.be.false;
    });

    it('should clean up files without provider references', async () => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);

      const mockFilesWithoutProviders = [
        {
          _id: new ObjectId(),
          originalName: 'orphaned-file.pdf',
          created: new Date(cutoffDate.getTime() - 1000),
          fileStoragePath: 'path/to/orphaned.pdf',
          providerFiles: {},
          metadata: { size: 5120 }
        }
      ];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      // First call for inactive files
      findStub.onCall(0).returns({ limit: createQueryChain([]) } as any);
      // Second call for files without providers
      findStub.onCall(1).returns({ limit: createQueryChain(mockFilesWithoutProviders) } as any);
      // Third call returns empty
      findStub.onCall(2).returns({ limit: createQueryChain([]) } as any);
      // Fourth call for expired Gemini files
      findStub.onCall(3).returns({ limit: createQueryChain([]) } as any);

      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false,
        noProviderDays: 90
      });

      expect(result.deletedFiles).to.equal(1);
      expect(result.deletedBytes).to.equal(5120);
      expect((mockFileStorage.deleteFile as sinon.SinonStub).calledWith('path/to/orphaned.pdf')).to.be.true;
    });

    it('should clean up expired Gemini provider files', async () => {
      const geminiCutoff = new Date();
      geminiCutoff.setHours(geminiCutoff.getHours() - 49); // Older than 48 hours

      const mockGeminiFiles = [
        {
          _id: new ObjectId(),
          originalName: 'gemini-file.pdf',
          providerFiles: {
            gemini: {
              fileId: 'gemini-123',
              uploadedAt: geminiCutoff
            }
          }
        }
      ];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      // Empty results for inactive and no-provider cleanup
      findStub.onCall(0).returns({ limit: createQueryChain([]) } as any);
      findStub.onCall(1).returns({ limit: createQueryChain([]) } as any);
      // Gemini files to clean up
      findStub.onCall(2).returns({ limit: createQueryChain(mockGeminiFiles) } as any);
      findStub.onCall(3).returns({ limit: createQueryChain([]) } as any);

      const updateOneStub = sandbox.stub(AiUploadedFile, 'updateOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false
      });

      expect(updateOneStub.calledOnce).to.be.true;
      const args = getStubCallArgs(updateOneStub, 0);
      expect(args).to.exist;
      expect(args![0]).to.deep.equal({ _id: mockGeminiFiles[0]._id });
      expect(args![1]).to.deep.equal({ $unset: { 'providerFiles.gemini': 1 } });
      expect(result.errors).to.be.empty;
    });

    it('should handle deletion errors gracefully', async () => {
      const mockFile = {
        _id: new ObjectId(),
        originalName: 'error-file.pdf',
        isActive: false,
        updated: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        fileStoragePath: 'path/to/error.pdf',
        metadata: { size: 1024 }
      };

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      
      // Stub all cleanup phases
      findStub
        .onFirstCall()
        .returns({ limit: createQueryChain([mockFile]) } as any)  // inactive files - has one
        .onSecondCall()
        .returns({ limit: createQueryChain([]) } as any)           // inactive files - no more
        .onThirdCall()
        .returns({ limit: createQueryChain([]) } as any)           // files without providers
        .onCall(3)
        .returns({ limit: createQueryChain([]) } as any);

      (mockFileStorage.deleteFile as sinon.SinonStub).rejects(new Error('Storage deletion failed'));
      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false,
        inactiveDays: 30
      });

      expect(result.processedCount).to.equal(1);
      expect(result.deletedFiles).to.equal(1);
      expect(result.errors).to.be.empty; // Storage errors are logged but don't prevent deletion
    });

    it('should process files in batches', async () => {
      const createMockFile = (index: number) => ({
        _id: new ObjectId(),
        originalName: `file${index}.pdf`,
        isActive: false,
        updated: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        metadata: { size: 1024 }
      });

      const batch1 = [createMockFile(1), createMockFile(2)];
      const batch2 = [createMockFile(3)];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      // First batch of inactive files
      findStub.onCall(0).returns({ limit: createQueryChain(batch1) } as any);
      // Second batch of inactive files
      findStub.onCall(1).returns({ limit: createQueryChain(batch2) } as any);
      // Empty batch to stop
      findStub.onCall(2).returns({ limit: createQueryChain([]) } as any);
      // Empty results for other cleanup types
      findStub.onCall(3).returns({ limit: createQueryChain([]) } as any);
      findStub.onCall(4).returns({ limit: createQueryChain([]) } as any);

      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false,
        batchSize: 2,
        inactiveDays: 30
      });

      expect(result.processedCount).to.equal(3);
      expect(result.deletedFiles).to.equal(3);
      expect(findStub.callCount).to.be.at.least(4); // Changed from 5 to 4 since we have 3 cleanup phases
    });

    it('should handle general cleanup errors', async () => {
      sandbox.stub(AiUploadedFile, 'find').throws(new Error('Database connection failed'));

      const result = await service.cleanupFiles();

      expect(result.errors).to.have.length(1);
      expect(result.errors[0].fileId).to.equal('general');
      expect(result.errors[0].error).to.include('Database connection failed');
      expect(result.duration).to.be.at.least(0); // Duration is set even on error
    });
  });

  describe('getCleanupStatistics', () => {
    it('should return cleanup statistics', async () => {
      sandbox.stub(AiUploadedFile, 'countDocuments')
        .onFirstCall().resolves(100)  // total files
        .onSecondCall().resolves(70)  // active files
        .onThirdCall().resolves(15);  // files without providers

      sandbox.stub(AiUploadedFile, 'aggregate').resolves([
        {
          _id: null,
          totalSize: 1048576,    // 1MB
          inactiveSize: 262144   // 256KB
        }
      ]);

      const stats = await service.getCleanupStatistics();

      expect(stats).to.deep.equal({
        totalFiles: 100,
        activeFiles: 70,
        inactiveFiles: 30,
        filesWithoutProviders: 15,
        totalSize: 1048576,
        potentialCleanupSize: 262144
      });
    });

    it('should handle empty aggregation results', async () => {
      sandbox.stub(AiUploadedFile, 'countDocuments').resolves(0);
      sandbox.stub(AiUploadedFile, 'aggregate').resolves([]);

      const stats = await service.getCleanupStatistics();

      expect(stats).to.deep.equal({
        totalFiles: 0,
        activeFiles: 0,
        inactiveFiles: 0,
        filesWithoutProviders: 0,
        totalSize: 0,
        potentialCleanupSize: 0
      });
    });
  });

  describe('edge cases', () => {
    it('should handle files without metadata size', async () => {
      const mockFile = {
        _id: new ObjectId(),
        originalName: 'no-size.pdf',
        isActive: false,
        updated: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        metadata: {} // No size property
      };

      sandbox.stub(AiUploadedFile, 'find')
        .onFirstCall()
        .returns({ limit: createQueryChain([mockFile]) } as any)
        .returns({ limit: createQueryChain([]) } as any);

      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({ dryRun: false });

      expect(result.deletedFiles).to.equal(1);
      expect(result.deletedBytes).to.equal(0); // Should handle missing size gracefully
    });

    it('should handle provider file manager deletion failure', async () => {
      const mockFile = {
        _id: new ObjectId(),
        originalName: 'provider-error.pdf',
        isActive: false,
        updated: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        providerFiles: { openai: 'file-123' },
        metadata: { size: 1024 }
      };

      sandbox.stub(AiUploadedFile, 'find')
        .onFirstCall()
        .returns({ limit: createQueryChain([mockFile]) } as any)
        .returns({ limit: createQueryChain([]) } as any);

      (mockProviderFileManager.deleteFile as sinon.SinonStub).rejects(
        new Error('Provider deletion failed')
      );
      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({ dryRun: false });

      expect(result.deletedFiles).to.equal(1);
      expect(result.errors).to.be.empty; // Should continue despite provider errors
    });

    it('should handle complex provider file queries', async () => {
      const filesWithMixedProviders = [
        {
          _id: new ObjectId(),
          originalName: 'no-providers.pdf',
          created: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000),
          providerFiles: undefined,
          metadata: { size: 1024 }
        },
        {
          _id: new ObjectId(),
          originalName: 'empty-providers.pdf',
          created: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000),
          providerFiles: {},
          metadata: { size: 2048 }
        }
      ];

      const findStub = sandbox.stub(AiUploadedFile, 'find');
      findStub.onCall(0).returns({ limit: createQueryChain([]) } as any); // inactive
      findStub.onCall(1).returns({ limit: createQueryChain(filesWithMixedProviders) } as any); // no providers
      findStub.onCall(2).returns({ limit: createQueryChain([]) } as any); // next batch
      findStub.onCall(3).returns({ limit: createQueryChain([]) } as any); // gemini

      sandbox.stub(AiUploadedFile, 'deleteOne').resolves();

      const result = await service.cleanupFiles({
        dryRun: false,
        noProviderDays: 90
      });

      expect(result.deletedFiles).to.equal(2);
      expect(result.deletedBytes).to.equal(3072);
    });
  });
});