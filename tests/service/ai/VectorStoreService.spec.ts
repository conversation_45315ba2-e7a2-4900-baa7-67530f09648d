/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { VectorStoreService } from '../../../server/service/ai/VectorStoreService';
import ContextError from '../../../server/error/ContextError';
import { createMockWithStubs, createModelMock, createServiceMock, type MockWithStubs } from '../../utils/test-doubles';
import { testLogger } from '../../factories/logger';
import type { LoggerInterface } from '../../../server/service/wwgLogger';
import type { ChatGPT } from '../../../server/service/ai/models/ChatGPT';
import VectorStore from '../../../server/models/vectorStore';
import AiUploadedFile from '../../../server/models/aiUploadedFile';
import User from '../../../server/models/user';

describe('VectorStoreService', () => {
  const sandbox = sinon.createSandbox();
  let service: VectorStoreService;
  let mockVectorStoreModel: MockWithStubs<typeof VectorStore>;
  let mockAiUploadedFileModel: MockWithStubs<typeof AiUploadedFile>;
  let mockUserModel: MockWithStubs<typeof User>;
  let mockChatGPT: MockWithStubs<ChatGPT>;

  beforeEach(() => {
    // Create typed mocks for Mongoose models
    mockVectorStoreModel = createModelMock<typeof VectorStore>({
      findOne: sandbox.stub(),
      create: sandbox.stub(),
      findOneAndUpdate: sandbox.stub(),
      find: sandbox.stub()
    });

    mockAiUploadedFileModel = createModelMock<typeof AiUploadedFile>({
      find: sandbox.stub(),
      updateMany: sandbox.stub()
    });

    mockUserModel = createModelMock<typeof User>({
      findById: sandbox.stub()
    });

    // Create service mock
    mockChatGPT = createServiceMock<ChatGPT>({
      createVectorStore: sandbox.stub(),
      addFilesToVectorStore: sandbox.stub()
    });

    // Create service instance with mocked dependencies - no casting needed!
    service = new VectorStoreService(
      testLogger,
      mockVectorStoreModel.mock,
      mockAiUploadedFileModel.mock,
      mockUserModel.mock,
      mockChatGPT.mock
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getOrCreateUserStore', () => {
    it('should return existing store if found', async () => {
      const userId = new ObjectId();
      const mockStore = {
        _id: new ObjectId(),
        vectorStoreId: 'vs_existing123',
        ownerId: userId,
        name: `AI Testing Vector Store - ${userId}`,
        purpose: 'ai_testing',
        fileCount: 5
      };

      mockVectorStoreModel.stubs.findOne.resolves(mockStore);

      const result = await service.getOrCreateUserStore(userId);

      expect(result).to.equal('vs_existing123');
      expect(mockVectorStoreModel.stubs.findOne.calledOnce).to.be.true;
      expect(mockVectorStoreModel.stubs.findOne.firstCall.args[0]).to.deep.equal({
        ownerId: userId,
        purpose: 'ai_testing'
      });
    });

    it('should create new store if not found', async () => {
      const userId = new ObjectId();
      const userName = 'Test User';

      mockVectorStoreModel.stubs.findOne.resolves(null);
      mockUserModel.stubs.findById.resolves({ name: userName });
      mockChatGPT.stubs.createVectorStore.resolves('vs_new123');
      mockVectorStoreModel.stubs.create.resolves([{
        vectorStoreId: 'vs_new123',
        ownerId: userId,
        name: `AI Testing - ${userName}`,
        purpose: 'ai_testing',
        fileCount: 0
      }]);

      const result = await service.getOrCreateUserStore(userId);

      expect(result).to.equal('vs_new123');
      expect(mockChatGPT.stubs.createVectorStore.calledOnce).to.be.true;
      expect(mockChatGPT.stubs.createVectorStore.firstCall.args[0]).to.equal(`AI Testing - ${userName}`);
      expect(mockVectorStoreModel.stubs.create.calledOnce).to.be.true;
    });

    it('should handle OpenAI API errors', async () => {
      const userId = new ObjectId();

      mockVectorStoreModel.stubs.findOne.resolves(null);
      mockUserModel.stubs.findById.resolves({ name: 'Test User' });
      mockChatGPT.stubs.createVectorStore.rejects(new Error('OpenAI API error'));

      await expect(service.getOrCreateUserStore(userId))
        .to.be.rejected;
    });
  });

  describe('addFilesToUserStore', () => {
    it('should add files to vector store successfully', async () => {
      const userId = new ObjectId();
      const fileIds = ['file1', 'file2', 'file3'];
      const vectorStoreId = 'vs_123';

      // Mock finding files that already exist in store (empty)
      mockAiUploadedFileModel.stubs.find.resolves([]);

      // Mock getting/creating vector store
      sandbox.stub(service, 'getOrCreateUserStore').resolves(vectorStoreId);

      mockChatGPT.stubs.addFilesToVectorStore.resolves();
      mockAiUploadedFileModel.stubs.updateMany.resolves({ modifiedCount: 3 });
      mockVectorStoreModel.stubs.findOneAndUpdate.resolves();

      await service.addFilesToUserStore(userId, fileIds);

      expect(mockChatGPT.stubs.addFilesToVectorStore.calledOnce).to.be.true;
      expect(mockChatGPT.stubs.addFilesToVectorStore.firstCall.args[0]).to.equal(vectorStoreId);
      expect(mockChatGPT.stubs.addFilesToVectorStore.firstCall.args[1]).to.deep.equal(fileIds);
      expect(mockAiUploadedFileModel.stubs.updateMany.calledOnce).to.be.true;
      expect(mockVectorStoreModel.stubs.findOneAndUpdate.calledOnce).to.be.true;
    });

    it('should handle empty file list', async () => {
      const userId = new ObjectId();

      // Mock getting/creating vector store
      sandbox.stub(service, 'getOrCreateUserStore').resolves('vs_123');

      mockAiUploadedFileModel.stubs.find.resolves([]);

      await service.addFilesToUserStore(userId, []);

      // Should return early without making any API calls
      expect(mockChatGPT.stubs.addFilesToVectorStore.called).to.be.false;
    });

    it('should skip files already in store', async () => {
      const userId = new ObjectId();
      const fileIds = ['file1', 'file2'];
      const vectorStoreId = 'vs_123';

      // Mock finding files that already exist in store
      const alreadyInStore = [
        { providerFiles: { openai: { fileId: 'file1', vectorStoreId } } }
      ];

      mockAiUploadedFileModel.stubs.find.resolves(alreadyInStore);
      sandbox.stub(service, 'getOrCreateUserStore').resolves(vectorStoreId);

      mockChatGPT.stubs.addFilesToVectorStore.resolves();
      mockAiUploadedFileModel.stubs.updateMany.resolves({ modifiedCount: 1 });
      mockVectorStoreModel.stubs.findOneAndUpdate.resolves();

      await service.addFilesToUserStore(userId, fileIds);

      // Should only add file2 since file1 is already in store
      expect(mockChatGPT.stubs.addFilesToVectorStore.calledOnce).to.be.true;
      expect(mockChatGPT.stubs.addFilesToVectorStore.firstCall.args[1]).to.deep.equal(['file2']);
    });

    it('should handle batch creation errors', async () => {
      const userId = new ObjectId();
      const fileIds = ['file1'];

      mockAiUploadedFileModel.stubs.find.resolves([]);
      sandbox.stub(service, 'getOrCreateUserStore').resolves('vs_123');

      mockChatGPT.stubs.addFilesToVectorStore.rejects(new Error('Batch creation failed'));

      await expect(service.addFilesToUserStore(userId, fileIds))
        .to.be.rejectedWith(ContextError, /Failed to add files to vector store/);
    });
  });
});
