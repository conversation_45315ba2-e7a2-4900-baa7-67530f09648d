import { expect } from 'chai';
import sinon from 'sinon';
import { PromptChainBuilder, type ChainStep } from '../../../server/service/ai/PromptChainBuilder';
import { type AIModel, type AIPrompt, type AIResponse } from '../../../server/service/ai/models/AIModel';
import ContextError from '../../../server/error/ContextError';

describe('PromptChainBuilder', () => {
  const sandbox = sinon.createSandbox();
  let mockModel: sinon.SinonStubbedInstance<AIModel>;

  beforeEach(() => {
    mockModel = {
      parseCompletion: sandbox.stub(),
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('constructor', () => {
    it('should initialize with an empty array of steps', () => {
      const builder = new PromptChainBuilder([]);
      expect(builder['steps']).to.deep.equal([]);
    });

    it('should initialize with the provided steps', () => {
      const step1: ChainStep = {
        model: mockModel,
        generatePrompt: async () => ({ role: 'user', content: 'prompt 1' }),
      };
      const builder = new PromptChainBuilder([step1]);
      expect(builder['steps']).to.deep.equal([step1]);
    });
  });

  describe('addStep', () => {
    it('should add a step to the chain', () => {
      const builder = new PromptChainBuilder([]);
      const step1: ChainStep = {
        model: mockModel,
        generatePrompt: async () => ({ role: 'user', content: 'prompt 1' }),
      };
      builder.addStep(step1);
      expect(builder['steps']).to.deep.equal([step1]);
    });

    it('should allow chaining of addStep calls', () => {
      const builder = new PromptChainBuilder([]);
      const step1: ChainStep = {
        model: mockModel,
        generatePrompt: async () => ({ role: 'user', content: 'prompt 1' }),
      };
      const step2: ChainStep = {
        model: mockModel,
        generatePrompt: async () => ({ role: 'user', content: 'prompt 2' }),
      };
      builder.addStep(step1).addStep(step2);
      expect(builder['steps']).to.deep.equal([step1, step2]);
    });
  });

  describe('execute', () => {
    it('should throw a ContextError if the chain is empty', async () => {
      const builder = new PromptChainBuilder([]);
      await expect(builder.execute()).to.be.rejectedWith(ContextError, /Cannot execute an empty chain/);
    });

    it('should execute a single-step chain', async () => {
      const prompt: AIPrompt = { role: 'user', content: 'test prompt' };
      const expectedResponse: AIResponse<string> = { content: 'test response' };

      const generatePromptStub = sandbox.stub().resolves(prompt);
      mockModel.parseCompletion.resolves(expectedResponse);

      const step: ChainStep = {
        model: mockModel,
        generatePrompt: generatePromptStub,
        maxTokens: 100,
      };

      const builder = new PromptChainBuilder([step]);
      const finalResponse = await builder.execute();

      expect(generatePromptStub.calledOnceWith(undefined)).to.be.true;
      expect(mockModel.parseCompletion.calledOnceWith([prompt], 100)).to.be.true;
      expect(finalResponse).to.deep.equal(expectedResponse);
    });

    it('should execute a multi-step chain, passing responses between steps', async () => {
      const prompt1: AIPrompt = { role: 'user', content: 'prompt 1' };
      const response1: AIResponse<string> = { content: 'response 1' };
      const prompt2: AIPrompt = { role: 'user', content: 'prompt 2' };
      const response2: AIResponse<string> = { content: 'response 2' };

      const generatePrompt1 = sandbox.stub().resolves(prompt1);
      const generatePrompt2 = sandbox.stub().resolves(prompt2);

      mockModel.parseCompletion.onFirstCall().resolves(response1);
      mockModel.parseCompletion.onSecondCall().resolves(response2);

      const step1: ChainStep = { model: mockModel, generatePrompt: generatePrompt1 };
      const step2: ChainStep = { model: mockModel, generatePrompt: generatePrompt2 };

      const builder = new PromptChainBuilder([step1, step2]);
      const finalResponse = await builder.execute();

      expect(generatePrompt1.calledOnceWith(undefined)).to.be.true;
      expect(mockModel.parseCompletion.calledWith([prompt1])).to.be.true;
      expect(generatePrompt2.calledOnceWith(response1)).to.be.true;
      expect(mockModel.parseCompletion.calledWith([prompt2])).to.be.true;
      expect(finalResponse).to.deep.equal(response2);
    });

    it('should stop execution if a step returns an undefined prompt', async () => {
      const prompt1: AIPrompt = { role: 'user', content: 'prompt 1' };
      const response1: AIResponse<string> = { content: 'response 1' };

      const generatePrompt1 = sandbox.stub().resolves(prompt1);
      const generatePrompt2 = sandbox.stub().resolves(undefined);

      mockModel.parseCompletion.onFirstCall().resolves(response1);

      const step1: ChainStep = { model: mockModel, generatePrompt: generatePrompt1 };
      const step2: ChainStep = { model: mockModel, generatePrompt: generatePrompt2 };

      const builder = new PromptChainBuilder([step1, step2]);
      const finalResponse = await builder.execute();

      expect(generatePrompt1.calledOnce).to.be.true;
      expect(generatePrompt2.calledOnceWith(response1)).to.be.true;
      expect(mockModel.parseCompletion.calledOnce).to.be.true; // Only called for the first step
      expect(finalResponse).to.deep.equal(response1);
    });
  });
});
