/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { createMongooseModel } from '../../setup';
import { getFirstArg, getStubCallArgs } from '../../utils/sinon-helpers';
import { createExecuteTestRequest, createExecuteTestResponse, createPopulatedAITestExecution } from '../../fixtures/aiTestingFixtures';
import { AITestingExecutionService } from '../../../server/service/ai/AITestingExecutionService';
import type { getAITestingService } from '../../../server/service/ai/AITestingService';
import type { getAITestingPromptService } from '../../../server/service/ai/AITestingPromptService';
import AiTestExecution, { TestExecutionStatus } from '../../../server/models/aiTestExecution';
import BadRequestError from '../../../server/error/BadRequestError';
import type { ExecuteTestRequest, PromptConfiguration } from '../../../server/service/ai/ai-testing-types';
import ContextError from '../../../server/error/ContextError';
import { testLogger } from '../../factories/logger';
import { createMockWithStubs, type MockWithStubs } from '../../utils/test-doubles';

describe('AITestingExecutionService', () => {
  const sandbox = sinon.createSandbox();
  let service: AITestingExecutionService;
  let mockAITestingService: MockWithStubs<ReturnType<typeof getAITestingService>>;
  let mockPromptService: MockWithStubs<ReturnType<typeof getAITestingPromptService>>;

  beforeEach(() => {
    // Create mocks for dependencies
    mockAITestingService = createMockWithStubs<ReturnType<typeof getAITestingService>>({
      executeTest: sandbox.stub(),
      improvePrompt: sandbox.stub(),
      improveCustomPrompt: sandbox.stub(),
      generatePromptFromDescription: sandbox.stub()
    });

    mockPromptService = createMockWithStubs<ReturnType<typeof getAITestingPromptService>>({
      incrementUsageCount: sandbox.stub(),
      getDefaultPrompts: sandbox.stub(),
      getPromptTemplates: sandbox.stub(),
      createPromptTemplate: sandbox.stub(),
      getPromptTemplate: sandbox.stub(),
      updatePromptTemplate: sandbox.stub(),
      deletePromptTemplate: sandbox.stub(),
      improvePromptTemplate: sandbox.stub(),
      generatePromptFromDescription: sandbox.stub(),
      improveCustomPrompt: sandbox.stub()
    });

    // Create service with mocked dependencies
    service = new AITestingExecutionService(
      mockAITestingService.mock,
      mockPromptService.mock,
      testLogger
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('parseExecuteTestRequest', () => {
    it('should parse valid execute test request', () => {
      const rawRequest = {
        testType: 'utr-matching',
        prompt: {
          templateId: new ObjectId().toString(),
          customPrompt: 'Test prompt'
        } as PromptConfiguration,
        uploadedFileIds: ['file1', 'file2'],
        utrSelection: {
          filters: {
            codes: ['UTR001', 'UTR002']
          }
        },
        options: {
          aiModel: 'claude-sonnet',
          includeExplanation: true,
          includeMatchedContent: false,
          relevanceThreshold: 0.7
        }
      };

      const result = service.parseExecuteTestRequest(rawRequest);

      expect(result.testType).to.equal('utr-matching');
      expect(result.options?.aiModel).to.equal('claude-sonnet');
      expect(result.prompt.templateId).to.equal(rawRequest.prompt.templateId);
    });

    it('should throw error for invalid request', () => {
      const invalidRequest = {
        testType: 'utr-matching',
        prompt: {},
        uploadedFileIds: [],
        options: {
          aiModel: undefined
        }
      };

      expect(() => service.parseExecuteTestRequest(invalidRequest))
        .to.throw(BadRequestError);
    });

    it('should handle missing optional fields', () => {
      const minimalRequest = {
        testType: 'utr-matching',
        prompt: {
          customPrompt: 'Test prompt'
        },
        uploadedFileIds: ['file1'],
        options: {
          aiModel: 'claude-sonnet'
        }
      };

      const result = service.parseExecuteTestRequest(minimalRequest);

      expect(result.utrSelection).to.be.undefined;
    });
  });

  describe('executeTest', () => {
    it('should execute test and save results', async () => {
      const request = createExecuteTestRequest({
        uploadedFileIds: ['file1'],
        utrSelection: {
          filters: {
            codes: ['UTR001']
          }
        }
      });

      const userId = new ObjectId().toString();
      const executionId = new ObjectId();

      // Mock AI testing service response
      const mockResponse = createExecuteTestResponse({
        results: [
          {
            utrCode: 'UTR001',
            relevanceScore: 0.85,
            explanation: 'Highly relevant'
          }
        ],
        metrics: {
          tokenUsage: {
            inputTokens: 100,
            outputTokens: 50,
            totalTokens: 150,
            cost: 0.001
          },
          cost: 0.001,
          executionTime: 500
        }
      });

      // Configure mocks
      mockAITestingService.stubs.executeTest.resolves(mockResponse);

      const saveStub = sandbox.stub(AiTestExecution.prototype, 'save');
      // Ensure the save method sets the _id on the instance
      saveStub.callsFake(function(this: { _id: ObjectId }) {
        this._id = executionId;
        return Promise.resolve(this);
      });

      const result = await service.executeTest(request, userId);

      // Wait for async execution to start
      await new Promise(resolve => setTimeout(resolve, 50));
      
      expect(mockAITestingService.stubs.executeTest.calledOnce).to.be.true;
      const callArgs = getStubCallArgs(mockAITestingService.stubs.executeTest);
      // The request now includes executionId
      expect(callArgs?.[0]).to.deep.equal({ ...request, executionId: executionId.toString() });
      expect(callArgs?.[1]).to.equal(userId);

      // Save is called once initially (async execution happens in background)
      expect(saveStub.calledOnce).to.be.true;

      expect(result).to.include({
        testId: executionId.toString(),
        status: 'running'
      });
      // Results are not available immediately
      expect(result.results).to.be.undefined;

      // Should not increment usage count when no templateId
      expect(mockPromptService.stubs.incrementUsageCount.called).to.be.false;
    });

    it('should increment prompt template usage count when templateId is provided', async () => {
      const templateId = new ObjectId().toString();
      const request = createExecuteTestRequest({
        prompt: {
          customPrompt: 'Test prompt'
        }
      });

      const userId = new ObjectId().toString();
      const executionId = new ObjectId();

      const mockResponse = createExecuteTestResponse();
      mockAITestingService.stubs.executeTest.resolves(mockResponse);

      const saveStub = sandbox.stub(AiTestExecution.prototype, 'save');
      saveStub.callsFake(function(this: { _id: ObjectId; promptTemplateId: ObjectId }) {
        this._id = executionId;
        this.promptTemplateId = new ObjectId(templateId);
        return Promise.resolve(this);
      });

      mockPromptService.stubs.incrementUsageCount.resolves();
      
      // Mock findById for async execution
      const findByIdStub = sandbox.stub(AiTestExecution, 'findById');
      findByIdStub.resolves({
        _id: executionId,
        promptTemplateId: new ObjectId(templateId),
        results: [],
        save: sandbox.stub().resolves()
      });

      const result = await service.executeTest(request, userId);
      
      // Should return immediately
      expect(result.status).to.equal('running');
      
      // Wait for async execution
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockPromptService.stubs.incrementUsageCount.calledOnce).to.be.true;
      expect(mockPromptService.stubs.incrementUsageCount.calledWith(templateId)).to.be.true;
    });

    it('should store template ID and AI model when provided', async () => {
      const templateId = new ObjectId().toString();
      const aiModel = 'gpt-5';
      const request = createExecuteTestRequest({
        uploadedFileIds: ['file1'],
        prompt: {
          customPrompt: 'Test prompt',
          templateId: templateId
        },
        options: {
          aiModel: aiModel
        }
      });

      const userId = new ObjectId().toString();
      const executionId = new ObjectId();

      const saveStub = sandbox.stub(AiTestExecution.prototype, 'save');
      saveStub.callsFake(function(this: any) {
        this._id = executionId;
        // Verify that templateId and aiModel are set
        expect(this.promptTemplateId.toString()).to.equal(templateId);
        expect(this.aiModel).to.equal(aiModel);
        return Promise.resolve(this);
      });

      const result = await service.executeTest(request, userId);

      expect(saveStub.calledOnce).to.be.true;
      expect(result.status).to.equal('running');
    });

    it('should handle execution errors', async () => {
      const request: ExecuteTestRequest = {
        testType: 'utr-matching',
        uploadedFileIds: ['file1'],
        prompt: {
          customPrompt: 'Test prompt'
        },
        options: {
          aiModel: 'claude-sonnet'
        }
      };

      // Configure mock to reject
      mockAITestingService.stubs.executeTest.rejects(new ContextError('AI service error'));

      const executionId = new ObjectId();
      const saveStub = sandbox.stub(AiTestExecution.prototype, 'save');
      saveStub.callsFake(function(this: { _id: ObjectId }) {
        this._id = executionId;
        return Promise.resolve(this);
      });
      
      const updateStub = sandbox.stub(AiTestExecution, 'findByIdAndUpdate').resolves();

      // Should not throw, returns immediately
      const result = await service.executeTest(request, 'user-id');
      expect(result.status).to.equal('running');
      
      // Wait for async execution to fail
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Should update status to failed
      expect(updateStub.calledOnce).to.be.true;
      const updateCall = updateStub.getCall(0);
      expect(updateCall.args[0]).to.equal(executionId.toString());
      expect(updateCall.args[1]).to.include({
        status: 'failed',
        error: 'AI service error'
      });
    });
  });

  describe('getTestHistory', () => {
    it('should return paginated test history', async () => {
      const userId = new ObjectId();
      const mockExecutions = [createPopulatedAITestExecution({
        createdBy: userId,
        aiModel: 'gpt-5'
      })];

      const findStub = sandbox.stub(AiTestExecution, 'find');
      findStub.returns(createMongooseModel(mockExecutions));

      sandbox.stub(AiTestExecution, 'countDocuments').resolves(1);

      const result = await service.getTestHistory({
        limit: 10,
        offset: 0
      });

      expect(result.tests).to.have.length(1);
      expect(result.total).to.equal(1);
      expect(result.tests[0].aiModel).to.equal('gpt-5');
    });

    it('should filter by status', async () => {
      const findStub = sandbox.stub(AiTestExecution, 'find').returns( createMongooseModel([]));
      const countStub = sandbox.stub(AiTestExecution, 'countDocuments').resolves(0);

      const { tests , total } = await service.getTestHistory({
        status: 'completed',
        limit: 10,
        offset: 0
      });

      expect(findStub.calledOnce).to.be.true;
      expect(countStub.calledOnce).to.be.true;
      expect(tests).to.have.length(0);
      expect(total).to.equal(0);
    });

    it('should filter by date range', async () => {
      const startDate = '2025-01-01';
      const endDate = '2025-01-31';

      const findStub = sandbox.stub(AiTestExecution, 'find').returns(createMongooseModel([]));
      const countStub = sandbox.stub(AiTestExecution, 'countDocuments').resolves(0);

      await service.getTestHistory({
        startDate,
        endDate,
        limit: 10,
        offset: 0
      });

      expect(findStub.calledOnce).to.be.true;
      expect(countStub.calledOnce).to.be.true;

      const query = getFirstArg<any>(findStub) || {};
      expect(query).to.have.property('created');
      expect(query.created.$gte).to.be.instanceOf(Date);
      expect(query.created.$lte).to.be.instanceOf(Date);
    });

    it('should filter by test type', async () => {
      const findStub = sandbox.stub(AiTestExecution, 'find');
      const queryResult = createMongooseModel([]);
      queryResult.sort = sandbox.stub().returns(queryResult);
      queryResult.limit = sandbox.stub().returns(queryResult);
      queryResult.skip = sandbox.stub().returns(queryResult);
      findStub.returns(queryResult);

      sandbox.stub(AiTestExecution, 'countDocuments').resolves(0);

      await service.getTestHistory({
        testType: 'utr-matching',
        limit: 10,
        offset: 0
      });

      expect(findStub.calledOnce).to.be.true;
      const findQuery = getFirstArg<any>(findStub) || {};
      expect(findQuery).to.deep.include({ testType: 'utr-matching' });
    });

    it('should filter by createdBy', async () => {
      const userId = new ObjectId().toString();

      const findStub = sandbox.stub(AiTestExecution, 'find');
      const queryResult = createMongooseModel([]);
      queryResult.sort = sandbox.stub().returns(queryResult);
      queryResult.limit = sandbox.stub().returns(queryResult);
      queryResult.skip = sandbox.stub().returns(queryResult);
      findStub.returns(queryResult);

      sandbox.stub(AiTestExecution, 'countDocuments').resolves(0);

      await service.getTestHistory({
        createdBy: userId,
        limit: 10,
        offset: 0
      });

      expect(findStub.calledOnce).to.be.true;
      const query = getFirstArg<any>(findStub) || {};
      expect(query.createdBy?.toString()).to.equal(userId);
    });
  });

  describe('getTestExecution', () => {
    it('should return a specific test execution', async () => {
      const executionId = new ObjectId();
      const mockExecution = createPopulatedAITestExecution({
        _id: executionId,
        results: [
          {
            utrCode: 'UTR001',
            relevanceScore: 0.9
          }
        ]
      });

      const findByIdStub = sandbox.stub(AiTestExecution, 'findById');
      findByIdStub.returns(createMongooseModel(mockExecution));

      const result = await service.getTestExecution(executionId.toString());

      // The service returns a different structure
      expect(result).to.have.property('executionId');
      expect(result).to.have.property('status', mockExecution.status);
      expect(result.details).to.have.property('testType', mockExecution.testType);
      expect(result.results).to.have.length(1);
    });

    it('should throw error if execution not found', async () => {
      const findByIdStub = sandbox.stub(AiTestExecution, 'findById');
      findByIdStub.returns(createMongooseModel(null));

      // Use a valid ObjectId
      const validId = new ObjectId().toString();
      await expect(service.getTestExecution(validId))
        .to.be.rejectedWith(BadRequestError, 'Test execution not found');
    });
  });

  describe('getTestStatus', () => {
    it('should return test status with AI model and template info', async () => {
      const executionId = new ObjectId();
      const templateId = new ObjectId();
      
      const mockExecution = {
        _id: executionId,
        status: TestExecutionStatus.Completed,
        created: new Date(),
        completedAt: new Date(),
        results: [{ utrCode: 'UTR001', relevanceScore: 0.9 }],
        aiModel: 'gpt-5',
        promptTemplateId: templateId
      };
      
      const findByIdStub = sandbox.stub(AiTestExecution, 'findById').returns(createMongooseModel(mockExecution));
      
      const result = await service.getTestStatus(executionId.toString());
      
      expect(result.testId).to.equal(executionId.toString());
      expect(result.status).to.equal(TestExecutionStatus.Completed);
      expect(result.aiModel).to.equal('gpt-5');
      expect(result.promptTemplateId).to.equal(templateId.toString());
      expect(result.results).to.have.length(1);
    });
    
    it('should handle missing AI model and template', async () => {
      const executionId = new ObjectId();
      
      const mockExecution = {
        _id: executionId,
        status: TestExecutionStatus.Running,
        created: new Date(),
        results: []
      };
      
      const findByIdStub = sandbox.stub(AiTestExecution, 'findById').returns(createMongooseModel(mockExecution));
      
      const result = await service.getTestStatus(executionId.toString());
      
      expect(result.aiModel).to.be.undefined;
      expect(result.promptTemplateId).to.be.undefined;
      expect(result.results).to.be.undefined; // Not completed yet
    });
  });

  // Skip internal method tests as they're not part of the public API
});
