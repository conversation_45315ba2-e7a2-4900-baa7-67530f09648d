/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { createMongooseModel } from '../../setup';
import { getFirstArg } from '../../utils/sinon-helpers';
import { createAIUploadedFile } from '../../fixtures/aiTestingFixtures';
import { AITestingFileService } from '../../../server/service/ai/AITestingFileService';
import AiUploadedFile from '../../../server/models/aiUploadedFile';
import BadRequestError from '../../../server/error/BadRequestError';
import { createMockWithStubs, type MockWithStubs } from '../../utils/test-doubles';
import type { ProviderFileManager } from '../../../server/service/ai/ProviderFileManager';
import type { FileStorageService } from '../../../server/service/ai/FileStorageService';
import type { AIFileHandlingService } from '../../../server/service/ai/services/AIFileHandlingService';

describe('AITestingFileService', () => {
  const sandbox = sinon.createSandbox();
  let service: AITestingFileService;
  let mockProviderFileManager: MockWithStubs<ProviderFileManager>;
  let mockFileStorageService: MockWithStubs<FileStorageService>;
  let mockFileHandlingService: MockWithStubs<AIFileHandlingService>;
  
  beforeEach(() => {
    // Create mocked dependencies
    mockProviderFileManager = createMockWithStubs<ProviderFileManager>({
      uploadFile: sandbox.stub(),
      deleteFile: sandbox.stub(),
      getProviderFile: sandbox.stub()
    });
    
    mockFileStorageService = createMockWithStubs<FileStorageService>({
      storeFile: sandbox.stub(),
      retrieveFile: sandbox.stub(),
      deleteFile: sandbox.stub()
    });
    
    mockFileHandlingService = createMockWithStubs<AIFileHandlingService>({
      createFileReference: sandbox.stub(),
      handleFileInput: sandbox.stub(),
      resolveFileReference: sandbox.stub(),
      cleanupTemporaryFiles: sandbox.stub(),
      prepareVectorStore: sandbox.stub()
    });
    
    // Create service instance with injected dependencies
    service = new AITestingFileService(
      mockProviderFileManager.mock,
      mockFileStorageService.mock,
      mockFileHandlingService.mock
    );
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  describe('uploadFile', () => {
    it('should upload file and create database record', async () => {
      const fileData = {
        buffer: Buffer.from('test content'),
        originalname: 'test-document.pdf',
        mimetype: 'application/pdf',
        size: 1024
      } as Express.Multer.File;
      
      const userId = new ObjectId().toString();
      const mongoId = new ObjectId();
      
      // Mock fileHandlingService createFileReference
      mockFileHandlingService.stubs.createFileReference.resolves({
        referenceId: mongoId.toString(),
        metadata: {
          name: 'test-document.pdf',
          size: 1024,
          type: 'application/pdf',
          uploadedAt: new Date()
        }
      });
      
      const result = await service.uploadFile(fileData, userId);
      
      expect(result).to.have.property('fileId');
      expect(result.fileId).to.equal(mongoId.toString());
      expect(result.fileName).to.equal('test-document.pdf');
      expect(result.prefixedName).to.be.a('string');
      expect(result.uploadedAt).to.be.a('string');
      
      expect(mockFileHandlingService.stubs.createFileReference.calledOnce).to.be.true;
    });
    
    it('should handle upload errors', async () => {
      const fileData = {
        buffer: Buffer.from('test'),
        originalname: 'test.pdf',
        mimetype: 'application/pdf',
        size: 1024
      } as Express.Multer.File;
      
      mockFileHandlingService.stubs.createFileReference.rejects(new Error('Upload failed'));
      
      await expect(service.uploadFile(fileData, 'user-id'))
        .to.be.rejectedWith(Error, 'Upload failed');
    });
  });
  
  describe('getUserFiles', () => {
    it('should return paginated user files', async () => {
      const userId = new ObjectId().toString();
      const mockFiles = [
        createAIUploadedFile({
          originalName: 'document1.pdf',
          prefixedName: '1234567890_document1.pdf',
          metadata: {
            size: 2048,
            mimetype: 'application/pdf',
            extension: 'pdf',
            uploadedAt: new Date()
          }
        }),
        createAIUploadedFile({
          originalName: 'spreadsheet.xlsx',
          prefixedName: '1234567891_spreadsheet.xlsx',
          metadata: {
            size: 4096,
            mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            extension: 'xlsx',
            uploadedAt: new Date()
          }
        })
      ];
      
      const findStub = sandbox.stub(AiUploadedFile, 'find');
      const queryResult = createMongooseModel(mockFiles);
      // Add missing chained methods
      queryResult.sort = sandbox.stub().returns(queryResult);
      queryResult.limit = sandbox.stub().returns(queryResult);
      queryResult.skip = sandbox.stub().returns(queryResult);
      findStub.returns(queryResult);
      
      sandbox.stub(AiUploadedFile, 'countDocuments').resolves(2);
      
      const result = await service.getUserFiles({
        userId,
        limit: 10,
        offset: 0
      });
      
      expect(result.files).to.have.length(2);
      expect(result.total).to.equal(2);
      // hasMore property might not exist in the response
      expect(result.files[0]).to.have.property('id');
      expect(result.files[0]).to.have.property('originalName', 'document1.pdf');
      expect(result.files[0]).to.have.property('providerFiles');
      expect(result.files[0].providerFiles).to.have.property('openai');
      expect(result.files[0].providerFiles.openai).to.have.property('fileId');
      expect(result.files[0].providerFiles).to.have.property('claude');
      expect(result.files[0].providerFiles.claude).to.have.property('fileId');
      expect(result.files[0].providerFiles).to.have.property('gemini');
      expect(result.files[0].providerFiles.gemini).to.have.property('fileId');
    });
    
    it('should filter by search term', async () => {
      const userId = new ObjectId();
      const findStub = sandbox.stub(AiUploadedFile, 'find');
      const queryResult = createMongooseModel([]);
      queryResult.sort = sandbox.stub().returns(queryResult);
      queryResult.limit = sandbox.stub().returns(queryResult);
      queryResult.skip = sandbox.stub().returns(queryResult);
      findStub.returns(queryResult);
      
      sandbox.stub(AiUploadedFile, 'countDocuments').resolves(0);
      
      await service.getUserFiles({
        userId: userId.toString(),
        search: 'report',
        limit: 10,
        offset: 0
      });
      
      expect(findStub.calledOnce).to.be.true;
      const queryArg = getFirstArg<any>(findStub) || {};
      expect(queryArg.uploadedBy?.toString()).to.equal(userId.toString());
      expect(queryArg).to.have.property('$or');
      expect(queryArg.$or).to.have.lengthOf(3);
      expect(queryArg.$or[0].originalName).to.deep.equal({ $regex: 'report', $options: 'i' });
    });
    
    it('should handle pagination correctly', async () => {
      const findStub = sandbox.stub(AiUploadedFile, 'find');
      const queryResult = createMongooseModel([]);
      const skipStub = sandbox.stub().returns(queryResult);
      const limitStub = sandbox.stub().returns(queryResult);
      queryResult.sort = sandbox.stub().returns(queryResult);
      queryResult.skip = skipStub;
      queryResult.limit = limitStub;
      findStub.returns(queryResult);
      
      sandbox.stub(AiUploadedFile, 'countDocuments').resolves(50);
      
      await service.getUserFiles({
        userId: 'user-id',
        limit: 20,
        offset: 20
      });
      
      expect(skipStub.calledWith(20)).to.be.true;
      expect(limitStub.calledWith(20)).to.be.true;
    });
  });
  
  describe('deleteFile', () => {
    it('should delete file from all providers and database', async () => {
      const fileId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      const mockFile = createAIUploadedFile({
        _id: new ObjectId(fileId),
        uploadedBy: new ObjectId(userId),
        originalName: 'document.pdf'
      });
      
      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFile);
      
      mockProviderFileManager.stubs.deleteFile.resolves();
      
      const result = await service.deleteFile(fileId, userId);
      
      expect(result.success).to.be.true;
      expect(result.message).to.include('deleted successfully');
      expect(mockProviderFileManager.stubs.deleteFile.calledOnce).to.be.true;
      const deleteFileArg = getFirstArg<string>(mockProviderFileManager.stubs.deleteFile);
      expect(deleteFileArg).to.equal(fileId);
    });
    
    it('should throw error if file not found', async () => {
      // Use a valid ObjectId format to pass the validation check
      const validFileId = new ObjectId().toString();
      sandbox.stub(AiUploadedFile, 'findOne').resolves(null);
      
      await expect(service.deleteFile(validFileId, 'user-id'))
        .to.be.rejectedWith(BadRequestError, 'File not found or access denied');
    });
    
    it('should throw error if user does not own the file', async () => {
      const fileId = new ObjectId().toString();
      const requestUserId = new ObjectId();
      
      // When searching with the wrong userId, it will return null
      sandbox.stub(AiUploadedFile, 'findOne').resolves(null);
      
      await expect(service.deleteFile(fileId, requestUserId.toString()))
        .to.be.rejectedWith(BadRequestError, 'File not found or access denied');
    });
  });
  
  describe('renameFile', () => {
    it('should rename file successfully', async () => {
      const fileId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      const newName = 'renamed-document.pdf';
      
      const mockFile = {
        _id: new ObjectId(fileId),
        uploadedBy: new ObjectId(userId),
        originalName: 'original.pdf',
        save: sandbox.stub().resolves(),
        toObject: function() { return this; }
      };
      
      // Return the mockFile directly as it has a save method
      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFile);
      
      await service.renameFile(fileId, newName, userId);
      
      expect(mockFile.originalName).to.equal(newName);
      expect(mockFile.save.calledOnce).to.be.true;
    });
    
    it('should throw error for invalid file name', async () => {
      await expect(service.renameFile('id', '', 'user-id'))
        .to.be.rejectedWith(BadRequestError, 'New name is required');
    });
  });
  
  describe('downloadFile', () => {
    it('should download file and return buffer with metadata', async () => {
      const fileId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      const mockFile = createAIUploadedFile({
        _id: new ObjectId(fileId),
        uploadedBy: new ObjectId(userId),
        originalName: 'download.pdf',
        fileStoragePath: 'ai-files/2025/download.pdf'
      });
      
      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFile);
      
      const fileContent = Buffer.from('file content');
      mockFileStorageService.stubs.retrieveFile.resolves(fileContent);
      
      const result = await service.downloadFile(fileId, userId);
      
      expect(result.buffer).to.deep.equal(fileContent);
      expect(result.originalName).to.equal('download.pdf');
      expect(result.mimetype).to.equal('application/pdf');
      expect(mockFileStorageService.stubs.retrieveFile.calledWith(mockFile.fileStoragePath)).to.be.true;
    });
    
    it('should handle download errors', async () => {
      const fileId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      
      // Since file is not found, it will throw before trying to retrieve from storage
      sandbox.stub(AiUploadedFile, 'findOne').resolves(null);
      
      await expect(service.downloadFile(fileId, userId))
        .to.be.rejectedWith(BadRequestError, 'File not found or access denied');
    });
  });
  
  describe('validateFileType', () => {
    it('should validate supported file types', () => {
      const supportedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv',
        'image/jpeg',
        'image/png'
      ];
      
      supportedTypes.forEach(type => {
        expect(service.validateFileType(type)).to.be.true;
      });
    });
    
    it('should reject unsupported file types', () => {
      const unsupportedTypes = [
        'application/x-executable',
        'video/mp4',
        'audio/mpeg'
      ];
      
      unsupportedTypes.forEach(type => {
        expect(service.validateFileType(type)).to.be.false;
      });
    });
  });
  
  describe('getSupportedMimeTypes', () => {
    it('should return list of supported MIME types', () => {
      const types = service.getSupportedMimeTypes();
      
      expect(types).to.be.an('array');
      expect(types).to.include('application/pdf');
      expect(types).to.include('text/plain');
      expect(types).to.include('image/jpeg');
    });
  });
  
  // Skip getFileByReference tests as method doesn't exist in service
});