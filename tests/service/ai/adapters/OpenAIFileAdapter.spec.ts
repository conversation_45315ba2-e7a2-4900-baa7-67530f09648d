/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import { OpenAIFileAdapter } from '../../../../server/service/ai/adapters/OpenAIFileAdapter';
import { testLogger } from '../../../factories/logger';
import { createTestDouble } from '../../../utils/test-doubles';
import ContextError from '../../../../server/error/ContextError';
import type { ChatGPT } from '../../../../server/service/ai/models/ChatGPT';
import type { FileObject } from 'openai/resources/files';
import { AiProvider } from '../../../../server/service/ai/types';

describe('OpenAIFileAdapter', () => {
  const sandbox = sinon.createSandbox();
  let adapter: OpenAIFileAdapter;
  let mockAIModel: ChatGPT;
  let createFileStub: sinon.SinonStub;
  let deleteFileStub: sinon.SinonStub;
  let retrieveFileStub: sinon.SinonStub;

  beforeEach(() => {
    createFileStub = sandbox.stub();
    deleteFileStub = sandbox.stub();
    retrieveFileStub = sandbox.stub();

    mockAIModel = createTestDouble<ChatGPT>({
      createFile: createFileStub,
      deleteFile: deleteFileStub,
      retrieveFile: retrieveFileStub,
      getProvider: sandbox.stub().returns(AiProvider.OpenAi)
    });

    adapter = new OpenAIFileAdapter(mockAIModel, testLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('uploadFile', () => {
    const testFileData = {
      buffer: Buffer.from('test content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024
    };

    it('should upload file to OpenAI successfully', async () => {
      const mockFileObject: FileObject = {
        id: 'file-abc123',
        object: 'file',
        bytes: 1024,
        created_at: **********,
        filename: 'test.pdf',
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      createFileStub.resolves(mockFileObject);

      const result = await adapter.uploadFile(testFileData);

      expect(createFileStub.calledOnce).to.be.true;
      const callArgs = createFileStub.firstCall.args[0];
      expect(callArgs.purpose).to.equal('assistants');
      expect(callArgs.file).to.be.instanceOf(File);

      expect(result).to.have.property('fileId', 'file-abc123');
      expect(result).to.have.property('uploadedAt');
      expect(result.uploadedAt).to.be.instanceOf(Date);
      expect(result.expiresAt).to.be.undefined;
    });

    it('should convert buffer to File object correctly', async () => {
      const mockFileObject: FileObject = {
        id: 'file-123',
        object: 'file',
        bytes: 1024,
        created_at: **********,
        filename: 'test.pdf',
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      createFileStub.resolves(mockFileObject);

      await adapter.uploadFile(testFileData);

      const callArgs = createFileStub.firstCall.args[0];
      const file = callArgs.file as File;
      
      expect(file).to.be.instanceOf(File);
      expect(file.name).to.equal('test.pdf');
      expect(file.type).to.equal('application/pdf');
    });

    it('should handle upload errors', async () => {
      createFileStub.rejects(new Error('Upload failed'));

      await expect(
        adapter.uploadFile(testFileData)
      ).to.be.rejectedWith(Error, 'Upload failed');
    });
  });

  describe('deleteFile', () => {
    it('should delete file from OpenAI successfully', async () => {
      deleteFileStub.resolves();

      await adapter.deleteFile('file-123');

      expect(deleteFileStub.calledOnce).to.be.true;
      expect(deleteFileStub.firstCall.args[0]).to.equal('file-123');
    });

    it('should handle 404 errors gracefully', async () => {
      const error404 = new Error('File not found');
      (error404 as any).status = 404;
      deleteFileStub.rejects(error404);

      // Should not throw
      await adapter.deleteFile('file-123');

      expect(deleteFileStub.calledOnce).to.be.true;
    });

    it('should handle file_not_found error code', async () => {
      const error = new Error('File not found');
      (error as any).code = 'file_not_found';
      deleteFileStub.rejects(error);

      // Should not throw
      await adapter.deleteFile('file-123');

      expect(deleteFileStub.calledOnce).to.be.true;
    });

    it('should throw ContextError for non-404 errors', async () => {
      deleteFileStub.rejects(new Error('Server error'));

      await expect(
        adapter.deleteFile('file-123')
      ).to.be.rejectedWith(ContextError, 'OpenAI file deletion failed');
    });
  });

  describe('fileExists', () => {
    it('should return true if file exists', async () => {
      const mockFileInfo = {
        id: 'file-123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date(),
        expiresAt: undefined
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('file-123');

      expect(exists).to.be.true;
    });

    it('should return false if file does not exist', async () => {
      sandbox.stub(adapter, 'getFileInfo').resolves(null);

      const exists = await adapter.fileExists('file-123');

      expect(exists).to.be.false;
    });

    it('should return false on error', async () => {
      sandbox.stub(adapter, 'getFileInfo').rejects(new Error('API error'));

      const exists = await adapter.fileExists('file-123');

      expect(exists).to.be.false;
    });
  });

  describe('getFileInfo', () => {
    it('should retrieve file info when retrieveFile is available', async () => {
      const mockFileObject: FileObject = {
        id: 'file-123',
        object: 'file',
        bytes: 2048,
        created_at: **********,
        filename: 'report.pdf',
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      retrieveFileStub.resolves(mockFileObject);

      const result = await adapter.getFileInfo('file-123');

      expect(retrieveFileStub.calledOnce).to.be.true;
      expect(retrieveFileStub.firstCall.args[0]).to.equal('file-123');

      expect(result).to.deep.equal({
        id: 'file-123',
        name: 'report.pdf',
        size: 2048,
        createdAt: new Date(********** * 1000),
        expiresAt: undefined
      });
    });

    it('should handle missing filename gracefully', async () => {
      const mockFileObject: FileObject = {
        id: 'file-123',
        object: 'file',
        bytes: 0,
        created_at: **********,
        filename: undefined as any, // Missing filename
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      retrieveFileStub.resolves(mockFileObject);

      const result = await adapter.getFileInfo('file-123');

      expect(result).to.have.property('name', 'unknown');
    });

    it('should return null for non-existent file', async () => {
      retrieveFileStub.resolves(null);

      const result = await adapter.getFileInfo('file-123');

      expect(result).to.be.null;
    });

    it('should return fallback when retrieveFile is not implemented', async () => {
      // Remove retrieveFile method
      delete (mockAIModel as any).retrieveFile;

      const result = await adapter.getFileInfo('file-123');

      expect(result).to.deep.equal({
        id: 'file-123',
        name: 'unknown',
        size: 0,
        createdAt: result?.createdAt, // Use actual date from result
        expiresAt: undefined
      });
      expect(result?.createdAt).to.be.instanceOf(Date);
    });

    it('should return null for 404 errors', async () => {
      const error404 = new Error('File not found');
      (error404 as any).status = 404;
      retrieveFileStub.rejects(error404);

      const result = await adapter.getFileInfo('file-123');

      expect(result).to.be.null;
    });

    it('should throw non-404 errors', async () => {
      retrieveFileStub.rejects(new Error('Server error'));

      await expect(
        adapter.getFileInfo('file-123')
      ).to.be.rejectedWith(Error, 'Server error');
    });
  });

  describe('is404Error', () => {
    it('should detect various 404 error formats', async () => {
      // Test by triggering different 404 scenarios through deleteFile
      const test404Scenarios = [
        { status: 404 },
        { code: 'file_not_found' },
        { message: 'File not found' },
        { message: 'Error 404: Resource missing' },
        { error: { code: 'file_not_found' } }
      ];

      for (const errorObj of test404Scenarios) {
        const error = new Error('Test error');
        Object.assign(error, errorObj);
        deleteFileStub.rejects(error);

        // Should not throw for 404 errors
        await adapter.deleteFile('file-test');
        
        deleteFileStub.reset();
      }

      // All scenarios should have been handled as 404
      expect(deleteFileStub.callCount).to.equal(0); // Reset after each call
    });

    it('should not detect non-404 errors as 404', async () => {
      const nonFile404Errors = [
        { status: 500 },
        { code: 'internal_error' },
        { message: 'Server error' },
        new Error('Random error')
      ];

      for (const errorObj of nonFile404Errors) {
        const error = errorObj instanceof Error ? errorObj : Object.assign(new Error('Test'), errorObj);
        deleteFileStub.rejects(error);

        await expect(
          adapter.deleteFile('file-test')
        ).to.be.rejectedWith(ContextError);
        
        deleteFileStub.reset();
      }
    });
  });
});