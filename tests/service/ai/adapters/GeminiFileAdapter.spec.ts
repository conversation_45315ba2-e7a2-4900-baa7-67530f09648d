/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { GeminiFileAdapter } from '../../../../server/service/ai/adapters/GeminiFileAdapter';
import { testLogger } from '../../../factories/logger';
import { createTestDouble } from '../../../utils/test-doubles';
import type { GeminiAI } from '../../../../server/service/ai/models/GeminiAI';
import type { FileObject } from 'openai/resources/files';

describe('GeminiFileAdapter', () => {
  const sandbox = sinon.createSandbox();
  let adapter: GeminiFileAdapter;
  let mockAIModel: GeminiAI;
  let createFileStub: sinon.SinonStub;
  let retrieveFileStub: sinon.SinonStub;

  beforeEach(() => {
    createFileStub = sandbox.stub();
    retrieveFileStub = sandbox.stub();

    mockAIModel = createTestDouble<GeminiAI>({
      createFile: createFileStub,
      retrieveFile: retrieveFileStub
    });

    adapter = new GeminiFileAdapter(mockAIModel, testLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('uploadFile', () => {
    const testFileData = {
      buffer: Buffer.from('test content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024
    };

    it('should upload file to Gemini successfully', async () => {
      const mockFileResponse = {
        id: 'gemini_abc123',
        filename: 'test.pdf',
        bytes: 1024,
        created_at: Math.floor(Date.now() / 1000),
        purpose: 'assistants' as const,
        object: 'file' as const,
        status: 'processed' as const,
        status_details: undefined
      };

      createFileStub.resolves(mockFileResponse);

      const result = await adapter.uploadFile(testFileData);

      expect(createFileStub.calledOnce).to.be.true;
      const callArgs = createFileStub.firstCall.args[0];
      expect(callArgs.file).to.be.instanceOf(File);

      expect(result).to.have.property('fileId', 'gemini_abc123');
      expect(result).to.have.property('uploadedAt');
      expect(result.uploadedAt).to.be.instanceOf(Date);
      expect(result.expiresAt).to.be.undefined;
    });

    it('should convert buffer to File object correctly', async () => {
      const mockFileResponse = {
        id: 'gemini_123',
        filename: 'test.pdf'
      };

      createFileStub.resolves(mockFileResponse);

      await adapter.uploadFile(testFileData);

      const callArgs = createFileStub.firstCall.args[0];
      const file = callArgs.file as File;
      
      expect(file).to.be.instanceOf(File);
      expect(file.name).to.equal('test.pdf');
      expect(file.type).to.equal('application/pdf');
    });

    it('should handle various mime types', async () => {
      const mimeTypes = [
        'image/jpeg',
        'image/png',
        'text/plain',
        'application/json',
        'video/mp4'
      ];

      for (const mimetype of mimeTypes) {
        const fileData = { ...testFileData, mimetype };
        const mockResponse = { id: `gemini_${mimetype.replace('/', '_')}` };
        
        createFileStub.resolves(mockResponse);
        
        await adapter.uploadFile(fileData);
        
        const file = createFileStub.lastCall.args[0].file as File;
        expect(file.type).to.equal(mimetype);
      }
    });

    it('should handle upload errors', async () => {
      createFileStub.rejects(new Error('Upload failed'));

      await expect(
        adapter.uploadFile(testFileData)
      ).to.be.rejectedWith(Error, 'Upload failed');
    });
  });

  describe('deleteFile', () => {
    it('should log that Gemini does not support deletion', async () => {
      // Should not throw - Gemini doesn't support deletion
      await adapter.deleteFile('gemini_123');

      // Verify no actual deletion attempt was made
      expect(createFileStub.called).to.be.false;
      expect(retrieveFileStub.called).to.be.false;
    });

    it('should handle multiple delete calls gracefully', async () => {
      // Should not throw for any file ID
      await adapter.deleteFile('gemini_123');
      await adapter.deleteFile('gemini_456');
      await adapter.deleteFile('invalid-id');

      // All should complete without error
      expect(true).to.be.true;
    });
  });

  describe('fileExists', () => {
    it('should return true for ACTIVE files', async () => {
      const mockFileInfo = {
        id: 'gemini_123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date(),
        state: 'ACTIVE'
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.true;
    });

    it('should return true for PROCESSING files', async () => {
      const mockFileInfo = {
        id: 'gemini_123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date(),
        state: 'PROCESSING'
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.true;
    });

    it('should return false for files in other states', async () => {
      const mockFileInfo = {
        id: 'gemini_123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date(),
        state: 'FAILED'
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.false;
    });

    it('should return true for files without state', async () => {
      const mockFileInfo = {
        id: 'gemini_123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date()
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.true;
    });

    it('should return false if file does not exist', async () => {
      sandbox.stub(adapter, 'getFileInfo').resolves(null);

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.false;
    });

    it('should return false on error', async () => {
      sandbox.stub(adapter, 'getFileInfo').rejects(new Error('API error'));

      const exists = await adapter.fileExists('gemini_123');

      expect(exists).to.be.false;
    });
  });

  describe('getFileInfo', () => {
    it('should retrieve file info when retrieveFile is available', async () => {
      const mockFileObject: FileObject = {
        id: 'gemini_123',
        object: 'file',
        bytes: 2048,
        created_at: 1704067200,
        filename: 'report.pdf',
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      retrieveFileStub.resolves(mockFileObject);

      const result = await adapter.getFileInfo('gemini_123');

      expect(retrieveFileStub.calledOnce).to.be.true;
      expect(retrieveFileStub.firstCall.args[0]).to.equal('gemini_123');

      expect(result).to.deep.equal({
        id: 'gemini_123',
        name: 'report.pdf',
        size: 2048,
        createdAt: new Date(1704067200 * 1000),
        expiresAt: undefined,
        state: 'ACTIVE'
      });
    });

    it('should handle file with state property', async () => {
      const mockFileObject = {
        id: 'gemini_123',
        object: 'file' as const,
        bytes: 2048,
        created_at: 1704067200,
        filename: 'report.pdf',
        purpose: 'assistants' as const,
        status: 'processed' as const,
        state: 'PROCESSING',
        status_details: undefined
      };

      retrieveFileStub.resolves(mockFileObject);

      const result = await adapter.getFileInfo('gemini_123');

      expect(result?.state).to.equal('PROCESSING');
    });

    it('should handle missing filename gracefully', async () => {
      const mockFileObject: FileObject = {
        id: 'gemini_123',
        object: 'file',
        bytes: 0,
        created_at: 1704067200,
        filename: undefined as any,
        purpose: 'assistants',
        status: 'processed',
        status_details: undefined
      };

      retrieveFileStub.resolves(mockFileObject);

      const result = await adapter.getFileInfo('gemini_123');

      expect(result?.name).to.equal('unknown');
    });

    it('should return null for invalid Gemini file ID format', async () => {
      const invalidIds = [
        'file_123',      // Wrong prefix
        'gemini-123',    // Wrong separator
        'GEMINI_123',    // Wrong case
        '',              // Empty
        null,            // Null
        undefined        // Undefined
      ];

      for (const fileId of invalidIds) {
        const result = await adapter.getFileInfo(fileId as any);
        expect(result).to.be.null;
      }

      // retrieveFile should not be called for invalid IDs
      expect(retrieveFileStub.called).to.be.false;
    });

    it('should return null when retrieveFile returns null', async () => {
      retrieveFileStub.resolves(null);

      const result = await adapter.getFileInfo('gemini_123');

      expect(result).to.be.null;
    });

    it('should return null when retrieveFile method is not available', async () => {
      delete (mockAIModel as any).retrieveFile;

      const result = await adapter.getFileInfo('gemini_123');

      expect(result).to.be.null;
    });

    it('should return null when retrieveFile throws error', async () => {
      retrieveFileStub.rejects(new Error('API error'));

      const result = await adapter.getFileInfo('gemini_123');

      expect(result).to.be.null;
    });

    it('should return null for 404 errors', async () => {
      const error404 = new Error('File not found');
      (error404 as any).status = 404;
      retrieveFileStub.rejects(error404);

      const result = await adapter.getFileInfo('gemini_123');

      expect(result).to.be.null;
    });
  });

  describe('is404Error', () => {
    it('should detect various 404 error formats', async () => {
      const test404Scenarios = [
        { status: 404 },
        { code: 'file_not_found' },
        { message: 'File not found' },
        { message: 'Error 404: Resource missing' }
      ];

      for (const errorObj of test404Scenarios) {
        const error = new Error('Test error');
        Object.assign(error, errorObj);
        
        // Test through getFileInfo which uses is404Error
        retrieveFileStub.rejects(error);
        
        const result = await adapter.getFileInfo('gemini_123');
        expect(result).to.be.null; // Should return null for 404
        
        retrieveFileStub.reset();
      }
    });

    it('should not detect non-404 errors as 404', async () => {
      const nonFile404Errors = [
        { status: 500 },
        { code: 'internal_error' },
        { message: 'Server error' }
      ];

      for (const errorObj of nonFile404Errors) {
        const error = Object.assign(new Error('Test'), errorObj);
        
        // Create a new stub that throws the error in a nested try/catch
        sandbox.stub(adapter, 'getFileInfo').callsFake(async () => {
          try {
            throw error;
          } catch (e) {
            // This simulates the is404Error check
            if ((adapter as any).is404Error(e)) {
              return null;
            }
            throw e;
          }
        });

        await expect(
          adapter.getFileInfo('gemini_123')
        ).to.be.rejectedWith(Error);
        
        sandbox.restore();
        adapter = new GeminiFileAdapter(mockAIModel, testLogger);
      }
    });
  });
});