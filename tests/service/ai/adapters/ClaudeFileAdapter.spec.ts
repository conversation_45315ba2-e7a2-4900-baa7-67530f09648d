/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { ClaudeFileAdapter } from '../../../../server/service/ai/adapters/ClaudeFileAdapter';
import { testLogger } from '../../../factories/logger';
import { createTestDouble } from '../../../utils/test-doubles';
import type { ClaudeAI } from '../../../../server/service/ai/models/ClaudeAI';

describe('ClaudeFileAdapter', () => {
  const sandbox = sinon.createSandbox();
  let adapter: ClaudeFileAdapter;
  let mockAIModel: ClaudeAI;
  let createFileStub: sinon.SinonStub;
  let deleteFileStub: sinon.SinonStub;

  beforeEach(() => {
    createFileStub = sandbox.stub();
    deleteFileStub = sandbox.stub();

    mockAIModel = createTestDouble<ClaudeAI>({
      createFile: createFileStub,
      deleteFile: deleteFileStub
    });

    adapter = new ClaudeFileAdapter(mockAIModel, testLogger);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('uploadFile', () => {
    const testFileData = {
      buffer: Buffer.from('test content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024
    };

    it('should upload file to Claude successfully', async () => {
      const mockFileResponse = {
        id: 'file_abc123',
        filename: 'test.pdf',
        purpose: 'assistants',
        created_at: Math.floor(Date.now() / 1000)
      };

      createFileStub.resolves(mockFileResponse);

      const result = await adapter.uploadFile(testFileData);

      expect(createFileStub.calledOnce).to.be.true;
      const callArgs = createFileStub.firstCall.args[0];
      expect(callArgs.file).to.be.instanceOf(File);

      expect(result).to.have.property('fileId', 'file_abc123');
      expect(result).to.have.property('uploadedAt');
      expect(result.uploadedAt).to.be.instanceOf(Date);
      expect(result.expiresAt).to.be.undefined;
    });

    it('should convert JSON mimetype to text/plain for Claude', async () => {
      const jsonFileData = {
        ...testFileData,
        originalname: 'data.json',
        mimetype: 'application/json'
      };

      const mockFileResponse = {
        id: 'file_json123',
        filename: 'data.json',
        purpose: 'assistants',
        created_at: Math.floor(Date.now() / 1000)
      };

      createFileStub.resolves(mockFileResponse);

      await adapter.uploadFile(jsonFileData);

      const callArgs = createFileStub.firstCall.args[0];
      const file = callArgs.file as File;
      
      expect(file).to.be.instanceOf(File);
      expect(file.type).to.equal('text/plain'); // Converted from application/json
      expect(file.name).to.equal('data.json');
    });

    it('should preserve non-JSON mimetypes', async () => {
      const pdfFileData = {
        ...testFileData,
        mimetype: 'application/pdf'
      };

      const mockFileResponse = {
        id: 'file_pdf123',
        filename: 'test.pdf'
      };

      createFileStub.resolves(mockFileResponse);

      await adapter.uploadFile(pdfFileData);

      const callArgs = createFileStub.firstCall.args[0];
      const file = callArgs.file as File;
      
      expect(file.type).to.equal('application/pdf'); // Not converted
    });

    it('should handle upload errors', async () => {
      createFileStub.rejects(new Error('Upload failed'));

      await expect(
        adapter.uploadFile(testFileData)
      ).to.be.rejectedWith(Error, 'Upload failed');
    });
  });

  describe('deleteFile', () => {
    it('should attempt to delete file when deleteFile method exists', async () => {
      deleteFileStub.resolves();

      await adapter.deleteFile('file_123');

      expect(deleteFileStub.calledOnce).to.be.true;
      expect(deleteFileStub.firstCall.args[0]).to.equal('file_123');
    });

    it('should handle 404 errors gracefully when deleting', async () => {
      const error404 = new Error('File not found');
      (error404 as any).status = 404;
      deleteFileStub.rejects(error404);

      // Should not throw
      await adapter.deleteFile('file_123');

      expect(deleteFileStub.calledOnce).to.be.true;
    });

    it('should handle file_not_found error code', async () => {
      const error = new Error('File not found');
      (error as any).code = 'file_not_found';
      deleteFileStub.rejects(error);

      // Should not throw
      await adapter.deleteFile('file_123');
    });

    it('should handle not_found_error type', async () => {
      const error = new Error('File not found');
      (error as any).type = 'not_found_error';
      deleteFileStub.rejects(error);

      // Should not throw
      await adapter.deleteFile('file_123');
    });

    it('should propagate non-404 errors', async () => {
      deleteFileStub.rejects(new Error('Server error'));

      await expect(
        adapter.deleteFile('file_123')
      ).to.be.rejectedWith(Error, 'Server error');
    });

    it('should handle missing deleteFile method gracefully', async () => {
      // Remove deleteFile method
      delete (mockAIModel as any).deleteFile;

      // Should not throw - Claude doesn't support deletion
      await adapter.deleteFile('file_123');

      // Verify deleteFileStub was not called since method doesn't exist
      expect(deleteFileStub.called).to.be.false;
    });
  });

  describe('fileExists', () => {
    it('should return true if file exists', async () => {
      const mockFileInfo = {
        id: 'file_123',
        name: 'test.pdf',
        size: 1024,
        createdAt: new Date(),
        expiresAt: undefined
      };

      sandbox.stub(adapter, 'getFileInfo').resolves(mockFileInfo);

      const exists = await adapter.fileExists('file_123');

      expect(exists).to.be.true;
    });

    it('should return false if file does not exist', async () => {
      sandbox.stub(adapter, 'getFileInfo').resolves(null);

      const exists = await adapter.fileExists('file_123');

      expect(exists).to.be.false;
    });

    it('should return false on error', async () => {
      sandbox.stub(adapter, 'getFileInfo').rejects(new Error('API error'));

      const exists = await adapter.fileExists('file_123');

      expect(exists).to.be.false;
    });
  });

  describe('getFileInfo', () => {
    it('should return file info for valid Claude file ID', async () => {
      const result = await adapter.getFileInfo('file_abc123');

      expect(result).to.deep.include({
        id: 'file_abc123',
        name: 'unknown',
        size: 0
      });
      expect(result?.createdAt).to.be.instanceOf(Date);
      expect(result?.expiresAt).to.be.undefined;
    });

    it('should return null for invalid file ID format', async () => {
      const result = await adapter.getFileInfo('invalid-id');

      expect(result).to.be.null;
    });

    it('should return null for empty file ID', async () => {
      const result = await adapter.getFileInfo('');

      expect(result).to.be.null;
    });

    it('should handle file IDs with correct prefix', async () => {
      const validIds = ['file_123', 'file_abc', 'file_xyz123'];

      for (const fileId of validIds) {
        const result = await adapter.getFileInfo(fileId);
        expect(result).to.not.be.null;
        expect(result?.id).to.equal(fileId);
      }
    });

    it('should reject file IDs with incorrect prefix', async () => {
      const invalidIds = ['files_123', 'fil_123', 'FILE_123', '123_file'];

      for (const fileId of invalidIds) {
        const result = await adapter.getFileInfo(fileId);
        expect(result).to.be.null;
      }
    });
  });

  describe('is404Error', () => {
    it('should detect various 404 error formats', async () => {
      // Test by triggering different 404 scenarios through deleteFile
      const test404Scenarios = [
        { status: 404 },
        { code: 'file_not_found' },
        { type: 'not_found_error' },
        { message: 'File not found' },
        { message: 'Error 404: Resource missing' },
        { error: { code: 'file_not_found' } },
        { error: { type: 'not_found_error' } }
      ];

      for (const errorObj of test404Scenarios) {
        const error = new Error('Test error');
        Object.assign(error, errorObj);
        deleteFileStub.rejects(error);

        // Should not throw for 404 errors
        await adapter.deleteFile('file-test');
        
        deleteFileStub.reset();
      }

      // All scenarios should have been handled as 404
      expect(deleteFileStub.callCount).to.equal(0); // Reset after each call
    });

    it('should not detect non-404 errors as 404', async () => {
      const nonFile404Errors = [
        { status: 500 },
        { code: 'internal_error' },
        { type: 'server_error' },
        { message: 'Server error' },
        new Error('Random error')
      ];

      for (const errorObj of nonFile404Errors) {
        const error = errorObj instanceof Error ? errorObj : Object.assign(new Error('Test'), errorObj);
        deleteFileStub.rejects(error);

        await expect(
          adapter.deleteFile('file-test')
        ).to.be.rejectedWith(Error);
        
        deleteFileStub.reset();
      }
    });
  });
});