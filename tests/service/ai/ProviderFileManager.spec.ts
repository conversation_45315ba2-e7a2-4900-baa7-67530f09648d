/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import { Types } from 'mongoose';
import { ProviderFileManager, getProviderFileManager } from '../../../server/service/ai/ProviderFileManager';
import type { AdapterFactory } from '../../../server/service/ai/adapters/AdapterFactory';
import AiUploadedFile from '../../../server/models/aiUploadedFile';
import { testLogger } from '../../factories/logger';
import { createTestDouble } from '../../utils/test-doubles';
import UserError from '../../../server/error/UserError';
import ContextError from '../../../server/error/ContextError';
import type { FileStorageService } from '../../../server/service/ai/FileStorageService';
import type { FileProviderAdapter } from '../../../server/service/ai/adapters/FileProviderAdapter';
import type { AiUploadedFileModel } from '../../../server/models/aiUploadedFile';
import { AiProvider } from '../../../server/service/ai/types';

// Type for partial file record during save
type PartialFileRecord = Partial<AiUploadedFileModel> & {
  _id?: Types.ObjectId;
  metadata?: {
    extension?: string;
  };
  prefixedName?: string;
};

describe('ProviderFileManager', () => {
  const sandbox = sinon.createSandbox();
  let manager: ProviderFileManager;
  let mockFileStorage: FileStorageService;
  let mockAdapterFactory: AdapterFactory;
  let storeFileStub: sinon.SinonStub;
  let retrieveFileStub: sinon.SinonStub;
  let deleteFileStub: sinon.SinonStub;
  let getSignedUrlStub: sinon.SinonStub;
  let createAdapterStub: sinon.SinonStub;

  const testUserId = new Types.ObjectId();
  const testFileId = new Types.ObjectId();
  const testBuffer = Buffer.from('test file content');

  beforeEach(() => {
    storeFileStub = sandbox.stub();
    retrieveFileStub = sandbox.stub();
    deleteFileStub = sandbox.stub();
    getSignedUrlStub = sandbox.stub();
    createAdapterStub = sandbox.stub();

    mockFileStorage = createTestDouble<FileStorageService>({
      storeFile: storeFileStub,
      retrieveFile: retrieveFileStub,
      deleteFile: deleteFileStub,
      getSignedUrl: getSignedUrlStub
    });

    mockAdapterFactory = {
      createAdapter: createAdapterStub
    };

    manager = new ProviderFileManager(mockFileStorage, testLogger, mockAdapterFactory);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('uploadFile', () => {
    const testFileData = {
      buffer: testBuffer,
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024
    };

    it('should upload file and create database record', async () => {
      const storagePath = 'uploads/123/test.pdf';
      storeFileStub.resolves({ storagePath });

      sandbox.stub(AiUploadedFile.prototype, 'save').callsFake(function(this: PartialFileRecord) {
        this._id = testFileId;
        return Promise.resolve(this);
      });

      const result = await manager.uploadFile(testFileData, testUserId, {
        description: 'Test file',
        tags: ['test', 'pdf']
      });

      expect(storeFileStub.calledOnce).to.be.true;
      expect(storeFileStub.firstCall.args[1]).to.equal(testUserId.toString());

      expect(result).to.have.property('mongoId');
      expect(result.mongoId.toString()).to.equal(testFileId.toString());
    });

    it('should handle file extension extraction', async () => {
      const testFiles = [
        { name: 'document.pdf', expectedExt: '.pdf' },
        { name: 'file.with.dots.txt', expectedExt: '.txt' },
        { name: 'noextension', expectedExt: '' },
        { name: 'UPPERCASE.PDF', expectedExt: '.pdf' }
      ];

      for (const testFile of testFiles) {
        const fileData = { ...testFileData, originalname: testFile.name };
        
        storeFileStub.resolves({ storagePath: 'test/path' });
        
        let savedRecord: PartialFileRecord | undefined;
        sandbox.stub(AiUploadedFile.prototype, 'save').callsFake(function(this: PartialFileRecord) {
          savedRecord = this;
          return Promise.resolve(this);
        });

        await manager.uploadFile(fileData, testUserId);

        expect(savedRecord?.metadata?.extension).to.equal(testFile.expectedExt);
        
        sandbox.restore();
      }
    });

    it('should create prefixed name with timestamp', async () => {
      const clockStub = sandbox.useFakeTimers(1234567890000);
      
      storeFileStub.resolves({ storagePath: 'test/path' });
      
      let savedRecord: PartialFileRecord | undefined;
      sandbox.stub(AiUploadedFile.prototype, 'save').callsFake(function(this: PartialFileRecord) {
        savedRecord = this;
        return Promise.resolve(this);
      });

      await manager.uploadFile(testFileData, testUserId);

      expect(savedRecord?.prefixedName).to.equal('1234567890000_test.pdf');
      
      clockStub.restore();
    });
  });

  describe('getProviderFile', () => {
    it('should return existing provider file if it exists and is valid', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        providerFiles: {
          openai: {
            fileId: 'file-123',
            uploadedAt: new Date()
          }
        }
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      // Mock adapter creation and file existence check
      const mockAdapter = createTestDouble<FileProviderAdapter>({
        fileExists: sandbox.stub().resolves(true)
      });
      
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(mockAdapter);

      const result = await manager.getProviderFile(testFileId, AiProvider.OpenAi);

      expect(result).to.equal('file-123');
    });

    it('should re-upload file if provider file does not exist', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf',
        originalName: 'test.pdf',
        metadata: {
          mimetype: 'application/pdf',
          size: 1024
        },
        providerFiles: {},
        save: sandbox.stub().resolves()
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      retrieveFileStub.resolves(testBuffer);

      const mockAdapter = createTestDouble<FileProviderAdapter>({
        fileExists: sandbox.stub().resolves(false),
        uploadFile: sandbox.stub().resolves({
          fileId: 'new-file-123',
          uploadedAt: new Date()
        })
      });
      
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(mockAdapter);

      const result = await manager.getProviderFile(testFileId, AiProvider.OpenAi);

      expect(result).to.equal('new-file-123');
      expect((mockFileRecord.save as sinon.SinonStub).calledOnce).to.be.true;
    });

    it('should throw error if autoReupload is false and file not available', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        providerFiles: {}
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      await expect(
        manager.getProviderFile(testFileId, AiProvider.OpenAi, { autoReupload: false })
      ).to.be.rejectedWith(UserError, 'File not available for provider: openai');
    });

    it('should handle invalid provider', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        providerFiles: {}
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      
      // Mock factory to throw error for invalid provider
      createAdapterStub.withArgs('invalid' as AiProvider, testLogger)
        .throws(new UserError('Provider not supported: invalid'));

      await expect(
        manager.getProviderFile(testFileId, 'invalid' as AiProvider)
      ).to.be.rejectedWith(UserError, 'Provider not supported: invalid');
    });

    it('should cleanup on upload failure', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf',
        originalName: 'test.pdf',
        metadata: {
          mimetype: 'application/pdf',
          size: 1024
        },
        providerFiles: {},
        save: sandbox.stub().rejects(new Error('Save failed'))
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      retrieveFileStub.resolves(testBuffer);

      const deleteFileStub = sandbox.stub().resolves();
      const mockAdapter = createTestDouble<FileProviderAdapter>({
        uploadFile: sandbox.stub().resolves({
          fileId: 'new-file-123',
          uploadedAt: new Date()
        }),
        deleteFile: deleteFileStub
      });
      
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(mockAdapter);

      await expect(
        manager.getProviderFile(testFileId, AiProvider.OpenAi)
      ).to.be.rejectedWith(ContextError, 'Failed to upload file to openai');

      // Verify cleanup was attempted
      expect(deleteFileStub.calledOnce).to.be.true;
      expect(deleteFileStub.firstCall.args[0]).to.equal('new-file-123');
    });
  });

  describe('deleteFile', () => {
    it('should delete file from all providers and storage', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf',
        providerFiles: {
          openai: { fileId: 'file-123', uploadedAt: new Date() },
          claude: { fileId: 'claude-456', uploadedAt: new Date() }
        },
        isActive: true,
        save: sandbox.stub().resolves()
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      deleteFileStub.resolves();

      const openaiDeleteStub = sandbox.stub().resolves();
      const claudeDeleteStub = sandbox.stub().resolves();
      
      const openaiAdapter = createTestDouble<FileProviderAdapter>({
        deleteFile: openaiDeleteStub
      });
      const claudeAdapter = createTestDouble<FileProviderAdapter>({
        deleteFile: claudeDeleteStub
      });
      
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(openaiAdapter);
      createAdapterStub.withArgs(AiProvider.Claude, testLogger).returns(claudeAdapter);

      await manager.deleteFile(testFileId);

      // Verify provider deletions
      expect(openaiDeleteStub.calledOnce).to.be.true;
      expect(openaiDeleteStub.firstCall.args[0]).to.equal('file-123');
      expect(claudeDeleteStub.calledOnce).to.be.true;
      expect(claudeDeleteStub.firstCall.args[0]).to.equal('claude-456');

      // Verify storage deletion
      expect(deleteFileStub.calledOnce).to.be.true;
      expect(deleteFileStub.firstCall.args[0]).to.equal('uploads/test.pdf');

      // Verify record marked as inactive
      expect(mockFileRecord.isActive).to.be.false;
      expect((mockFileRecord.save as sinon.SinonStub).calledOnce).to.be.true;
    });

    it('should continue cleanup even if some deletions fail', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf',
        providerFiles: {
          openai: { fileId: 'file-123', uploadedAt: new Date() },
          claude: { fileId: 'claude-456', uploadedAt: new Date() }
        },
        isActive: true,
        save: sandbox.stub().resolves()
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      
      // Make storage deletion fail
      deleteFileStub.rejects(new Error('Storage deletion failed'));

      const openaiDeleteStub = sandbox.stub().rejects(new Error('OpenAI deletion failed'));
      const claudeDeleteStub = sandbox.stub().resolves();
      
      const openaiAdapter = createTestDouble<FileProviderAdapter>({
        deleteFile: openaiDeleteStub
      });
      const claudeAdapter = createTestDouble<FileProviderAdapter>({
        deleteFile: claudeDeleteStub
      });
      
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(openaiAdapter);
      createAdapterStub.withArgs(AiProvider.Claude, testLogger).returns(claudeAdapter);

      await manager.deleteFile(testFileId);

      // Verify all deletions were attempted
      expect(openaiDeleteStub.calledOnce).to.be.true;
      expect(claudeDeleteStub.calledOnce).to.be.true;
      expect(deleteFileStub.calledOnce).to.be.true;

      // Verify record still marked as inactive despite errors
      expect(mockFileRecord.isActive).to.be.false;
      expect((mockFileRecord.save as sinon.SinonStub).calledOnce).to.be.true;
    });

    it('should handle missing fileStoragePath', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: undefined,
        providerFiles: {},
        isActive: true,
        save: sandbox.stub().resolves()
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      await manager.deleteFile(testFileId);

      // Storage deletion should not be attempted
      expect(deleteFileStub.called).to.be.false;

      // Record should still be marked inactive
      expect(mockFileRecord.isActive).to.be.false;
      expect((mockFileRecord.save as sinon.SinonStub).calledOnce).to.be.true;
    });
  });

  describe('getFileContent', () => {
    it('should retrieve file content from storage', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf'
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      retrieveFileStub.resolves(testBuffer);

      const result = await manager.getFileContent(testFileId);

      expect(retrieveFileStub.calledOnce).to.be.true;
      expect(retrieveFileStub.firstCall.args[0]).to.equal('uploads/test.pdf');
      expect(result).to.equal(testBuffer);
    });

    it('should throw error if fileStoragePath not available', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: undefined
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      await expect(
        manager.getFileContent(testFileId)
      ).to.be.rejectedWith(UserError, 'File storage path not available');
    });
  });

  describe('getFileSignedUrl', () => {
    it('should get signed URL from storage', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf'
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      const expectedUrl = 'https://storage.example.com/signed-url';
      getSignedUrlStub.resolves(expectedUrl);

      const result = await manager.getFileSignedUrl(testFileId, 30);

      expect(getSignedUrlStub.calledOnce).to.be.true;
      expect(getSignedUrlStub.firstCall.args[0]).to.equal('uploads/test.pdf');
      expect(getSignedUrlStub.firstCall.args[1]).to.equal(30);
      expect(result).to.equal(expectedUrl);
    });

    it('should use default expiration if not provided', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: 'uploads/test.pdf'
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);
      getSignedUrlStub.resolves('https://example.com/url');

      await manager.getFileSignedUrl(testFileId);

      expect(getSignedUrlStub.firstCall.args[1]).to.equal(60);
    });

    it('should throw error if fileStoragePath not available', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        fileStoragePath: undefined
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      await expect(
        manager.getFileSignedUrl(testFileId)
      ).to.be.rejectedWith(UserError, 'File storage path not available');
    });
  });

  describe('getAdapter', () => {
    it('should create and cache adapters', async () => {
      const mockFileRecord = createTestDouble<AiUploadedFileModel>({  
        _id: testFileId,
        providerFiles: {
          openai: { fileId: 'file-123', uploadedAt: new Date() }
        }
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      const mockAdapter = createTestDouble<FileProviderAdapter>({
        fileExists: sandbox.stub().resolves(true)
      });
      createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).returns(mockAdapter);

      // Make two calls that will use the same adapter
      await manager.getProviderFile(testFileId, AiProvider.OpenAi);
      await manager.getProviderFile(testFileId, AiProvider.OpenAi);

      // Verify factory was called only once due to caching
      expect(createAdapterStub.withArgs(AiProvider.OpenAi, testLogger).calledOnce).to.be.true;
    });

    it('should throw error for unsupported provider through factory', async () => {
      createAdapterStub.withArgs('unsupported' as AiProvider, testLogger)
        .throws(new UserError('Provider not supported: unsupported'));

      const mockFileRecord = createTestDouble<AiUploadedFileModel>({
        _id: testFileId,
        providerFiles: {}
      });

      sandbox.stub(AiUploadedFile, 'findOne').resolves(mockFileRecord);

      await expect(
        manager.getProviderFile(testFileId, 'unsupported' as AiProvider)
      ).to.be.rejectedWith(UserError, 'Provider not supported: unsupported');
    });
  });

  describe('getProviderFileManager', () => {
    it('should return singleton instance', () => {
      const instance1 = getProviderFileManager();
      const instance2 = getProviderFileManager();

      expect(instance1).to.be.instanceOf(ProviderFileManager);
      expect(instance1).to.equal(instance2);
    });
  });
});