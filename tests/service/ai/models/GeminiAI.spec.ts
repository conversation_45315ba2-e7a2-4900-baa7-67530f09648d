/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import * as sinon from 'sinon';
import { GeminiAI, getGeminiAI, GEMINI_MODELS } from '../../../../server/service/ai/models/GeminiAI';
import { testLogger } from '../../../factories/logger';
import { createTestDouble } from '../../../utils/test-doubles';
import UserError from '../../../../server/error/UserError';
import ContextError from '../../../../server/error/ContextError';
import type { GoogleGenAI } from '@google/genai';
import type { AIPrompt } from '../../../../server/service/ai/models/UnifiedAIModel';
import type { Uploadable } from 'openai/uploads';
import type { ZodType } from 'zod';

describe('GeminiAI', () => {
  const sandbox = sinon.createSandbox();
  let geminiAI: GeminiAI;
  let mockClient: GoogleGenAI;
  let mockGenerateContent: sinon.SinonStub;

  beforeEach(() => {
    // Create mock for Google Generative AI client
    mockGenerateContent = sandbox.stub().resolves({
      text: 'Test response from Gemini'
    });

    mockClient = createTestDouble<GoogleGenAI>({
      models: {
        generateContent: mockGenerateContent
      }
    });

    // Create GeminiAI instance with test logger and injected client
    geminiAI = new GeminiAI(testLogger, mockClient);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getProvider', () => {
    it('should return gemini as provider', () => {
      expect(geminiAI.getProvider()).to.equal('gemini');
    });
  });

  describe('supportsModel', () => {
    it('should return true for supported Gemini models', () => {
      expect(geminiAI.supportsModel('gemini-2.5-pro')).to.be.true;
      expect(geminiAI.supportsModel('gemini-2.5-flash')).to.be.true;
    });

    it('should return false for unsupported models', () => {
      expect(geminiAI.supportsModel('gemini-1.0')).to.be.false;
      expect(geminiAI.supportsModel('gpt-4')).to.be.false;
      expect(geminiAI.supportsModel('unknown-model')).to.be.false;
    });
  });

  describe('getSupportedModels', () => {
    it('should return list of supported Gemini models', () => {
      const models = geminiAI.getSupportedModels();
      expect(models).to.be.an('array');
      expect(models).to.have.members(['gemini-2.5-pro', 'gemini-2.5-flash']);
    });
  });

  describe('getModelVersion', () => {
    it('should return default model version', () => {
      expect(geminiAI.getModelVersion()).to.equal('gemini-2.5-flash');
    });
  });

  describe('supportsAssistants', () => {
    it('should return false as Gemini does not support assistants', () => {
      expect(geminiAI.supportsAssistants()).to.be.false;
    });
  });

  describe('runCompletion', () => {
    const testMessages: AIPrompt[] = [
      { role: 'system', content: 'You are a helpful assistant' },
      { role: 'user', content: 'Hello' }
    ];

    it('should run completion with default model', async () => {
      const result = await geminiAI.runCompletion(testMessages);

      expect(result).to.deep.include({
        content: 'Test response from Gemini'
      });
      expect(result.usage).to.exist;
      if (result.usage) {
        expect(result.usage.prompt_tokens).to.be.a('number');
        expect(result.usage.completion_tokens).to.be.a('number');
        expect(result.usage.total_tokens).to.be.a('number');
      }
    });

    it('should run completion with specified model', async () => {
      const result = await geminiAI.runCompletion('gemini-2.5-pro', testMessages);

      expect(mockGenerateContent.calledOnce).to.be.true;
      const callArgs = mockGenerateContent.firstCall.args[0];
      expect(callArgs.model).to.equal('gemini-2.5-pro');
      expect(result.content).to.equal('Test response from Gemini');
    });

    it('should respect maxTokens option', async () => {
      await geminiAI.runCompletion('gemini-2.5-pro', testMessages, { maxTokens: 1000 });

      const callArgs = mockGenerateContent.firstCall.args[0];
      expect(callArgs.config.maxOutputTokens).to.equal(1000);
    });

    it('should respect temperature option', async () => {
      await geminiAI.runCompletion('gemini-2.5-pro', testMessages, { temperature: 0.5 });

      const callArgs = mockGenerateContent.firstCall.args[0];
      expect(callArgs.config.temperature).to.equal(0.5);
    });

    it('should throw UserError for unsupported model', async () => {
      await expect(
        geminiAI.runCompletion('unsupported-model', testMessages)
      ).to.be.rejectedWith(UserError, 'Unsupported Gemini model');
    });

    it('should handle API errors gracefully', async () => {
      mockGenerateContent.rejects(new Error('API error'));

      await expect(
        geminiAI.runCompletion(testMessages)
      ).to.be.rejectedWith(UserError, 'Unable to communicate with Gemini AI');
    });
  });

  describe('parseCompletion', () => {
    const testMessages: AIPrompt[] = [
      { role: 'user', content: 'Return JSON' }
    ];

    it('should parse JSON response successfully', async () => {
      mockGenerateContent.resolves({
        text: '{"key": "value", "number": 42}'
      });

      const result = await geminiAI.parseCompletion<{ key: string; number: number }>(testMessages);

      expect(result.content).to.deep.equal({
        key: 'value',
        number: 42
      });
    });

    it('should throw ContextError for invalid JSON', async () => {
      mockGenerateContent.resolves({
        text: 'This is not JSON'
      });

      await expect(
        geminiAI.parseCompletion(testMessages)
      ).to.be.rejectedWith(ContextError, 'Failed to parse Gemini response as JSON');
    });

    it('should support unified interface with model parameter', async () => {
      mockGenerateContent.resolves({
        text: '{"result": true}'
      });

      const result = await geminiAI.parseCompletion(
        'gemini-2.5-pro',
        testMessages,
        { maxTokens: 500 }
      );

      expect(result.content).to.deep.equal({ result: true });
    });
  });

  describe('file operations', () => {
    describe('createFile', () => {
      it('should create file and return OpenAI-compatible format', async () => {
        const uploadStub = sandbox.stub(geminiAI, 'uploadFile').resolves({
          id: 'file-123',
          name: 'test.txt',
          size: 1024,
          createdAt: new Date('2025-01-01')
        });

        const testFile = createTestDouble<Uploadable>({
          name: 'test.txt',
          size: 12,
          type: 'text/plain'
        });

        const result = await geminiAI.createFile({
          file: testFile,
          purpose: 'assistants'
        });

        expect(uploadStub.calledOnce).to.be.true;
        expect(result).to.include({
          id: 'file-123',
          bytes: 1024,
          filename: 'test.txt',
          object: 'file',
          purpose: 'assistants',
          status: 'processed'
        });
      });
    });

    describe('deleteFile', () => {
      it('should remove file reference and return success', async () => {
        const result = await geminiAI.deleteFile('file-123');

        expect(result).to.deep.equal({
          id: 'file-123',
          object: 'file',
          deleted: true
        });
      });
    });

    describe('retrieveFile', () => {
      it('should return file info if exists', async () => {
        sandbox.stub(geminiAI, 'fileExists').resolves(true);

        const result = await geminiAI.retrieveFile('file-123');

        expect(result).to.include({
          id: 'file-123',
          object: 'file',
          purpose: 'assistants',
          status: 'processed'
        });
      });

      it('should return null if file does not exist', async () => {
        sandbox.stub(geminiAI, 'fileExists').resolves(false);

        const result = await geminiAI.retrieveFile('file-123');

        expect(result).to.be.null;
      });
    });
  });

  describe('assistant operations', () => {
    describe('createAssistant', () => {
      it('should return mock assistant as Gemini does not support assistants', async () => {
        const result = await geminiAI.createAssistant({
          name: 'Test Assistant',
          instructions: 'Be helpful',
          tools: []
        });

        expect(result).to.include({
          id: 'gemini-no-assistant',
          object: 'assistant',
          name: 'Test Assistant',
          model: 'gemini-2.5-flash',
          instructions: 'Be helpful'
        });
      });
    });

    describe('deleteAssistant', () => {
      it('should return mock deletion response', async () => {
        const result = await geminiAI.deleteAssistant('asst-123');

        expect(result).to.deep.equal({
          id: 'asst-123',
          object: 'assistant.deleted',
          deleted: true
        });
      });
    });

    describe('runThreadWithAssistant', () => {
      it('should delegate to executeWithFiles', async () => {
        const executeStub = sandbox.stub(geminiAI, 'executeWithFiles').resolves({
          result: 'test'
        });

        await geminiAI.runThreadWithAssistant({
          assistantId: 'asst-123',
          message: {
            role: 'user',
            content: 'Test message',
            attachments: [{ file_id: 'file-123' }]
          },
          jsonSchema: createTestDouble<ZodType>({})
        });

        expect(executeStub.calledOnce).to.be.true;
        expect(executeStub.firstCall.args[0]).to.deep.equal({
          prompt: 'Test message',
          files: [{ fileId: 'file-123' }],
          jsonSchema: {}
        });
      });
    });
  });

  describe('model configuration', () => {
    it('should have correct token limits for models', () => {
      expect(GEMINI_MODELS['gemini-2.5-pro']).to.deep.equal({
        tokenLimit: 1048576,
        outputLimit: 65536
      });
      expect(GEMINI_MODELS['gemini-2.5-flash']).to.deep.equal({
        tokenLimit: 1048576,
        outputLimit: 65536
      });
    });
  });

  describe('getGeminiAI', () => {
    it('should return singleton instance', () => {
      const instance1 = getGeminiAI();
      const instance2 = getGeminiAI();
      
      expect(instance1).to.be.instanceOf(GeminiAI);
      expect(instance1).to.equal(instance2);
    });
  });
});