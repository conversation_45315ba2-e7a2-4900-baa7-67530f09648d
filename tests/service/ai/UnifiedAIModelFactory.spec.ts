/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import {
  UnifiedAIModelFactory,
  UNIFIED_AI_MODELS,
  type UnifiedModelName,
  type AIModelGetters
} from '../../../server/service/ai/UnifiedAIModelFactory';
import { testLogger } from '../../factories/logger';
import { createTestDouble } from '../../utils/test-doubles';
import type { ChatGPT } from '../../../server/service/ai/models/ChatGPT';
import type { ClaudeAI } from '../../../server/service/ai/models/ClaudeAI';
import type { GeminiAI } from '../../../server/service/ai/models/GeminiAI';
import ContextError from '../../../server/error/ContextError';


describe('UnifiedAIModelFactory', () => {
  const sandbox = sinon.createSandbox();
  let factory: UnifiedAIModelFactory;
  let mockChatGPT: ChatGPT;
  let mockClaudeAI: ClaudeAI;
  let mockGeminiAI: GeminiAI;
  let mockGetters: AIModelGetters;

  beforeEach(() => {
    // Create mocks for AI models with minimal implementation
    mockChatGPT = createTestDouble<ChatGPT>({
      runCompletion: sandbox.stub().resolves({ content: 'ChatGPT response' }),
      parseCompletion: sandbox.stub().resolves({}),
      getModelVersion: sandbox.stub().returns('gpt-4')
    });

    mockClaudeAI = createTestDouble<ClaudeAI>({
      runCompletion: sandbox.stub().resolves({ content: 'Claude response' }),
      parseCompletion: sandbox.stub().resolves({}),
      getModelVersion: sandbox.stub().returns('claude-3-5-sonnet')
    });

    mockGeminiAI = createTestDouble<GeminiAI>({
      runCompletion: sandbox.stub().resolves({ content: 'Gemini response' }),
      parseCompletion: sandbox.stub().resolves({}),
      getModelVersion: sandbox.stub().returns('gemini-2.5-pro'),
      supportsModel: sandbox.stub().returns(true)
    });

    // Create mock getters that return our test doubles
    mockGetters = {
      getChatGPT: () => mockChatGPT,
      getClaudeAI: () => mockClaudeAI,
      getGeminiAI: () => mockGeminiAI
    };

    factory = new UnifiedAIModelFactory(testLogger, mockGetters);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getModel', () => {
    it('should return ChatGPT instance for OpenAI models', () => {
      const model = factory.getModel('gpt-4');
      expect(model).to.equal(mockChatGPT);
    });

    it('should return Claude instance for Claude models', () => {
      const model = factory.getModel('claude-3-5-sonnet-latest');
      expect(model).to.equal(mockClaudeAI);
    });

    it('should return Gemini instance for Gemini models', () => {
      const model = factory.getModel('gemini-2.5-pro');
      expect(model).to.equal(mockGeminiAI);
    });

    it('should default to ChatGPT for unknown models', () => {
      const model = factory.getModel('unknown-model');
      expect(model).to.equal(mockChatGPT);
    });

    it('should cache instances and reuse them', () => {
      const model1 = factory.getModel('gpt-4');
      const model2 = factory.getModel('gpt-4o');

      expect(model1).to.equal(model2);
    });

    it('should handle all configured models', () => {
      const modelNames = Object.keys(UNIFIED_AI_MODELS) as UnifiedModelName[];

      modelNames.forEach(modelName => {
        const model = factory.getModel(modelName);
        expect(model).to.exist;

        const config = UNIFIED_AI_MODELS[modelName];
        switch (config.provider) {
          case 'openai':
            expect(model).to.equal(mockChatGPT);
            break;
          case 'claude':
            expect(model).to.equal(mockClaudeAI);
            break;
          case 'gemini':
            expect(model).to.equal(mockGeminiAI);
            break;
        }
      });
    });
  });

  describe('getFileSupportModel', () => {
    it('should return ChatGPT for OpenAI models', () => {
      const model = factory.getFileSupportModel('gpt-4');
      expect(model).to.equal(mockChatGPT);
    });

    it('should return Claude for Claude models', () => {
      const model = factory.getFileSupportModel('claude-3-5-sonnet-latest');
      expect(model).to.equal(mockClaudeAI);
    });

    it('should return Gemini for Gemini models', () => {
      const model = factory.getFileSupportModel('gemini-2.5-pro');
      expect(model).to.equal(mockGeminiAI);
    });

    it('should throw error for models that do not support file operations', () => {
      // Override getModelCapabilities to return false for file support
      sandbox.stub(factory, 'getModelCapabilities').returns({
        supportsFileOperations: false,
        supportsStructuredOutput: false,
        maxInputTokens: 32000,
        maxOutputTokens: 4096
      });

      expect(() => factory.getFileSupportModel('unsupported-model'))
        .to.throw(ContextError, 'does not support file operations');
    });

    it('should throw error for unknown models', () => {
      expect(() => factory.getFileSupportModel('unknown-model'))
        .to.throw(ContextError, 'Unknown model "unknown-model"');
    });
  });

  describe('getModelCapabilities', () => {
    it('should return OpenAI capabilities for GPT models', () => {
      const capabilities = factory.getModelCapabilities('gpt-4');

      expect(capabilities).to.deep.include({
        supportsFileOperations: true,
        supportsStructuredOutput: true,
        maxInputTokens: 128000,
        maxOutputTokens: 4096
      });
      expect(capabilities.costPerMillionTokens).to.exist;
    });

    it('should return Claude 3.5 capabilities', () => {
      const capabilities = factory.getModelCapabilities('claude-3-5-sonnet-latest');

      expect(capabilities).to.deep.include({
        supportsFileOperations: true,
        supportsStructuredOutput: true,
        maxInputTokens: 200000,
        maxOutputTokens: 8192
      });
      expect(capabilities.costPerMillionTokens).to.exist;
      expect(capabilities.costPerMillionTokens!.input).to.equal(3);
    });

    it('should return Claude 4 capabilities', () => {
      const capabilities = factory.getModelCapabilities('claude-sonnet-4-20250514');

      expect(capabilities).to.deep.include({
        supportsFileOperations: true,
        supportsStructuredOutput: true,
        maxInputTokens: 200000,
        maxOutputTokens: 10000 // Claude 4 has 10K output
      });
      expect(capabilities.costPerMillionTokens!.input).to.equal(3);
    });

    it('should return different costs for Opus vs Sonnet', () => {
      const opusCapabilities = factory.getModelCapabilities('claude-opus-4-20250514');
      const sonnetCapabilities = factory.getModelCapabilities('claude-sonnet-4-20250514');

      expect(opusCapabilities.costPerMillionTokens!.input).to.equal(15);
      expect(sonnetCapabilities.costPerMillionTokens!.input).to.equal(3);
    });

    it('should return Haiku capabilities with lower cost', () => {
      const capabilities = factory.getModelCapabilities('claude-3-5-haiku-latest');

      expect(capabilities.costPerMillionTokens!.input).to.equal(0.25);
      expect(capabilities.costPerMillionTokens!.output).to.equal(1.25);
    });

    it('should return Gemini capabilities', () => {
      const capabilities = factory.getModelCapabilities('gemini-2.5-pro');

      expect(capabilities).to.deep.include({
        supportsFileOperations: true,
        supportsStructuredOutput: true,
        maxInputTokens: 1048576, // 1M context when supported
        maxOutputTokens: 65536
      });
    });

    it('should handle Gemini models that are not supported', () => {
      (mockGeminiAI.supportsModel as sinon.SinonStub).returns(false);

      const capabilities = factory.getModelCapabilities('gemini-2.5-flash');

      expect(capabilities.maxInputTokens).to.equal(128000); // Fallback values
      expect(capabilities.maxOutputTokens).to.equal(8192);
    });

    it('should return default capabilities for unknown models', () => {
      const capabilities = factory.getModelCapabilities('unknown-model');

      expect(capabilities).to.deep.include({
        supportsFileOperations: true, // Defaults to OpenAI
        supportsStructuredOutput: true,
        maxInputTokens: 128000,
        maxOutputTokens: 4096
      });
    });

    it('should throw error for unsupported Claude models', () => {
      // First override UNIFIED_AI_MODELS to return claude provider for this test
      const originalModels = { ...UNIFIED_AI_MODELS };
      (UNIFIED_AI_MODELS as any)['claude-3-opus'] = { provider: 'claude', model: 'claude-3-opus' };

      expect(() => factory.getModelCapabilities('claude-3-opus'))
        .to.throw(ContextError, 'Only Claude 3.5 latest models and Claude 4 are supported');

      // Restore original models
      delete (UNIFIED_AI_MODELS as any)['claude-3-opus'];
    });
  });

  describe('getSupportedModels', () => {
    it('should return all configured models with capabilities', () => {
      const models = factory.getSupportedModels();

      expect(models).to.be.an('array');
      expect(models.length).to.equal(Object.keys(UNIFIED_AI_MODELS).length);

      models.forEach(model => {
        // Check for required keys (baseSystemPrompt and specialPrefixes are optional)
        expect(model).to.include.keys('name', 'provider', 'capabilities');
        expect(model.capabilities).to.include.keys(
          'supportsFileOperations',
          'supportsStructuredOutput',
          'maxInputTokens',
          'maxOutputTokens'
        );
        // costPerMillionTokens is optional
        if (model.capabilities.costPerMillionTokens) {
          expect(model.capabilities.costPerMillionTokens).to.have.all.keys('input', 'output');
        }
      });
    });

    it('should include correct providers for each model', () => {
      const models = factory.getSupportedModels();

      const openaiModels = models.filter(m => m.provider === 'openai');
      const claudeModels = models.filter(m => m.provider === 'claude');
      const geminiModels = models.filter(m => m.provider === 'gemini');

      expect(openaiModels.length).to.be.greaterThan(0);
      expect(claudeModels.length).to.be.greaterThan(0);
      expect(geminiModels.length).to.be.greaterThan(0);
    });
  });

  describe('getModelsByCapability', () => {
    it('should return models that support file operations', () => {
      const models = factory.getModelsByCapability('supportsFileOperations', true);

      expect(models).to.be.an('array');
      expect(models).to.include('gpt-4');
      expect(models).to.include('claude-3-5-sonnet-latest');
      expect(models).to.include('gemini-2.5-pro');
    });

    it('should return models that support structured output', () => {
      const models = factory.getModelsByCapability('supportsStructuredOutput', true);

      expect(models).to.be.an('array');
      expect(models.length).to.be.greaterThan(0);
    });

    it('should return models by max input tokens', () => {
      const highContextModels = factory.getModelsByCapability('maxInputTokens', 200000);

      expect(highContextModels).to.include('claude-3-5-sonnet-latest');
      expect(highContextModels).to.include('claude-sonnet-4-20250514');
    });

    it('should handle default value parameter', () => {
      const models = factory.getModelsByCapability('supportsFileOperations');
      const modelsExplicit = factory.getModelsByCapability('supportsFileOperations', true);

      expect(models).to.deep.equal(modelsExplicit);
    });

    it('should return empty array when no models match', () => {
      // Override all capabilities to return false
      sandbox.stub(factory, 'getModelCapabilities').returns({
        supportsFileOperations: false,
        supportsStructuredOutput: false,
        maxInputTokens: 1000,
        maxOutputTokens: 100
      });

      const models = factory.getModelsByCapability('supportsFileOperations', true);
      expect(models).to.be.an('array').that.is.empty;
    });
  });

  describe('model configuration', () => {
    it('should have correct model mappings', () => {
      expect(UNIFIED_AI_MODELS['gpt-4']).to.deep.equal({
        provider: 'openai',
        model: 'gpt-4',
        cost: { input: 30.00, output: 60.00 }
      });

      expect(UNIFIED_AI_MODELS['claude-3-5-sonnet-latest']).to.deep.equal({
        provider: 'claude',
        model: 'claude-3-5-sonnet-20241022',
        cost: { input: 3.00, output: 15.00 }
      });

      expect(UNIFIED_AI_MODELS['gemini-2.5-pro']).to.deep.equal({
        provider: 'gemini',
        model: 'gemini-2.5-pro',
        cost: { input: 1.25, output: 10.00 }
      });
    });

    it('should include all expected OpenAI models', () => {
      const openaiModels = ['gpt-5', 'gpt-5-mini', 'gpt-5-nano', 'gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-4.1-2025-04-14'];

      openaiModels.forEach(model => {
        expect(UNIFIED_AI_MODELS).to.have.property(model);
        expect(UNIFIED_AI_MODELS[model as UnifiedModelName].provider).to.equal('openai');
      });
    });

    it('should include all expected Claude models', () => {
      const claudeModels = [
        'claude-3-5-sonnet-latest',
        'claude-3-5-haiku-latest',
        'claude-sonnet-4-20250514',
        'claude-opus-4-20250514'
      ];

      claudeModels.forEach(model => {
        expect(UNIFIED_AI_MODELS).to.have.property(model);
        expect(UNIFIED_AI_MODELS[model as UnifiedModelName].provider).to.equal('claude');
      });
    });
  });
});
