/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import sinon from 'sinon';
import { FileStorageService } from '../../../server/service/ai/FileStorageService';
import type { FileValidationService } from '../../../server/service/ai/FileValidationService';
import BadRequestError from '../../../server/error/BadRequestError';
import { createMockWithStubs, type MockWithStubs } from '../../utils/test-doubles';
import { testLogger } from '../../factories/logger';
import type { FileStorageInterface } from '../../../server/service/storage/fileStorage';

describe('FileStorageService', () => {
  let fileStorageService: FileStorageService;
  let mockStorage: MockWithStubs<Pick<FileStorageInterface, 'streamBufferUpload' | 'downloadFile' | 'remove' | 'getSignedUrl' | 'copyFile'>>;
  let mockFileValidator: MockWithStubs<FileValidationService>;
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create typed mocks
    mockStorage = createMockWithStubs<Pick<FileStorageInterface, 'streamBufferUpload' | 'downloadFile' | 'remove' | 'getSignedUrl' | 'copyFile'>>({
      streamBufferUpload: sandbox.stub(),
      downloadFile: sandbox.stub(),
      remove: sandbox.stub(),
      getSignedUrl: sandbox.stub(),
      copyFile: sandbox.stub()
    });

    mockFileValidator = createMockWithStubs<FileValidationService>({
      validateFile: sandbox.stub().resolves({
        valid: true,
        errors: [],
        warnings: []
      })
    });

    fileStorageService = new FileStorageService(
      mockStorage.mock as unknown as FileStorageInterface,  // FileStorageInterface is complex, using unknown for test
      mockFileValidator.mock,
      testLogger
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('storeFile', () => {
    it('should store a file and return storage path', async () => {
      const fileData = {
        buffer: Buffer.from('test content'),
        originalname: 'test-file.pdf',
        mimetype: 'application/pdf',
        size: 1024
      };
      const userId = '507f1f77bcf86cd799439011';

      mockStorage.stubs.streamBufferUpload.resolves({
        path: 'ai-uploaded-files/2025/01/507f1f77bcf86cd799439011/12345-abc123-test-file.pdf'
      });

      const result = await fileStorageService.storeFile(fileData, userId);

      expect(result).to.deep.equal({
        storagePath: 'ai-uploaded-files/2025/01/507f1f77bcf86cd799439011/12345-abc123-test-file.pdf',
        size: 1024
      });

      // Verify the file was validated
      expect(mockFileValidator.stubs.validateFile.calledOnce).to.be.true;
      expect(mockFileValidator.stubs.validateFile.calledWith(
        fileData.buffer,
        fileData.mimetype,
        fileData.originalname
      )).to.be.true;

      // Verify the file was uploaded
      expect(mockStorage.stubs.streamBufferUpload.calledOnce).to.be.true;
    });

    it('should handle upload errors', async () => {
      const fileData = {
        buffer: Buffer.from('test content'),
        originalname: 'test-file.pdf',
        mimetype: 'application/pdf',
        size: 1024
      };
      const userId = '507f1f77bcf86cd799439011';

      mockStorage.stubs.streamBufferUpload.rejects(new Error('Upload failed'));

      await expect(fileStorageService.storeFile(fileData, userId))
        .to.be.rejectedWith('Failed to store file in cloud storage');
    });
  });

  describe('retrieveFile', () => {
    it('should retrieve a file from storage', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';
      const expectedBuffer = Buffer.from('file content');

      // Mock downloadFile to simulate file retrieval
      mockStorage.stubs.downloadFile.callsFake(async (source: string, dest: string) => {
        // Simulate the file being downloaded to the destination
        // In a real test, we would mock the fs operations, not perform them
        const fs = await import('fs/promises');
        const path = await import('path');
        await fs.mkdir(path.dirname(dest), { recursive: true });
        await fs.writeFile(dest, expectedBuffer);
      });

      const result = await fileStorageService.retrieveFile(storagePath);

      expect(result).to.deep.equal(expectedBuffer);
      expect(mockStorage.stubs.downloadFile.calledOnce).to.be.true;
      // Verify it was called with source path and a temporary destination path
      const [sourcePath, destPath] = mockStorage.stubs.downloadFile.firstCall.args;
      expect(sourcePath).to.equal(storagePath);
      expect(destPath).to.include('tmp/ai-downloads');
      expect(destPath).to.include('test-file.pdf');
    });

    it('should handle retrieval errors', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.downloadFile.rejects(new Error('Network error'));

      await expect(fileStorageService.retrieveFile(storagePath))
        .to.be.rejectedWith('Failed to retrieve file from cloud storage');
    });

    it('should handle file not found errors', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.downloadFile.rejects(new Error('File not found'));

      await expect(fileStorageService.retrieveFile(storagePath))
        .to.be.rejectedWith('File not found in storage:');
    });
  });

  describe('deleteFile', () => {
    it('should delete a file from storage', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.remove.resolves({ path: storagePath, statusCode: 200 });

      await fileStorageService.deleteFile(storagePath);

      expect(mockStorage.stubs.remove.calledOnce).to.be.true;
      expect(mockStorage.stubs.remove.calledWith(storagePath)).to.be.true;
    });

    it('should not throw on delete errors (just log warning)', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.remove.rejects(new Error('File not found'));

      // Should not throw
      await fileStorageService.deleteFile(storagePath);

      expect(mockStorage.stubs.remove.calledOnce).to.be.true;
    });
  });

  describe('getSignedUrl', () => {
    it('should generate a signed URL', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';
      const expectedUrl = 'https://storage.googleapis.com/bucket/path?signature=xxx';

      mockStorage.stubs.getSignedUrl.resolves([expectedUrl]);

      const result = await fileStorageService.getSignedUrl(storagePath, 30);

      expect(result).to.equal(expectedUrl);
      expect(mockStorage.stubs.getSignedUrl.calledOnce).to.be.true;

      const call = mockStorage.stubs.getSignedUrl.getCall(0);
      expect(call.args[0]).to.equal(storagePath);
      expect(call.args[1]).to.be.an('object');
      expect(call.args[1]).to.have.property('expireTimestamp');
      expect(call.args[1].expireTimestamp).to.be.a('number');
      // Verify the timestamp is approximately 30 minutes in the future
      const expectedTimestamp = Date.now() + (30 * 60 * 1000);
      expect(call.args[1].expireTimestamp).to.be.closeTo(expectedTimestamp, 1000); // Within 1 second
    });
  });

  describe('fileExists', () => {
    it('should return true if file exists', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.downloadFile.resolves();

      const result = await fileStorageService.fileExists(storagePath);

      expect(result).to.be.true;
      expect(mockStorage.stubs.downloadFile.calledOnce).to.be.true;
    });

    it('should return false if file does not exist', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      const error = Object.assign(new Error('No such object'), { code: 404 });
      mockStorage.stubs.downloadFile.rejects(error);

      const result = await fileStorageService.fileExists(storagePath);

      expect(result).to.be.false;
    });

    it('should re-throw non-404 errors', async () => {
      const storagePath = 'ai-uploaded-files/2025/01/user123/test-file.pdf';

      mockStorage.stubs.downloadFile.rejects(new Error('Network error'));

      await expect(fileStorageService.fileExists(storagePath))
        .to.be.rejectedWith('Network error');
    });
  });

  describe('copyFile', () => {
    it('should copy a file to a new location', async () => {
      const sourcePath = 'ai-uploaded-files/2025/01/user123/source.pdf';
      const destinationPath = 'ai-uploaded-files/2025/01/user123/destination.pdf';
      const fileBuffer = Buffer.from('file content');

      mockStorage.stubs.copyFile.resolves({ path: destinationPath });
      // Mock downloadFile to simulate the file buffer being returned
      mockStorage.stubs.downloadFile.resolves([fileBuffer]);

      const result = await fileStorageService.copyFile(sourcePath, destinationPath);

      expect(result).to.deep.equal({
        storagePath: destinationPath,
        size: fileBuffer.length
      });

      expect(mockStorage.stubs.copyFile.calledOnce).to.be.true;
      expect(mockStorage.stubs.copyFile.calledWith({
        source: sourcePath,
        destination: destinationPath
      })).to.be.true;
    });

    it('should reject path traversal attempts', async () => {
      const maliciousPaths = [
        '../../../etc/passwd',
        'ai-testing/../../../etc/passwd',
        'ai-testing/..\\..\\..\\windows\\system32',
        'ai-testing/%2E%2E%2F%2E%2E%2F%2E%2E%2Fetc%2Fpasswd',
        'ai-testing/\0malicious',
      ];

      for (const maliciousPath of maliciousPaths) {
        await expect(fileStorageService.copyFile(maliciousPath, 'ai-testing/safe.pdf'))
          .to.be.rejectedWith(BadRequestError, 'Invalid storage path');

        await expect(fileStorageService.copyFile('ai-testing/safe.pdf', maliciousPath))
          .to.be.rejectedWith(BadRequestError, 'Invalid storage path');
      }
    });
  });

  describe('path validation', () => {
    it('should reject path traversal in retrieveFile', async () => {
      await expect(fileStorageService.retrieveFile('../../../etc/passwd'))
        .to.be.rejectedWith(BadRequestError, 'Invalid storage path');
    });

    it('should reject path traversal in deleteFile', async () => {
      await expect(fileStorageService.deleteFile('../../../important-file'))
        .to.be.rejectedWith(BadRequestError, 'Invalid storage path');
    });

    it('should reject path traversal in getSignedUrl', async () => {
      await expect(fileStorageService.getSignedUrl('../../private/data'))
        .to.be.rejectedWith(BadRequestError, 'Invalid storage path');
    });

    it('should reject path traversal in fileExists', async () => {
      await expect(fileStorageService.fileExists('../../../etc/hosts'))
        .to.be.rejectedWith(BadRequestError, 'Invalid storage path');
    });

    it('should accept valid paths', async () => {
      const validPaths = [
        'ai-testing/file.pdf',
        'uploads/ai/document.docx',
        'tmp/ai/temp.txt',
        'ai-uploaded-files/2025/01/file.pdf'
      ];

      for (const validPath of validPaths) {
        // Should not throw validation error (will fail on actual storage operation)
        const error = Object.assign(new Error('File not found'), { code: 404 });
        mockStorage.stubs.downloadFile.rejects(error);

        // fileExists should return false for non-existent files without throwing
        const exists = await fileStorageService.fileExists(validPath);
        expect(exists).to.be.false;
      }
    });
  });
});
