/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { AITestingService } from '../../../server/service/ai/AITestingService';
import type { UnifiedAIModelFactory } from '../../../server/service/ai/UnifiedAIModelFactory';
import type { AITestingPromptService } from '../../../server/service/ai/AITestingPromptService';
import type { UTRPreparationService } from '../../../server/service/ai/services/UTRPreparationService';
import type { AIFileHandlingService } from '../../../server/service/ai/services/AIFileHandlingService';
import { createAITestingRequestInternal } from '../../fixtures/aiTestingFixtures';
import { createMockWithStubs, type MockWithStubs } from '../../utils/test-doubles';
import type { FileSupportAiModelWithCapabilities } from '../../../server/service/ai/models/FileSupportAiModel';
import { testLogger } from '../../factories/logger';
import { AiProvider } from '../../../server/service/ai/types';

describe('AITestingService', () => {
  const sandbox = sinon.createSandbox();
  let service: AITestingService;
  let mockAIModel: MockWithStubs<FileSupportAiModelWithCapabilities>;
  let mockModelFactory: MockWithStubs<UnifiedAIModelFactory>;
  let mockPromptService: MockWithStubs<AITestingPromptService>;
  let mockUtrPreparationService: MockWithStubs<UTRPreparationService>;
  let mockFileHandlingService: MockWithStubs<AIFileHandlingService>;

  beforeEach(() => {
    // Create mock for AI model
    mockAIModel = createMockWithStubs<FileSupportAiModelWithCapabilities>({
      executeWithFiles: sandbox.stub().resolves({
        data: {
          result: [
            { code: 'UTR001', score: 0.9, explanation: 'Test match' }
          ]
        },
        tokenUsage: {
          inputTokens: 1000,
          outputTokens: 500,
          totalTokens: 1500
        }
      }),
      createFile: sandbox.stub().resolves({ id: 'test-file-id' }),
      deleteFile: sandbox.stub().resolves(),
      getProvider: sandbox.stub().returns(AiProvider.OpenAi),
      supportsAssistants: sandbox.stub().returns(false),
      getModelVersion: sandbox.stub().returns('gpt-4'),
      createAssistant: sandbox.stub(),
      deleteAssistant: sandbox.stub(),
      runThreadWithAssistant: sandbox.stub(),
      parseCompletion: sandbox.stub(),
      runCompletion: sandbox.stub()
    });

    // Create mock for model factory
    mockModelFactory = createMockWithStubs<UnifiedAIModelFactory>({
      getFileSupportModel: sandbox.stub().returns(mockAIModel.mock)
    });

    // Create mock for prompt service
    mockPromptService = createMockWithStubs<AITestingPromptService>({
      improvePromptTemplate: sandbox.stub().resolves({ original: 'old', improved: 'new' }),
      improveCustomPrompt: sandbox.stub().resolves({ original: 'old', improved: 'new' }),
      generatePromptFromDescription: sandbox.stub().resolves({
        prompt: 'generated',
        name: 'Generated Prompt',
        variables: []
      })
    });

    // Create mock for UTR preparation service
    mockUtrPreparationService = createMockWithStubs<UTRPreparationService>({
      prepareUtrs: sandbox.stub().resolves([
        { code: 'UTR001', title: 'Test UTR 1' },
        { code: 'UTR002', title: 'Test UTR 2' }
      ]),
      createUtrsFile: sandbox.stub().resolves('utrs-file-id')
    });

    // Create mock for file handling service
    mockFileHandlingService = createMockWithStubs<AIFileHandlingService>({
      handleFileInput: sandbox.stub().resolves({
        fileIds: ['file1', 'file2'],
        temporaryFileIds: []
      }),
      resolveFileReference: sandbox.stub().resolves('resolved-file-id'),
      createFileReference: sandbox.stub().resolves({
        referenceId: 'ref-id',
        metadata: {}
      }),
      cleanupTemporaryFiles: sandbox.stub().resolves(),
      prepareVectorStore: sandbox.stub().resolves('vector-store-id')
    });

    // Create service with mocked dependencies
    service = new AITestingService(
      testLogger,
      mockModelFactory.mock,
      mockPromptService.mock,
      mockUtrPreparationService.mock,
      mockFileHandlingService.mock
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('executeTest', () => {
    it('should execute test successfully with composition pattern', async () => {
      const request = createAITestingRequestInternal({
        uploadedFileIds: ['507f1f77bcf86cd799439011'],
        prompt: {
          customPrompt: 'Test prompt {{variable}}',
          variables: { variable: 'value' }
        },
        options: {
          aiModel: 'gpt-4',
          includeExplanation: true
        }
      });

      const userId = new ObjectId().toString();

      const result = await service.executeTest(request, userId);

      expect(result).to.have.property('executionId');
      expect(result).to.have.property('results').that.is.an('array');
      expect(result.results[0]).to.deep.equal({
        utrCode: 'UTR001',
        relevanceScore: 0.9,
        explanation: 'Test match'
      });
      
      // Verify token usage is captured
      expect(result.metrics).to.have.property('tokenUsage');
      expect(result.metrics!.tokenUsage).to.deep.equal({
        inputTokens: 1000,
        outputTokens: 500,
        totalTokens: 1500
      });
      expect(result.metrics!.cost).to.be.a('number');
      expect(result.metrics!.cost).to.be.greaterThan(0);

      // Verify AI model was called correctly
      expect(mockAIModel.stubs.executeWithFiles.calledOnce).to.be.true;
      const [executeArgs] = mockAIModel.stubs.executeWithFiles.args[0];
      expect(executeArgs.prompt).to.include('Test prompt value');
      expect(executeArgs.files).to.be.an('array');
      expect(executeArgs.files.map((f: any) => f.fileId)).to.include.members(['file1', 'file2']);

      // Verify no files were created (since we're using uploadedFileIds)
      expect(mockAIModel.stubs.createFile.called).to.be.false;

      // Verify no cleanup was needed
      expect(mockAIModel.stubs.deleteFile.called).to.be.false;
    });

    it('should handle file reference properly', async () => {
      const fileRefId = new ObjectId().toString();
      const request = createAITestingRequestInternal({
        fileReference: fileRefId,
        prompt: {
          customPrompt: 'Test prompt'
        },
        options: {
          aiModel: 'gpt-4'
        }
      });

      const userId = new ObjectId().toString();

      const result = await service.executeTest(request, userId);

      expect(result).to.have.property('executionId');

      // Verify file handling was called
      expect(mockFileHandlingService.stubs.handleFileInput.calledOnce).to.be.true;
    });


    it('should delegate prompt methods to prompt service', async () => {
      await service.improvePrompt('prompt-id');
      expect(mockPromptService.stubs.improvePromptTemplate.calledWith('prompt-id')).to.be.true;

      await service.improveCustomPrompt('custom prompt');
      expect(mockPromptService.stubs.improveCustomPrompt.calledWith('custom prompt')).to.be.true;

      await service.generatePromptFromDescription('description', 'category');
      expect(mockPromptService.stubs.generatePromptFromDescription.calledWith('description', 'category')).to.be.true;
    });
  });
});
