import { config } from 'dotenv';
config({ path: './.env.test' });

import { expect } from 'chai';
import { ObjectId } from 'bson';
import { connect, disconnect } from '../../setup/mongoInMemory';
import ScheduledNotification, { ScheduledType } from '../../../server/models/scheduledNotification';
import Survey from '../../../server/models/survey';
import UniversalTrackerValue from '../../../server/models/universalTrackerValue';
import { getSurveyDeadlineScheduleService } from '../../../server/service/scheduled-notification/SurveyDeadlineScheduleService';
import { addDate, getStartOfDay, getEndOfDay } from '../../../server/util/date';
import { ActionList, UtrvType } from '../../../server/types/constants';
import { createSurvey } from '../../fixtures/survey';

before(connect);
after(disconnect);

describe('SurveyDeadlineScheduleService', () => {
  const service = getSurveyDeadlineScheduleService();

  beforeEach(async () => {
    // Clear collections before each test
    await ScheduledNotification.deleteMany({});
    await Survey.deleteMany({});
    await UniversalTrackerValue.deleteMany({});
  });

  describe('getDeadlineSurveyPipeline', () => {
    it('should correctly filter only active surveys (not completed or deleted)', async () => {
      // Create test surveys with different states
      const surveyIds = {
        active: new ObjectId(),
        completed: new ObjectId(),
        deleted: new ObjectId(),
        completedAndDeleted: new ObjectId(),
      };

      await Survey.create([
        createSurvey({
          _id: surveyIds.active,
          name: 'Active Survey',
          code: 'active/survey',
          deadlineDate: addDate(new Date(), 7, 'day'),
        }),
        createSurvey({
          _id: surveyIds.completed,
          name: 'Completed Survey',
          code: 'completed/survey',
          deadlineDate: addDate(new Date(), 7, 'day'),
          completedDate: new Date(),
        }),
        createSurvey({
          _id: surveyIds.deleted,
          name: 'Deleted Survey',
          code: 'deleted/survey',
          deadlineDate: addDate(new Date(), 7, 'day'),
          deletedDate: new Date(),
        }),
        createSurvey({
          _id: surveyIds.completedAndDeleted,
          name: 'Completed and Deleted Survey',
          code: 'completed-deleted/survey',
          deadlineDate: addDate(new Date(), 7, 'day'),
          completedDate: new Date(),
          deletedDate: new Date(),
        })
      ]);

      // Create scheduled notifications for all surveys
      const initiativeId = new ObjectId();
      const notifications = Object.entries(surveyIds).map(([key, surveyId]) => ({
        type: ScheduledType.SurveyDeadline,
        scheduledDate: addDate(new Date(), 1, 'day'),
        data: { surveyId },
        initiativeId,
        idempotencyKey: `${key}-${surveyId}`,
      }));

      await ScheduledNotification.create(notifications);

      // Run the pipeline
      const pipeline = [
        {
          $match: {
            scheduledDate: { $gt: getStartOfDay(), $lte: getEndOfDay(addDate(new Date(), 2, 'day')) },
            completedDate: { $exists: false },
            deletedDate: { $exists: false },
          },
        },
        ...service.getDeadlineSurveyPipeline()
      ];

      const results = await ScheduledNotification.aggregate(pipeline).exec();

      // Assert: Only active surveys are returned
      expect(results).to.have.lengthOf(1);
      expect(results[0].survey.name).to.equal('Active Survey');
      expect(results[0].survey.completedDate).to.be.undefined;
      expect(results[0].survey.deletedDate).to.be.undefined;
    });

    it('should include UTRVs for active surveys', async () => {
      const surveyId = new ObjectId();
      const utrvIds = [new ObjectId(), new ObjectId()];
      const utrId = new ObjectId();

      await Survey.create(
        createSurvey({
          _id: surveyId,
          name: 'Survey with UTRVs',
          code: 'survey/utrvs',
          deadlineDate: addDate(new Date(), 7, 'day'),
          visibleUtrvs: utrvIds,
        })
      );

      await UniversalTrackerValue.create(
        utrvIds.map((id, index) => ({
          _id: id,
          status: index === 0 ? ActionList.Created : ActionList.Updated,
          initiativeId: new ObjectId(),
          universalTrackerId: utrId,
          type: UtrvType.Baseline,
        }))
      );

      await ScheduledNotification.create({
        type: ScheduledType.SurveyDeadline,
        scheduledDate: addDate(new Date(), 1, 'day'),
        data: { surveyId },
        initiativeId: new ObjectId(),
        idempotencyKey: `utrvs-${surveyId}`,
      });

      const pipeline = [
        {
          $match: {
            scheduledDate: { $gt: getStartOfDay(), $lte: getEndOfDay(addDate(new Date(), 2, 'day')) },
            completedDate: { $exists: false },
            deletedDate: { $exists: false },
          },
        },
        ...service.getDeadlineSurveyPipeline()
      ];

      const results = await ScheduledNotification.aggregate(pipeline).exec();

      expect(results).to.have.lengthOf(1);
      expect(results[0].survey.name).to.equal('Survey with UTRVs');
      expect(results[0].utrvs).to.have.lengthOf(2);

      const resultUtrvIds = results[0].utrvs.map((u: any) => u._id.toString()).sort();
      const expectedUtrvIds = utrvIds.map(id => id.toString()).sort();
      expect(resultUtrvIds).to.deep.equal(expectedUtrvIds);
    });

    it('should handle surveys without deadline dates', async () => {
      const surveyId = new ObjectId();

      await Survey.create(
        createSurvey({
          _id: surveyId,
          name: 'Survey without deadline',
          code: 'no-deadline/survey',
          deadlineDate: undefined,
        })
      );

      await ScheduledNotification.create({
        type: ScheduledType.SurveyDeadline,
        scheduledDate: addDate(new Date(), 1, 'day'),
        data: { surveyId },
        initiativeId: new ObjectId(),
        idempotencyKey: `no-deadline-${surveyId}`,
      });

      const pipeline = [
        {
          $match: {
            scheduledDate: { $gt: getStartOfDay(), $lte: getEndOfDay(addDate(new Date(), 2, 'day')) },
            completedDate: { $exists: false },
            deletedDate: { $exists: false },
          },
        },
        ...service.getDeadlineSurveyPipeline()
      ];

      const results = await ScheduledNotification.aggregate(pipeline).exec();

      // Survey without deadline is still included if it's active
      // The actual deadline filtering happens in the notification service
      expect(results).to.have.lengthOf(1);
      expect(results[0].survey.name).to.equal('Survey without deadline');
      expect(results[0].survey.deadlineDate).to.be.undefined;
    });
  });
});
