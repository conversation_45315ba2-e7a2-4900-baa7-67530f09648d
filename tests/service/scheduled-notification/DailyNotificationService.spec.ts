import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { createMongooseModel, createAggregate } from '../../setup';
import { UserDailyNotificationService } from '../../../server/service/scheduled-notification/DailyNotificationService';
import Survey from '../../../server/models/survey';
import Initiative from '../../../server/models/initiative';
import { ActionList } from '../../../server/types/constants';
import { VisibleStakeholders } from '../../../server/service/survey/VisibleStakeholders';
import type { UserDailyNotificationModel } from '../../../server/models/userDailyNotification';
import { createSurveyDeadlineNotificationData } from '../../fixtures/scheduledNotificationFixtures';
import type { NotificationService } from '../../../server/service/notification/NotificationService';
import type { RootInitiativeService } from '../../../server/service/organization/RootInitiativeService';
import { testLogger } from '../../factories/logger';

describe('DailyNotificationService', () => {
  const sandbox = sinon.createSandbox();
  let service: UserDailyNotificationService;
  let mockNotificationService: Partial<NotificationService> & { createNotification: sinon.SinonStub };
  let mockRootInitiativeService: Partial<RootInitiativeService> & { getOrganization: sinon.SinonStub };

  function createMockSurveyDeadlineNotification(params: {
    userId: ObjectId;
    surveyIds: ObjectId[];
    total: number;
    stakeholderCount?: number;
    verifierCount?: number;
  }) {
    const notification = createSurveyDeadlineNotificationData(params);

    return {
      ...notification,
      save: sandbox.stub().resolves(),
      markModified: sandbox.stub()
    };
  }

  beforeEach(() => {
    mockNotificationService = {
      createNotification: sandbox.stub().resolves(),
    };

    mockRootInitiativeService = {
      getOrganization: sandbox.stub().resolves({
        _id: new ObjectId(),
        appConfigCode: 'TEST'
      }),
    };

    service = new UserDailyNotificationService(
      testLogger,
      mockNotificationService as unknown as NotificationService,
      mockRootInitiativeService as unknown as RootInitiativeService
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('sendSurveyDeadlineReminder', () => {
    const userId = new ObjectId();
    const surveyId1 = new ObjectId();
    const surveyId2 = new ObjectId();
    const utrvId1 = new ObjectId();
    const utrvId2 = new ObjectId();
    const utrvId3 = new ObjectId();
    const initiativeId = new ObjectId();

    const mockSurvey1 = {
      _id: surveyId1,
      visibleUtrvs: [utrvId1, utrvId2],
      initiativeId,
      name: 'Test Survey 1',
      effectiveDate: new Date(),
      deadlineDate: new Date('2025-07-30')
    };

    const mockSurvey2 = {
      _id: surveyId2,
      visibleUtrvs: [utrvId3],
      initiativeId,
      name: 'Test Survey 2',
      effectiveDate: new Date(),
      deadlineDate: new Date('2025-07-30')
    };

    const mockInitiative = {
      _id: initiativeId,
      name: 'Test Initiative'
    };

    it('should skip sending email when all questions are verified', async () => {
      const mockDailyNotification = createMockSurveyDeadlineNotification({
        userId,
        surveyIds: [surveyId1, surveyId2],
        total: 22,
        stakeholderCount: 0,
        verifierCount: 22
      });
      const aggregateResult = [
        {
          ...mockSurvey1,
          utrvs: [
            { _id: utrvId1, status: ActionList.Verified },
            { _id: utrvId2, status: ActionList.Verified }
          ]
        },
        {
          ...mockSurvey2,
          utrvs: [
            { _id: utrvId3, status: ActionList.Verified }
          ]
        }
      ];
      sandbox.stub(Survey, 'aggregate').returns(createAggregate(aggregateResult));
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(mockSurvey1));
      const statsMap = new Map();
      statsMap.set(userId.toString(), { total: 0, stakeholderCount: 0, verifierCount: 0 });
      sandbox.stub(VisibleStakeholders, 'getAllRelatedStakeholderUtrvStats').resolves(statsMap);
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel(mockInitiative));
      await service.sendSurveyDeadlineReminder(mockDailyNotification as unknown as UserDailyNotificationModel);

      expect(mockNotificationService.createNotification.called).to.be.false;
      expect(mockDailyNotification.skippedDate).to.exist;
      expect(mockDailyNotification.completedDate).to.exist;
      expect(mockDailyNotification.save.called).to.be.true;
    });

    it('should send email when there are outstanding questions', async () => {
      const mockDailyNotification = createMockSurveyDeadlineNotification({
        userId,
        surveyIds: [surveyId1, surveyId2],
        total: 22,
        stakeholderCount: 0,
        verifierCount: 22
      });
      const aggregateResult = [
        {
          ...mockSurvey1,
          utrvs: [
            { _id: utrvId1, status: ActionList.Created },
            { _id: utrvId2, status: ActionList.Updated }
          ]
        },
        {
          ...mockSurvey2,
          utrvs: [
            { _id: utrvId3, status: ActionList.Rejected }
          ]
        }
      ];
      sandbox.stub(Survey, 'aggregate').returns(createAggregate(aggregateResult));
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(mockSurvey1));
      const statsMap1 = new Map();
      statsMap1.set(userId.toString(), { total: 10, stakeholderCount: 5, verifierCount: 10 });
      const statsMap2 = new Map();
      statsMap2.set(userId.toString(), { total: 10, stakeholderCount: 5, verifierCount: 10 });
      const visibleStakeholdersStub = sandbox.stub(VisibleStakeholders, 'getAllRelatedStakeholderUtrvStats');
      visibleStakeholdersStub.onFirstCall().resolves(statsMap1);
      visibleStakeholdersStub.onSecondCall().resolves(statsMap2);
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel(mockInitiative));
      await service.sendSurveyDeadlineReminder(mockDailyNotification as unknown as UserDailyNotificationModel);

      expect(mockNotificationService.createNotification.called).to.be.true;
      expect(mockDailyNotification.data.templateData.total).to.equal(20);
      expect(mockDailyNotification.data.templateData.stakeholderCount).to.equal(10);
      expect(mockDailyNotification.data.templateData.verifierCount).to.equal(20);
      expect(mockDailyNotification.markModified.calledWith('data.templateData')).to.be.true;
      expect(mockDailyNotification.completedDate).to.exist;
      expect(mockDailyNotification.skippedDate).to.not.exist;
      expect(mockDailyNotification.save.called).to.be.true;
    });

    it('should correctly count outstanding questions for verifier role', async () => {
      const aggregateResult = [
        {
          ...mockSurvey1,
          utrvs: [
            { _id: utrvId1, status: ActionList.Updated, stakeholders: { verifier: [userId] } },
            { _id: utrvId2, status: ActionList.Created, stakeholders: { verifier: [userId] } }
          ]
        }
      ];
      sandbox.stub(Survey, 'aggregate').returns(createAggregate(aggregateResult));
      const statsMap = new Map();
      statsMap.set(userId.toString(), { total: 22, stakeholderCount: 0, verifierCount: 22 });
      sandbox.stub(VisibleStakeholders, 'getAllRelatedStakeholderUtrvStats').resolves(statsMap);
      const result = await (service as any).getCurrentOutstandingQuestions(userId, [surveyId1]);

      expect(result.total).to.equal(22);
      expect(result.stakeholderCount).to.equal(0);
      expect(result.verifierCount).to.equal(22);
    });

    it('should handle error in getCurrentOutstandingQuestions gracefully', async () => {
      const errorNotification = createMockSurveyDeadlineNotification({
        userId,
        surveyIds: [surveyId1, surveyId2],
        total: 22,
        stakeholderCount: 0,
        verifierCount: 22
      });
      const errorAggregate = {
        exec: sandbox.stub().rejects(new Error('Database connection error'))
      };
      sandbox.stub(Survey, 'aggregate').returns(errorAggregate as unknown as ReturnType<typeof Survey.aggregate>);
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(mockSurvey1));
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel(mockInitiative));
      await service.sendSurveyDeadlineReminder(errorNotification as unknown as UserDailyNotificationModel);

      expect(mockNotificationService.createNotification.called).to.be.true;
      expect(errorNotification.data.templateData.total).to.equal(22);
      expect(errorNotification.completedDate).to.exist;
      expect(errorNotification.save.called).to.be.true;
    });

    it('should handle error in notification params gracefully', async () => {
      const aggregateResult = [
        {
          ...mockSurvey1,
          utrvs: [
            { _id: utrvId1, status: ActionList.Created }
          ]
        }
      ];
      sandbox.stub(Survey, 'aggregate').returns(createAggregate(aggregateResult));
      const statsMap = new Map();
      statsMap.set(userId.toString(), { total: 5, stakeholderCount: 5, verifierCount: 0 });
      sandbox.stub(VisibleStakeholders, 'getAllRelatedStakeholderUtrvStats').resolves(statsMap);
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(null));
      const errorNotification = {
        ...createMockSurveyDeadlineNotification({
          userId,
          surveyIds: [surveyId1, surveyId2],
          total: 22,
          stakeholderCount: 0,
          verifierCount: 22
        }),
        erroredDate: undefined
      };
      await expect(service.sendSurveyDeadlineReminder(errorNotification as unknown as UserDailyNotificationModel))
        .to.be.rejectedWith('Survey deadline reminder - Do not find any survey with deadline');
      expect(errorNotification.erroredDate).to.exist;
      expect(errorNotification.save.called).to.be.true;
    });
  });
});
