import { MaterialityAssessmentConfigService } from '../../../server/service/materiality-assessment/MaterialityAssessmentConfigService';
import { createSandbox } from 'sinon';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import { MaterialityAssessmentConfig, UpdateImpactScope } from '../../../server/models/materiality';
import UserError from '../../../server/error/UserError';
import '../../setup';
import { getMTPPTXReportService } from '../../../server/service/pptx-report/MTPPTXReportService';
import { getMaterialityMetricGroupService } from '../../../server/service/materiality-assessment/MaterialityMetricGroupService';
import { SurveyRepository } from '../../../server/repository/SurveyRepository';
import { surveyOne } from '../../fixtures/survey';
import { createMongooseModel } from '../../setup';
import Survey from '../../../server/models/survey';

describe('MaterialityAssessmentConfigService', () => {
  let service: MaterialityAssessmentConfigService;
  let backgroundJobService: any;
  const pptxService = getMTPPTXReportService();
  const metricGroupService = getMaterialityMetricGroupService();
  const sandbox = createSandbox();

  beforeEach(() => {
    backgroundJobService = {
      findExistingJob: sandbox.stub(),
    };
    service = new MaterialityAssessmentConfigService(
      backgroundJobService,
      pptxService,
      SurveyRepository,
      metricGroupService
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('update', () => {
    const userId = new ObjectId();
    it('should throw error if assessment job not found', async () => {
      backgroundJobService.findExistingJob.resolves(undefined);

      await expect(
        service.update({ initiativeId: new ObjectId(), surveyId: new ObjectId(), impactScopes: [], userId })
      ).to.eventually.be.rejectedWith(UserError, /Materiality Assessment results not found/);
    });

    it('should throw error if assessment job data not found', async () => {
      backgroundJobService.findExistingJob.resolves({ tasks: [] });

      await expect(
        service.update({ initiativeId: new ObjectId(), surveyId: new ObjectId(), impactScopes: [], userId })
      ).to.eventually.be.rejectedWith(UserError, /Materiality Assessment results not found/);
    });

    it('should update assessment config in backgroundJob', async () => {
      const saveStub = sandbox.stub();
      const markModifiedStub = sandbox.stub();
      backgroundJobService.findExistingJob.resolves({
        tasks: [{ data: {} }],
        markModified: markModifiedStub,
        save: saveStub,
      });
      const config: MaterialityAssessmentConfig = {
        orderedTopics: [{ code: 'topic1' }, { code: 'topic2' }],
        explanation: 'explanation',
      };
      const initiativeId = new ObjectId();
      const surveyId = new ObjectId();

      sandbox.stub(SurveyRepository, 'mustFindById').returns(createMongooseModel(new Survey(surveyOne)));
      const pptxServiceStub = sandbox.stub(pptxService, 'createJob');
      const metricGroupServiceStub = sandbox.stub(metricGroupService, 'findOrCreateMetricGroup');

      const result = await service.update({
        initiativeId,
        surveyId,
        config,
        impactScopes: [UpdateImpactScope.Reports],
        userId,
      });

      expect(result).to.deep.equal(config);
      expect(backgroundJobService.findExistingJob.firstCall.firstArg).to.deep.equal({ initiativeId, surveyId });
      expect(markModifiedStub.firstCall.firstArg).to.equal('tasks');
      expect(saveStub.calledOnce).to.be.true;

      expect(pptxServiceStub.calledOnce).to.be.true;
      expect(metricGroupServiceStub.calledOnce).to.be.false;
    });
  });
});
