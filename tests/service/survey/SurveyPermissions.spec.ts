import { ObjectId } from 'bson';
import { expect } from 'chai';
import { SurveyPermissions } from '../../../server/service/survey/SurveyPermissions';
import sinon from 'sinon';
import { getWorkgroupPermissions } from '../../../server/service/workgroup/WorkgroupPermissions';
import { InitiativePermissions } from '../../../server/service/initiative/InitiativePermissions';

describe('SurveyPermissions', () => {
  const sandbox = sinon.createSandbox();
  const workgroupPermissions = getWorkgroupPermissions();

  afterEach(() => {
    sandbox.restore();
  });

  describe('canManage', () => {
    it('should return true if user is survey admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [user._id],
          viewer: [],
        },
      };

      expect(await SurveyPermissions.canManage(survey, user)).to.equal(true);
    });

    it('should return true if user belong to workgroup that has survey admin role', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(true);

      expect(await SurveyPermissions.canManage(survey, user)).to.equal(true);
    });

    it('should return true if user is initiative admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canManageInitiative').resolves(true);

      expect(await SurveyPermissions.canManage(survey, user)).to.equal(true);
    });

    it('should return false if none of the above', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canManageInitiative').resolves(false);

      expect(await SurveyPermissions.canManage(survey, user)).to.equal(false);
    });
  });

  describe('canAccess', () => {
    it('should return true if user is survey visibleStakeholders', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        visibleStakeholders: [user._id],
      };

      expect(await SurveyPermissions.canAccess(survey, user)).to.equal(true);
    });
    it('should return true if user is survey admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [user._id],
          viewer: [],
        },
        visibleStakeholders: [],
      };

      expect(await SurveyPermissions.canAccess(survey, user)).to.equal(true);
    });

    it('should return true if user belong to workgroup that has survey canAccessRoles', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        visibleStakeholders: [],
        permissions: [],
      };
      const stub = sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(true);
      const result = await SurveyPermissions.canAccess(survey, user);

      expect(stub.firstCall.firstArg.roles).to.equal(SurveyPermissions.canAccessRoles);
      expect(result).to.equal(true);
    });

    it('should return true if user can access initiative', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        visibleStakeholders: [],
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canAccess').resolves(true);

      expect(await SurveyPermissions.canAccess(survey, user)).to.equal(true);
    });

    it('should return false if none of the above', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        visibleStakeholders: [],
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canAccess').resolves(false);

      expect(await SurveyPermissions.canAccess(survey, user)).to.equal(false);
    });
  });

  describe('canAccessAllData', () => {
    it('should return true if user is survey admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [user._id],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
        visibleStakeholders: [],
      };

      expect(await SurveyPermissions.canAccessAllData(survey, user)).to.equal(true);
    });

    it('should return true if user is stakeholder', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [user._id],
          verifier: [],
          escalation: [],
        },
        visibleStakeholders: [],
      };

      expect(await SurveyPermissions.canAccessAllData(survey, user)).to.equal(true);
    });

    it('should return true if user belong to workgroup that has survey canAccessAllDataRoles', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
        visibleStakeholders: [],
      };
      const stub = sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(true);
      const result = await SurveyPermissions.canAccessAllData(survey, user);

      expect(stub.firstCall.firstArg.roles).to.equal(SurveyPermissions.canAccessAllDataRoles);
      expect(result).to.equal(true);
    });

    it('should return true if user can access all survey data at initiative level', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
        visibleStakeholders: [],
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canAccessAllSurveyData').resolves(true);

      expect(await SurveyPermissions.canAccessAllData(survey, user)).to.equal(true);
    });

    it('should return false if none of the above', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
        visibleStakeholders: [],
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canAccessAllSurveyData').resolves(false);

      expect(await SurveyPermissions.canAccessAllData(survey, user)).to.equal(false);
    });
  });

  describe('canContribute', () => {
    it('should return true if user is survey admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [user._id],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };

      expect(await SurveyPermissions.canContribute(survey, user)).to.equal(true);
    });

    it('should return true if user is stakeholder', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [user._id],
          verifier: [],
          escalation: [],
        },
      };

      expect(await SurveyPermissions.canContribute(survey, user)).to.equal(true);
    });

    it('should return true if user belong to workgroup that has survey canContributeRoles', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      const stub = sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(true);
      const result = await SurveyPermissions.canContribute(survey, user);

      expect(stub.firstCall.firstArg.roles).to.equal(SurveyPermissions.canContributeRoles);
      expect(result).to.equal(true);
    });

    it('should return true if user can contribute to survey at initiative level', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canContribute').resolves(true);

      expect(await SurveyPermissions.canContribute(survey, user)).to.equal(true);
    });

    it('should return false if none of the above', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canContribute').resolves(false);

      expect(await SurveyPermissions.canContribute(survey, user)).to.equal(false);
    });
  });

  describe('canVerify', () => {
    it('should return true if user is survey admin', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [user._id],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };

      expect(await SurveyPermissions.canVerify(survey, user)).to.equal(true);
    });

    it('should return true if user is verifier', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [user._id],
          escalation: [],
        },
      };

      expect(await SurveyPermissions.canVerify(survey, user)).to.equal(true);
    });

    it('should return true if user belong to workgroup that has survey canVerifyRoles', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      const stub = sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(true);
      const result = await SurveyPermissions.canVerify(survey, user);

      expect(stub.firstCall.firstArg.roles).to.equal(SurveyPermissions.canVerifyRoles);
      expect(result).to.equal(true);
    });

    it('should return true if user can verify at initiative level', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canVerify').resolves(true);

      expect(await SurveyPermissions.canVerify(survey, user)).to.equal(true);
    });

    it('should return false if none of the above', async () => {
      const user = {
        _id: new ObjectId(),
        permissions: [],
      };

      const survey = {
        initiativeId: new ObjectId(),
        roles: {
          admin: [],
          viewer: [],
        },
        stakeholders: {
          stakeholder: [],
          verifier: [],
          escalation: [],
        },
      };
      sandbox.stub(workgroupPermissions, 'checkHasSurveyUserRoles').resolves(false);
      sandbox.stub(InitiativePermissions, 'canVerify').resolves(false);

      expect(await SurveyPermissions.canVerify(survey, user)).to.equal(false);
    });
  });
});
