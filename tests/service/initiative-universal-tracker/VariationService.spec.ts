import { ObjectId } from 'bson';
import { expect } from 'chai';
import sinon from 'sinon';
import { VariationService } from '../../../server/service/initiative-universal-tracker/VariationService';
import { universalTrackerOne } from '../../fixtures/universalTrackerFixtures';
import {
  ColumnType,
  TableAggregationType,
  TableColumn,
  UtrValueType,
  ValueAggregation,
  Variation,
  VariationDataSource,
  VariationType,
} from '../../../server/models/public/universalTrackerType';
import { utrvOne, utrvTwo } from '../../fixtures/universalTrackerValueFixtures';
import UniversalTrackerValue from '../../../server/models/universalTrackerValue';
import { createMongooseModel } from '../../setup';
import { InitiativeUniversalTrackerPlain } from '../../../server/models/initiativeUniversalTracker';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import { valueListTestTable } from '../../fixtures/valueListFixtures';
import { utrTableOneWithOneGroupByPercentage } from '../../fixtures/utr/utrTableFixtures';

describe('VariationService', () => {
  const sandbox = sinon.createSandbox();
  let service: VariationService;
  let initiativeUtrService: any;

  beforeEach(() => {
    initiativeUtrService = {
      getRootInitiativeUniversalTrackers: sandbox.stub(),
    };
    service = new VariationService(initiativeUtrService);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getUtrvVariations', () => {
    const createVariation = (overrides: Partial<Variation> = {}) => ({
      type: VariationType.Percentage,
      dataSource: VariationDataSource.LastMonth,
      min: 10,
      max: 10,
      confirmationRequired: false,
      ...overrides,
    });
    const survey = { _id: new ObjectId(), unitConfig: { currency: 'JPY' } };
    const universalTracker = { ...universalTrackerOne, valueType: UtrValueType.Number };
    const utrv = { ...utrvOne, universalTracker };
    const dataSourceUtrv = { ...utrvTwo, valueData: { input: { value: 100 } }, universalTracker, survey };
    const initiativeUtr: InitiativeUniversalTrackerPlain = {
      _id: new ObjectId(),
      universalTrackerId: universalTrackerOne._id,
      initiativeId: initiativeOneSimple._id,
      valueValidation: { variations: [createVariation()] },
    };

    it('should return empty array when no overrides', async () => {
      initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([]);
      sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(utrv));
      const result = await service.getUtrvVariations(utrv._id);
      expect(result).to.deep.equal([]);
    });

    it('should return empty array when no data source utrv is found', async () => {
      initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeUtr]);
      sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(utrv));
      sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(undefined));
      const result = await service.getUtrvVariations(utrv._id);
      expect(result).to.deep.equal([]);
    });

    it(`should return variations - valueType: number, dataSource: ${VariationDataSource.LastMonth}`, async () => {
      initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeUtr]);
      sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(utrv));
      sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(dataSourceUtrv));

      const result = await service.getUtrvVariations(utrv._id);

      const variation = {
        ...createVariation(),
        valueListCode: undefined,
        details: {
          baseline: 100,
          min: 90,
          max: 110,
          unit: undefined,
          numberScale: undefined,
          aggregationColumns: undefined,
          effectiveDate: dataSourceUtrv.effectiveDate,
        },
      };

      expect(result).to.deep.equal([variation]);
    });

    it(`should return variations - valueType: single-row table, dataSource: ${VariationDataSource.LastYear}`, async () => {
      const tableUtr = {
        ...universalTrackerOne,
        valueType: UtrValueType.Table,
        valueValidation: {
          table: {
            columns: [
              { code: 'col-currency', unit: 'USD', unitType: 'currency', numberScale: 'millions' },
              { code: 'col-unit', unit: 'mt', unitType: 'volume' },
            ],
            validation: { maxRows: 1 },
          },
        },
      };
      const tableUtrv = { ...utrvOne, universalTracker: tableUtr };
      const dataSourceTableUtrv = {
        ...utrvOne,
        survey,
        universalTracker: tableUtr,
        valueData: {
          input: {
            table: [
              [
                { code: 'col-currency', value: 100, unit: 'EUR', numberScale: 'single' },
                { code: 'col-unit', value: 200, unit: 't' },
              ],
            ],
          },
        },
      };
      const initiativeTableUtr: InitiativeUniversalTrackerPlain = {
        _id: new ObjectId(),
        universalTrackerId: universalTrackerOne._id,
        initiativeId: initiativeOneSimple._id,
        valueValidation: {
          table: {
            columns: tableUtr.valueValidation.table?.columns.map((col) => ({
              ...col,
              validation: {
                variations: [createVariation({ dataSource: VariationDataSource.LastYear })],
              },
            })) as TableColumn[],
          },
        },
      };

      initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeTableUtr]);
      sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(tableUtrv));
      sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(dataSourceTableUtrv));

      const result = await service.getUtrvVariations(utrv._id);

      const variations = [
        {
          ...createVariation({ dataSource: VariationDataSource.LastYear }),
          valueListCode: 'col-currency',
          details: {
            baseline: 100,
            min: 90,
            max: 110,
            unit: 'JPY',
            numberScale: 'single',
            aggregationColumns: undefined,
            effectiveDate: dataSourceTableUtrv.effectiveDate,
          },
        },
        {
          ...createVariation({ dataSource: VariationDataSource.LastYear }),
          valueListCode: 'col-unit',
          details: {
            baseline: 200,
            min: 180,
            max: 220,
            unit: 't',
            numberScale: undefined,
            aggregationColumns: undefined,
            effectiveDate: dataSourceTableUtrv.effectiveDate,
          },
        },
      ];
      expect(result).to.deep.equal(variations);
    });

    it(`should return variations - valueType: numericValueList, dataSource: ${VariationDataSource.LatestVerified}`, async () => {
      const numericValueListUtr = {
        ...universalTrackerOne,
        valueType: UtrValueType.NumericValueList,
        valueListOptions: {
          code: 'value-list/one',
          name: 'Value List One',
          options: [
            { code: 'option1', name: 'Option 1' },
            { code: 'option2', name: 'Option 2' },
          ],
        },
        unitType: 'volume',
        unit: 'mt',
      };
      const numericValueListUtrv = { ...utrvOne, universalTracker: numericValueListUtr };
      const dataSourceNumericValueListUtrv = {
        ...utrvOne,
        survey,
        universalTracker: numericValueListUtr,
        valueData: { input: { data: { option1: 100, option2: 200 }, unit: 't', numberScale: 'single' } },
      };

      initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([
        {
          ...initiativeUtr,
          valueValidation: { variations: [createVariation({ dataSource: VariationDataSource.LatestVerified })] },
        },
      ]);
      sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(numericValueListUtrv));
      sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(dataSourceNumericValueListUtrv));

      const result = await service.getUtrvVariations(utrv._id);

      const variations = [
        {
          ...createVariation({ dataSource: VariationDataSource.LatestVerified }),
          valueListCode: 'option1',
          details: {
            baseline: 100,
            min: 90,
            max: 110,
            unit: 't',
            numberScale: 'single',
            aggregationColumns: undefined,
            effectiveDate: dataSourceNumericValueListUtrv.effectiveDate,
          },
        },
        {
          ...createVariation({ dataSource: VariationDataSource.LatestVerified }),
          valueListCode: 'option2',
          details: {
            baseline: 200,
            min: 180,
            max: 220,
            unit: 't',
            numberScale: 'single',
            aggregationColumns: undefined,
            effectiveDate: dataSourceNumericValueListUtrv.effectiveDate,
          },
        },
      ];

      expect(result).to.deep.equal(variations);
    });

    describe('multi-row table', () => {
      const tableUtr = {
        ...universalTrackerOne,
        valueType: UtrValueType.Table,
        valueValidation: {
          table: {
            columns: [
              {
                code: 'col-value-list',
                name: 'Col Value List',
                listId: valueListTestTable._id,
                type: ColumnType.Text,
              },
              // valueAggregation: ColumnValueAggregation.ColumnSumAggregator is default
              { code: 'col-num', name: 'Col Number', type: ColumnType.Number },
            ],
          },
        },
      };
      const initiativeTableUtr: InitiativeUniversalTrackerPlain = {
        _id: new ObjectId(),
        universalTrackerId: universalTrackerOne._id,
        initiativeId: initiativeOneSimple._id,
        valueValidation: {
          table: {
            columns: [
              {
                code: 'col-num',
                validation: { variations: [createVariation()] },
              },
            ] as TableColumn[],
          },
        },
      };

      it('should return empty when table does not have group aggregator', async () => {
        const tableUtrv = { ...utrvOne, universalTracker: tableUtr };
        initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeTableUtr]);
        sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(tableUtrv));
        const utrvFindOneSpy = sandbox.spy(UniversalTrackerValue, 'findOne');

        const result = await service.getUtrvVariations(utrv._id);
        expect(utrvFindOneSpy.callCount).to.equal(0);
        expect(result).to.deep.equal([]);
      });

      it('should return variations when table has group aggregator', async () => {
        const tableWithGroupAggregatorUtr = {
          ...tableUtr,
          valueLabel: 'Table With Group Aggregator',
          valueAggregation: ValueAggregation.TableRowGroupAggregator,
          valueValidation: {
            table: {
              ...tableUtr.valueValidation?.table,
              aggregation: {
                type: TableAggregationType.Group,
                columns: [{ code: 'col-value-list' }],
              },
            },
          },
        };

        const [optionOne, optionTwo, optionThree] = valueListTestTable.options;

        const dataSourceTableUtrv = {
          ...utrvOne,
          survey,
          universalTracker: tableWithGroupAggregatorUtr,
          valueData: {
            table: [
              [
                { code: 'col-value-list', value: optionOne.code },
                { code: 'col-num', value: 100 },
              ],
              [
                { code: 'col-value-list', value: optionOne.code },
                { code: 'col-num', value: 200 },
              ],
              [
                { code: 'col-value-list', value: optionTwo.code },
                { code: 'col-num', value: 400 },
              ],
              [
                { code: 'col-value-list', value: optionThree.code },
                { code: 'col-num', value: undefined },
              ],
            ],
          },
        };

        const tableUtrv = { ...utrvOne, universalTracker: tableWithGroupAggregatorUtr };
        initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeTableUtr]);
        sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(tableUtrv));
        sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(dataSourceTableUtrv));

        const result = await service.getUtrvVariations(utrv._id);

        const variations = [
          {
            ...createVariation(),
            valueListCode: 'col-num',
            details: {
              baseline: 300,
              min: 270,
              max: 330,
              unit: undefined,
              numberScale: undefined,
              aggregationColumns: [{ code: 'col-value-list', value: optionOne.code }],
              effectiveDate: dataSourceTableUtrv.effectiveDate,
            },
          },
          {
            ...createVariation(),
            valueListCode: 'col-num',
            details: {
              baseline: 400,
              min: 360,
              max: 440,
              unit: undefined,
              numberScale: undefined,
              aggregationColumns: [{ code: 'col-value-list', value: optionTwo.code }],
              effectiveDate: dataSourceTableUtrv.effectiveDate,
            },
          },
        ];

        expect(result).to.deep.equal(variations);
      });

      it('should return variations when table has group percentage', async () => {
        const tableWithGroupAggregatorUtr = {
          ...utrTableOneWithOneGroupByPercentage,
          valueLabel: 'Table With Group Aggregator',
          valueAggregation: ValueAggregation.TableRowGroupAggregator,
          valueValidation: {
            table: {
              ...utrTableOneWithOneGroupByPercentage.valueValidation?.table,
              aggregation: {
                type: TableAggregationType.Group,
                columns: [{ code: 'test/cm/20-1' }],
              },
            },
          },
        };

        const [optionOne, optionTwo] = valueListTestTable.options;

        const dataSourceTableUtrv = {
          ...utrvOne,
          survey,
          universalTracker: tableWithGroupAggregatorUtr,
          valueData: {
            table: [
              [
                { code: 'test/cm/20-1', value: optionOne.code },
                { code: 'test/cm/20-2', value: 100 },
              ],
              [
                { code: 'test/cm/20-1', value: optionOne.code },
                { code: 'test/cm/20-2', value: 200 },
              ],
              [
                { code: 'test/cm/20-1', value: optionTwo.code },
                { code: 'test/cm/20-2', value: 400 },
              ],
            ],
          },
        };

        const tableUtrv = { ...utrvOne, universalTracker: tableWithGroupAggregatorUtr };

        const initiativeTableUtr: InitiativeUniversalTrackerPlain = {
          _id: new ObjectId(),
          universalTrackerId: tableWithGroupAggregatorUtr._id,
          initiativeId: initiativeOneSimple._id,
          valueValidation: {
            table: {
              columns: [
                {
                  code: 'test/cm/20-2',
                  validation: { variations: [createVariation()] },
                },
              ] as TableColumn[],
            },
          },
        };


        initiativeUtrService.getRootInitiativeUniversalTrackers.resolves([initiativeTableUtr]);
        sandbox.stub(UniversalTrackerValue, 'findById').returns(createMongooseModel(tableUtrv));
        const findStub = sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(dataSourceTableUtrv));
        const result = await service.getUtrvVariations(utrv._id);

        const variations = [
          {
            ...createVariation(),
            valueListCode: 'test/cm/20-2',
            details: {
              baseline: 150,
              min: 135,
              max: 165,
              unit: undefined,
              numberScale: undefined,
              aggregationColumns: [{ code: 'test/cm/20-1', value: optionOne.code }],
              effectiveDate: dataSourceTableUtrv.effectiveDate,
            },
          },
          {
            ...createVariation(),
            valueListCode: 'test/cm/20-2',
            details: {
              baseline: 400,
              min: 360,
              max: 440,
              unit: undefined,
              numberScale: undefined,
              aggregationColumns: [{ code: 'test/cm/20-1', value: optionTwo.code }],
              effectiveDate: dataSourceTableUtrv.effectiveDate,
            },
          },
        ];
        expect(result).to.deep.equal(variations);
      });

    });
  });
});
