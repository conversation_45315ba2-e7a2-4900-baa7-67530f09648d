/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import setup, { createMongooseModel } from '../../setup';
import { expect } from 'chai';
import { createSandbox, type SinonStub } from 'sinon';
import { ObjectId } from 'bson';
import {
  getReportDocumentManager,
  ReportDocumentManager,
} from '../../../server/service/report-document/ReportDocumentManager';
import ReportDocument, { type CreateReportDocument, ReportDocumentStatus, ReportDocumentTemplate, ReportDocumentType } from '../../../server/models/reportDocument';
import {
  getCreateReportDocumentFixture,
  getEditorStateFixture,
  getReportDocumentFixture,
} from '../../fixtures/reportDocumentFixtures';
import { userOne } from '../../fixtures/userFixtures';
import { getReportingFactory } from '../../../server/service/reporting/ReportingFactory';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { getAIReportDocumentWorkflow } from '../../../server/service/report-document/AIReportDocumentWorkflow';
import ContextError from '../../../server/error/ContextError';

describe('ReportDocumentManager', () => {
  const sandbox = createSandbox();
  const reportingFactory = getReportingFactory();
  const reportWorkflow = getAIReportDocumentWorkflow();
  const manager = new ReportDocumentManager(wwgLogger, reportingFactory, reportWorkflow);
  const reportId = new ObjectId();
  const reportIdStr = reportId.toString();
  const initiativeId = new ObjectId();
  const initiativeIdStr = initiativeId.toString();

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getReportDocumentManager()).to.be.instanceOf(ReportDocumentManager);
  });

  describe('create', () => {
    let saveStub: SinonStub;

    beforeEach(() => {
      saveStub = setup.wrapStub(sandbox, ReportDocument.prototype, 'save', function (this: CreateReportDocument) {
        return this;
      });
    });

    const createData = getCreateReportDocumentFixture();

    it('should create a new report document', async () => {
      const document = await manager.create(createData);
      expect(document.title).equals(createData.title);
      expect(saveStub.calledOnce).to.equal(true);
    });

    it('should throw error if save fails', async () => {
      const error = new ContextError('Save failed');
      saveStub.rejects(error);
      await expect(manager.create(createData)).to.be.rejectedWith(error);
    });
  });

  describe('get', () => {
    let findOneStub: SinonStub;

    it('should get a report by id', async () => {
      const reportData = { _id: reportId, initiativeId };
      findOneStub = sandbox.stub(ReportDocument, 'findOne').returns(createMongooseModel(reportData));
      const report = await manager.get({ reportId: reportIdStr, initiativeId: initiativeIdStr });
      expect(findOneStub.calledOnce).to.equal(true);
      expect(findOneStub.firstCall.args[0]).to.deep.equal({
        _id: reportId,
        initiativeId,
      });
      expect(report).to.deep.equal(reportData);
    });

    it('should throw an error if getting a report fails', async () => {
      const error = new ContextError('Get failed');
      findOneStub = sandbox.stub(ReportDocument, 'findOne').returns(createMongooseModel(Promise.reject(error)));
      await expect(
        manager.get({ reportId: reportIdStr, initiativeId: initiativeIdStr })
      ).to.be.rejectedWith(error);
    });
  });

  describe('list', () => {
    let findStub: SinonStub;

    it('should list reports by initiativeId', async () => {
      findStub = sandbox.stub(ReportDocument, 'find').returns(createMongooseModel([getReportDocumentFixture(new ObjectId(), initiativeId)]));
      const reports = await manager.list({ initiativeId: initiativeIdStr });
      expect(findStub.calledOnce).to.equal(true);
      expect(findStub.firstCall.args[0]).to.deep.equal({
        initiativeId,
      });
      expect(reports).to.be.an('array').with.lengthOf(1);
    });

    it('should return an empty array if no reports are found', async () => {
      findStub = sandbox.stub(ReportDocument, 'find').returns(createMongooseModel([]));
      const reports = await manager.list({ initiativeId: initiativeIdStr });
      expect(findStub.calledOnce).to.equal(true);
      expect(reports).to.be.an('array').with.lengthOf(0);
    });
  });

  describe('update', () => {
    let findOneAndUpdateStub: SinonStub;
    const updatePayload = { title: 'Updated Title' };

    it('should update a report', async () => {
      findOneAndUpdateStub = sandbox.stub(ReportDocument, 'findOneAndUpdate').returns(createMongooseModel({ _id: reportId, initiativeId, ...updatePayload }));
      const report = await manager.update({
        reportId: reportIdStr,
        initiativeId: initiativeIdStr,
        ...updatePayload,
      });
      expect(findOneAndUpdateStub.calledOnce).to.equal(true);
      expect(findOneAndUpdateStub.firstCall.args[0]).to.deep.equal({
        _id: reportId,
        initiativeId,
      });
      expect(findOneAndUpdateStub.firstCall.args[1]).to.deep.equal({
        $set: updatePayload,
      });
      expect(report.title).to.equal('Updated Title');
    });

    it('should throw an error if updating a report fails', async () => {
      const error = new ContextError('Update failed');
      findOneAndUpdateStub = sandbox.stub(ReportDocument, 'findOneAndUpdate').returns(createMongooseModel(Promise.reject(error)));
      await expect(
        manager.update({
          reportId: reportIdStr,
          initiativeId: initiativeIdStr,
          ...updatePayload,
        })
      ).to.be.rejectedWith(error);
    });
  });

  describe('deleteReport', () => {
    let findOneAndDeleteStub: SinonStub;

    it('should delete a report document', async () => {
      findOneAndDeleteStub = sandbox.stub(ReportDocument, 'findOneAndDelete').returns(createMongooseModel({ _id: reportId }));
      await manager.deleteReport({ reportId: reportIdStr, initiativeId: initiativeIdStr });

      expect(findOneAndDeleteStub.calledOnce).to.equal(true);
      expect(findOneAndDeleteStub.firstCall.args[0]).to.deep.equal({
        initiativeId,
        _id: reportId,
      });
    });

    it('should throw error if delete fails', async () => {
      const error = new ContextError('Delete failed');
      findOneAndDeleteStub = sandbox.stub(ReportDocument, 'findOneAndDelete').returns(createMongooseModel(Promise.reject(error)));
      await expect(manager.deleteReport({ reportId: reportIdStr, initiativeId: initiativeIdStr })).to.be.rejectedWith(error);
    });
  });

  describe('getTemplate', () => {
    let findByIdStub: SinonStub;
    let lexicalGeneratorStub: SinonStub;
    let generatorStub: any;
    const user = userOne;
    const reportDocument = getReportDocumentFixture(reportId);

    beforeEach(() => {
      generatorStub = { getTemplate: sandbox.stub().resolves({ template: 'mockTemplate' }) };
      lexicalGeneratorStub = sandbox.stub(reportingFactory, 'getLexicalStateGenerator').returns(generatorStub as any);
    });

    it('should return the template', async () => {
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(reportDocument));
      const result = await manager.getTemplate({ reportId: reportIdStr, user });

      expect(findByIdStub.calledOnceWith(reportId)).to.equal(true);
      expect(lexicalGeneratorStub.calledOnceWith(reportDocument.type)).to.equal(true);
      expect(generatorStub.getTemplate.calledOnce).to.equal(true);
      expect(result).to.deep.equal({ template: 'mockTemplate' });
    });

    it('should throw error if report document not found', async () => {
      findByIdStub = sandbox
        .stub(ReportDocument, 'findById')
        .returns(createMongooseModel(Promise.reject(new ContextError('Report not found'))));
      await expect(manager.getTemplate({ reportId: reportIdStr, user })).to.be.rejectedWith('Report not found');
    });
  });

  describe('download', () => {
    let findByIdStub: SinonStub;
    let lexicalGeneratorStub: SinonStub;
    let generatorStub: any;
    const editorState = getEditorStateFixture();
    const reportDocument = getReportDocumentFixture(reportId);

    beforeEach(() => {
      generatorStub = { downloadReport: sandbox.stub().resolves('mockDownload') };
      lexicalGeneratorStub = sandbox.stub(reportingFactory, 'getLexicalStateGenerator').returns(generatorStub);
    });

    it('should download the report', async () => {
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(reportDocument));
      const result = await manager.download({ reportId: reportIdStr, editorState });

      expect(findByIdStub.calledOnceWith(reportId)).to.equal(true);
      expect(lexicalGeneratorStub.calledOnceWith(reportDocument.type)).to.equal(true);
      expect(generatorStub.downloadReport.calledOnce).to.equal(true);
      expect(result).to.equal('mockDownload');
    });

    it('should throw error if report document not found', async () => {
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(Promise.reject(new ContextError('Report not found'))));
      await expect(manager.download({ reportId: reportIdStr, editorState })).to.be.rejectedWith(
        'Report not found'
      );
    });
  });

  describe('initializeLexicalStateFromTemplate', () => {
    let findByIdStub: SinonStub;
    let lexicalGeneratorStub: SinonStub;
    let generatorStub: any;
    let upsertJobStub: SinonStub;

    const user = userOne;

    beforeEach(() => {
      generatorStub = { getTemplate: sandbox.stub().resolves({ root: { type: 'root' } }) };
      lexicalGeneratorStub = sandbox.stub(reportingFactory, 'getLexicalStateGenerator').returns(generatorStub);
      upsertJobStub = sandbox.stub(reportWorkflow, 'upsertJob');
    });

    it('should generate lexical state for Simple template and update status', async () => {
      const hydratedDoc = {
        _id: reportId,
        initiativeId,
        type: ReportDocumentType.CSRD,
        status: ReportDocumentStatus.Pending,
        config: { template: ReportDocumentTemplate.Simple },
        save: sandbox.stub().resolvesThis(),
      };
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(hydratedDoc));

      const result = await manager.initializeLexicalStateFromTemplate({ reportId: reportIdStr, user });

      expect(findByIdStub.calledOnceWith(reportIdStr)).to.equal(true);
      expect(lexicalGeneratorStub.calledOnceWith(hydratedDoc.type)).to.equal(true);
      expect(generatorStub.getTemplate.calledOnce).to.equal(true);
      expect(hydratedDoc.save.calledOnce).to.equal(true);
      expect(hydratedDoc.status).to.equal(ReportDocumentStatus.Generated);
      expect(result).to.deep.equal({ status: ReportDocumentStatus.Generated, lexicalState: { root: { type: 'root' } } });
    });

    it('should enqueue AI job for AiGenerated template and return current status', async () => {
      const hydratedDoc = {
        _id: reportId,
        initiativeId,
        type: 'csrd',
        status: ReportDocumentStatus.Pending,
        config: { template: ReportDocumentTemplate.AiGenerated },
        save: sandbox.stub().resolvesThis(),
      };
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(hydratedDoc));

      const result = await manager.initializeLexicalStateFromTemplate({ reportId: reportIdStr, user });

      expect(findByIdStub.calledOnceWith(reportIdStr)).to.equal(true);
      expect(upsertJobStub.calledOnceWith(hydratedDoc)).to.equal(true);
      expect(result).to.deep.equal({ status: ReportDocumentStatus.Pending });
    });

    it('should return status only for Blank template', async () => {
      const hydratedDoc = {
        _id: reportId,
        initiativeId,
        type: ReportDocumentType.CSRD,
        status: ReportDocumentStatus.Completed,
        config: { template: ReportDocumentTemplate.Blank },
        save: sandbox.stub().resolvesThis(),
      };
      findByIdStub = sandbox.stub(ReportDocument, 'findById').returns(createMongooseModel(hydratedDoc));

      const result = await manager.initializeLexicalStateFromTemplate({ reportId: reportIdStr, user });

      expect(findByIdStub.calledOnceWith(reportIdStr)).to.equal(true);
      expect(result).to.deep.equal({ status: ReportDocumentStatus.Completed });
    });

    it('should throw error if report document not found', async () => {
      findByIdStub = sandbox
        .stub(ReportDocument, 'findById')
        .returns(createMongooseModel(Promise.reject(new ContextError('Report not found'))));
      await expect(
        manager.initializeLexicalStateFromTemplate({ reportId: reportIdStr, user })
      ).to.be.rejectedWith('Report not found');
    });
  });

});
