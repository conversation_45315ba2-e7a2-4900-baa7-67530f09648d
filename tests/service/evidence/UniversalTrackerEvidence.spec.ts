import * as sinon from 'sinon';
import EvidenceUploader from '../../../server/service/evidence/EvidenceUploader';
import { mockPdfFile } from '../../fixtures/fileUploadFixtures';
import { userIdOne } from '../../fixtures/userFixtures';
import { utrValueOneId } from '../../fixtures/universalTrackerValueFixtures';
import { initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import documentModel from '../../../server/models/document';
import { expect } from 'chai';
import UserError from '../../../server/error/UserError';
import UniversalTrackerEvidence from '../../../server/service/evidence/UniversalTrackerEvidence';

describe('UniversalTrackerEvidence', () => {
  const sandbox = sinon.createSandbox();
  let utrEvidenceService: UniversalTrackerEvidence;
  let uploaderMock: EvidenceUploader;
  let storageMock: any;
  let documentSaveStub: sinon.SinonStub;

  beforeEach(() => {
    storageMock = {
      getSignedUrl: sandbox.stub(),
      upload: sandbox.stub().resolves({ path: `uploads/${mockPdfFile.originalname}` }),
      remove: sandbox.stub(),
      getExtensionFromMimeType: sandbox.stub().returns('pdf'),
      getContentTypeFromMimeType: sandbox.stub().returns(mockPdfFile.mimetype),
      getPublicUrl: sandbox.stub(),
    };
    uploaderMock = new EvidenceUploader(storageMock);
    utrEvidenceService = new UniversalTrackerEvidence(uploaderMock);
    documentSaveStub = sandbox.stub(documentModel.prototype, 'save').callsFake(function (this: unknown) {
      return this;
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('upload', () => {
    it('should use initiativeId as ownerId when the file need to be saved to library', async () => {
      const request = {
        utrvId: utrValueOneId.toString(),
        initiativeId: initiativeOneSimpleId.toString(),
        userId: userIdOne,
        files: [mockPdfFile],
        filesDescriptions: [''],
        filesMetadata: [
          {
            id: 0,
            saveToLibrary: true,
            description: '',
          },
        ],
      };

      const [result] = await utrEvidenceService.upload(request);
      expect(documentSaveStub.calledOnce).to.be.true;
      expect(result.ownerId && result.ownerId.equals(initiativeOneSimpleId)).to.be.true;
    });
    it('should use utrvId as ownerId when the file is only used for that utrv', async () => {
      const request = {
        utrvId: utrValueOneId.toString(),
        initiativeId: initiativeOneSimpleId.toString(),
        userId: userIdOne,
        files: [mockPdfFile],
        filesDescriptions: [''],
        filesMetadata: [
          {
            id: 0,
            saveToLibrary: false,
            description: '',
          },
        ],
      };

      const [result] = await utrEvidenceService.upload(request);
      expect(documentSaveStub.calledOnce).to.be.true;
      expect(result.ownerId && result.ownerId.equals(utrValueOneId)).to.be.true;
    });
    it('should do nothing if no file is provided', async () => {
      const request = {
        utrvId: utrValueOneId.toString(),
        initiativeId: initiativeOneSimpleId.toString(),
        userId: userIdOne,
        files: [],
      };
      const result = await utrEvidenceService.upload(request);
      expect(documentSaveStub.calledOnce).not.to.be.true;
      expect(result).to.be.empty;
    });
    it('should throw error if the file type is not supported', async () => {
      const invalidType = 'invalid';
      storageMock = {
        getContentTypeFromMimeType: sandbox.stub().returns(invalidType),
        getExtensionFromMimeType: sandbox.stub().returns(invalidType),
      };
      uploaderMock = new EvidenceUploader(storageMock);
      utrEvidenceService = new UniversalTrackerEvidence(uploaderMock);
      const request = {
        utrvId: utrValueOneId.toString(),
        initiativeId: initiativeOneSimpleId.toString(),
        userId: userIdOne,
        files: [{ ...mockPdfFile, mimetype: invalidType }],
      };
      await expect(utrEvidenceService.upload(request)).to.be.rejectedWith(
        UserError,
        `Files of type ${invalidType} are not supported.`
      );
    });
  });
});
