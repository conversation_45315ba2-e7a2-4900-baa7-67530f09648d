import sinon from 'sinon';
import { expect } from 'chai';
import { CustomReportManager } from '../../../server/service/custom-report/CustomReportManager';
import { createMongooseModel } from '../../setup';
import { ObjectId } from 'bson';
import { CustomReportType } from '../../../server/models/customReport';
import { DataPeriods } from '../../../server/service/utr/constants';
import { SurveyType } from '../../../server/models/survey';

describe('CustomReportManager', () => {
  let instance: CustomReportManager;
  let customReportModel: any;
  let surveyRepo: any;
  beforeEach(() => {
    surveyRepo = {
      findByIds: sinon.stub(),
    };

    customReportModel = {
      findOne: sinon.stub(),
    };

    instance = new CustomReportManager(customReportModel, surveyRepo);
  });
  afterEach(async () => {
    sinon.restore();
  });

  describe('getById', () => {
    it('should return custom report without converting data if not template', async () => {
      const customReport = { _id: new ObjectId(), initiativeId: new ObjectId(), type: CustomReportType.Metrics };
      customReportModel.findOne.returns(createMongooseModel(customReport));
      const result = await instance.getById({ id: customReport._id, initiativeId: customReport.initiativeId });

      expect(customReportModel.findOne.args[0][0]).to.deep.eq({
        _id: customReport._id,
        initiativeId: customReport.initiativeId,
      });
      expect(result).to.deep.eq(customReport);
    });

    it('should return custom report without converting data if is new template ', async () => {
      const customReport = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        type: CustomReportType.Template,
        survey: {
          initiativeIds: [new ObjectId()],
          surveyFilters: [{ effectiveDate: new Date(), period: DataPeriods.Monthly, type: SurveyType.Default }],
        },
      };
      customReportModel.findOne.returns(createMongooseModel(customReport));
      const result = await instance.getById({ id: customReport._id, initiativeId: customReport.initiativeId });

      expect(customReportModel.findOne.args[0][0]).to.deep.eq({
        _id: customReport._id,
        initiativeId: customReport.initiativeId,
      });
      expect(result).to.deep.eq(customReport);
    });

    it('should return custom report with converting data if is old template ', async () => {
      const survey = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        effectiveDate: new Date(),
        period: DataPeriods.Monthly,
        type: SurveyType.Default,
      };
      const customReport = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        type: CustomReportType.Template,
        survey: {
          ids: [survey._id],
        },
      };
      customReportModel.findOne.returns(createMongooseModel(customReport));
      surveyRepo.findByIds.resolves([survey]);
      const result = await instance.getById({ id: customReport._id, initiativeId: customReport.initiativeId });

      expect(customReportModel.findOne.args[0][0]).to.deep.eq({
        _id: customReport._id,
        initiativeId: customReport.initiativeId,
      });
      expect(surveyRepo.findByIds.args[0][0]).to.deep.eq([survey._id]);
      expect(result).to.deep.eq({
        ...customReport,
        survey: {
          ids: undefined,
          initiativeIds: [survey.initiativeId],
          surveyFilters: [{ effectiveDate: survey.effectiveDate, period: survey.period, type: survey.type }],
        },
      });
    });

    it('should return custom report with converting data if is old subsidiary comparison', async () => {
      const survey = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        effectiveDate: new Date(),
        period: DataPeriods.Monthly,
        type: SurveyType.Default,
      };
      const customReport = {
        _id: new ObjectId(),
        initiativeId: new ObjectId(),
        type: CustomReportType.Initiatives,
        survey: {
          initiativeIds: [survey.initiativeId],
          effectiveDate: survey.effectiveDate,
          period: survey.period,
          type: survey.type,
        },
      };
      customReportModel.findOne.returns(createMongooseModel(customReport));
      const result = await instance.getById({ id: customReport._id, initiativeId: customReport.initiativeId });

      expect(customReportModel.findOne.args[0][0]).to.deep.eq({
        _id: customReport._id,
        initiativeId: customReport.initiativeId,
      });
      expect(result).to.deep.eq({
        ...customReport,
        survey: {
          initiativeIds: [survey.initiativeId],
          surveyFilters: [{ effectiveDate: survey.effectiveDate, period: survey.period, type: survey.type }],
          effectiveDate: undefined,
          period: undefined,
          type: undefined,
        },
      });
    });
  });
});
