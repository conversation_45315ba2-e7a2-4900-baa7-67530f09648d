import { expect } from 'chai';
import sinon from 'sinon';
import { Column, InputColumnRule } from '../../../server/models/customReport';
import { ValueData } from '../../../server/models/public/universalTrackerValueType';
import { CustomReportData } from '../../../server/service/custom-report/ReportDataResolver';
import { ReportUtrv } from '../../../server/service/custom-report/reportTypes';
import { getTabularReportDataResolver } from '../../../server/service/custom-report/TabularReportDataResolver';
import { TabularReportGenerator } from '../../../server/service/custom-report/TabularReportGenerator';
import { blueprintDefaultUnitConfig } from '../../../server/service/units/unitTypes';
import { ActionList } from '../../../server/service/utr/constants';
import {
  createCurrencyNumberUtrv,
  createMultiRowTableUtrv,
  createNumberUtrv,
  createNumericValueListUtrv,
  createPercentageUtrv,
  createSingleRowTableUtrv,
  createTextUtrv,
  createUnitNumberUtrv,
  createValueListUtrv,
  defaultDownloadParams,
  downloadScopeWithOverrides,
  downloadScopeWithUserInput,
  numberScale,
  surveyId,
  unit,
  valueDataInputTableWithSameUnitType,
  valueDataTableWithSameUnitType,
} from '../../fixtures/customReportFixtures';
import { yesNoList } from '../../fixtures/valueListFixtures';
import { ObjectId } from 'bson';
import { QUESTION, SURVEY } from '../../../server/util/terminology';
import { initiativeOneSimple, initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { getNumericCellFormat } from '../../fixtures/universalTrackerValueFixtures';
import { utrTableWithMatchingUnits } from '../../fixtures/universalTrackerFixtures';
import { createHistory } from '../../factories/valueHistory';

describe('TabularReportGenerator', () => {
  let baseReportDownload: any;
  const tabularReportDataResolver = getTabularReportDataResolver();
  let tabularReportGenerator: TabularReportGenerator;

  beforeEach(() => {
    baseReportDownload = {
      getReportDataWithExtras: sinon.stub(),
    };

    tabularReportGenerator = new TabularReportGenerator(baseReportDownload, tabularReportDataResolver);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDownloadData', () => {
    const mockReportDataWithExtras = {
      reportDataWithAltCode: [],
      valueLists: [],
      utrTagMap: new Map(),
      assuranceMap: new Map(),
      surveyUnitConfigMap: new Map([[surveyId.toHexString(), blueprintDefaultUnitConfig]]),
      utrvAssurances: [],
    };
    const history = [createHistory({ action: ActionList.Created, value: undefined })];

    describe('records with headers', () => {
      it('should return empty records if data is empty', async () => {
        baseReportDownload.getReportDataWithExtras.resolves(mockReportDataWithExtras);
        const { records } = await tabularReportGenerator.getDownloadData(defaultDownloadParams);
        expect(records).to.deep.eq([]);
      });

      it('should return records with headers and empty row for each utr if data is not empty', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            { code: 'question/a', typeCode: 'a', name: 'A', utrvs: [{ status: ActionList.Created, surveyId, history }] },
            { code: 'question/b', typeCode: 'b', name: 'B', utrvs: [{ status: ActionList.Created, surveyId, history }] },
          ] as CustomReportData[],
        });

        const { headers, records } = await tabularReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input'].map((c) => ({ code: c } as Column)),
        });
        expect(headers).to.deep.eq(undefined);
        expect(records).to.deep.eq([
          ['Code', 'Title', 'Answer'],
          ['a', 'A', undefined],
          [],
          ['Code', 'Title', 'Answer'],
          ['b', 'B', undefined],
          [],
        ]);
      });

      it('should only return one table per utr when there are multi surveys', async () => {
        const surveyOne = {
          surveyId: surveyId,
          effectiveDate: new Date('2025-01-31T23:59:59.999Z'),
        };
        const surveyTwo = {
          surveyId: new ObjectId(),
          effectiveDate: new Date('2024-12-31T23:59:59.999Z'),
        };

        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            { code: 'question/a', name: 'A', utrvs: [{ status: ActionList.Created, ...surveyOne, history }] },
            { code: 'question/a', name: 'A', utrvs: [{ status: ActionList.Updated, ...surveyTwo, history }] },
            { code: 'question/b', name: 'B', utrvs: [{ status: ActionList.Verified, ...surveyOne, history }] },
            { code: 'question/b', name: 'B', utrvs: [{ status: ActionList.Rejected, ...surveyTwo, history }] },
          ] as CustomReportData[],
        });

        const { headers, records } = await tabularReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['effectiveDate', 'name', 'status'].map((c) => ({ code: c } as Column)),
        });
        expect(headers).to.deep.eq(undefined);
        expect(records).to.deep.eq([
          [`${SURVEY.CAPITALIZED_ADJECTIVE} Date`, 'Title', 'Status'],
          ['January 2025', 'A', 'Not submitted'],
          ['December 2024', 'A', 'Submitted'],
          [],
          [`${SURVEY.CAPITALIZED_ADJECTIVE} Date`, 'Title', 'Status'],
          ['January 2025', 'B', 'Verified'],
          ['December 2024', 'B', 'Rejected'],
          [],
        ]);
      });

      it('should use surveyName when available', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            {
              code: 'question/a',
              typeCode: 'a',
              name: 'A',
              utrvs: [{ status: ActionList.Created, surveyId, surveyName: 'Test Survey', history }],
            },
          ] as CustomReportData[],
        });

        const { headers, records } = await tabularReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input', 'surveyName'].map((c) => ({ code: c } as Column)),
        });

        expect(headers).to.deep.eq(undefined);
        expect(records).to.deep.eq([
          ['Code', 'Title', 'Answer', `${SURVEY.CAPITALIZED_SINGULAR} Name`],
          ['a', 'A', undefined, 'Test Survey'],
          [],
        ]);
      });

      it('should fallback to generated name when surveyName is missing', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            {
              code: 'question/b',
              typeCode: 'b',
              name: 'B',
              utrvs: [{ status: ActionList.Created, surveyId, initiativeId: initiativeOneSimpleId, history }],
            },
          ] as CustomReportData[],
          initiativeMap: new Map([[initiativeOneSimpleId.toString(), initiativeOneSimple]]),
        });

        const { headers, records } = await tabularReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input', 'surveyName'].map((c) => ({ code: c } as Column)),
        });

        expect(headers).to.deep.eq(undefined);
        expect(records).to.deep.eq([
          ['Code', 'Title', 'Answer', `${SURVEY.CAPITALIZED_SINGULAR} Name`],
          ['b', 'B', undefined, initiativeOneSimple.name],
          [],
        ]);
      });

      describe('unit and numberScale and input alignment - unitInput/numberScaleInput is set', () => {
        describe('unanswered', () => {
          const history = [createHistory({ action: ActionList.Created, value: undefined })];
          const utrvs = [{ status: ActionList.Created, surveyId, history }] as ReportUtrv[];
          const tableUtrvs = [
            {
              _id: new ObjectId(),
              status: ActionList.Created,
              surveyId,
              valueData: { table: [] },
              lastUpdated: new Date(),
              history,
            } as ValueData,
          ] as ReportUtrv[];
          const reportDataWithAltCode = [
            createNumberUtrv({ utrvs }),
            createUnitNumberUtrv({ utrvs }),
            createCurrencyNumberUtrv({ utrvs }),
            createPercentageUtrv({ utrvs }),
            createNumericValueListUtrv({ utrvs }),
            createSingleRowTableUtrv({ overrides: { utrvs: tableUtrvs } }),
          ];

          const columnCodes = ['valueLabel', 'unit', 'numberScale', 'input'];
          const columnHeaders = [QUESTION.CAPITALIZED_SINGULAR, 'Unit', 'Number Scale', 'Answer'];

          it('should return default unit/numberScale if not use metric override preferences', async () => {
            baseReportDownload.getReportDataWithExtras.resolves({ ...mockReportDataWithExtras, reportDataWithAltCode });
            const { records } = await tabularReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns: columnCodes.map((c) => ({ code: c } as Column)),
            });

            const expectedRecords = [
              columnHeaders,
              ['Number value label', '', '', undefined],
              [],
              columnHeaders,
              ['Number with unit value label', unit.default, '', undefined],
              [],
              columnHeaders,
              ['Number with currency/numberScale value label', 'USD', numberScale.default, undefined],
              [],
              columnHeaders,
              ['Percentage value label', '%', '', undefined],
              [],
              // numericValueList
              [QUESTION.CAPITALIZED_SINGULAR, 'Unit', 'Number Scale', 'Surface Water', 'Ground Water', 'Total'],
              ['Numeric value list value label', unit.default, '', undefined, undefined, undefined],
              [],
              // table
              [QUESTION.CAPITALIZED_SINGULAR, 'Unit', 'Number Scale', 'Col Num', 'Col Currency', 'Col Unit', 'Col Percentage'],
              ['Numeric table value label', undefined, undefined, undefined, undefined, undefined, undefined],
              [],
            ];
            expect(records).to.deep.eq(expectedRecords);
          });

          it('should return overridden unit/numberScale if use metric override preferences', async () => {
            baseReportDownload.getReportDataWithExtras.resolves({ ...mockReportDataWithExtras, reportDataWithAltCode });
            const { records } = await tabularReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              downloadScope: downloadScopeWithOverrides,
              columns: columnCodes.map((c) => ({ code: c } as Column)),
            });

            const expectedRecords = [
              columnHeaders,
              ['Number value label', '', numberScale.override, undefined],
              [],
              columnHeaders,
              ['Number with unit value label', unit.override, '', undefined],
              [],
              columnHeaders,
              ['Number with currency/numberScale value label', 'USD', numberScale.override, undefined],
              [],
              columnHeaders,
              ['Percentage value label', '%', numberScale.override, undefined],
              [],
              // numericValueList
              [QUESTION.CAPITALIZED_SINGULAR, 'Unit', 'Number Scale', 'Surface Water', 'Ground Water', 'Total'],
              ['Numeric value list value label', unit.override, '', undefined, undefined, undefined],
              [],
              // table
              [QUESTION.CAPITALIZED_SINGULAR, 'Unit', 'Number Scale', 'Col Num', 'Col Currency', 'Col Unit', 'Col Percentage'],
              ['Numeric table value label', undefined, undefined, undefined, undefined, undefined, undefined],
              [],
            ];
            expect(records).to.deep.eq(expectedRecords);
          });
        });

        describe('answered', () => {
          const columnCodes = ['input', 'unit', 'numberScale'];
          const columns = columnCodes.map((c) => ({ code: c } as Column));
          const downloadParams = { ...defaultDownloadParams, columns };

          const columnHeaders = ['Answer', 'Unit', 'Number Scale'];
          const numericValueListHeaders = ['Surface Water', 'Ground Water', 'Total', 'Unit', 'Number Scale'];
          const tableHeaders = ['Col Num', 'Col Currency', 'Col Unit', 'Col Percentage', 'Unit', 'Number Scale'];

          describe('valueType: number', () => {
            describe('no unit, no numberScale', () => {
              beforeEach(() => {
                const numberUtrv = createNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [numberUtrv],
                });
              });

              it('should return user input data if use user input', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 456 }), '', ''], []]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 0.456 }), '', numberScale.override], []]);
              });
            });

            describe('has unit', () => {
              beforeEach(() => {
                const unitNumberUtrv = createUnitNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [unitNumberUtrv],
                });
              });

              it('should return converted data if use system default', async () => {
                const { records } = await tabularReportGenerator.getDownloadData(downloadParams);
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 456000 }), unit.default, ''], []]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 456 }), unit.input, ''], []]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 0.456 }), unit.override, ''], []]);
              });
            });

            describe('has currency - numberScale', () => {
              beforeEach(() => {
                const currencyNumberUtrv = createCurrencyNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [currencyNumberUtrv],
                });
              });

              it('should return converted data if use system default', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 0.0456 }), 'USD', numberScale.default], []]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 456 }), 'USD', numberScale.input], []]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await tabularReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 45.6 }), 'USD', numberScale.override], []]);
              });
            });
          });

          describe('valueType: percentage', () => {
            beforeEach(() => {
              const percentageUtrv = createPercentageUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [percentageUtrv],
              });
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 20 }), '%', ''], []]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([columnHeaders, [getNumericCellFormat({ value: 0.02 }), '%', numberScale.override], []]);
            });
          });

          describe('valueType: numericValueList', () => {
            beforeEach(() => {
              const numericValueListUtrv = createNumericValueListUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [numericValueListUtrv],
              });
            });

            it('should return user input data if use system default', async () => {
              const { records } = await tabularReportGenerator.getDownloadData(downloadParams);
              expect(records).to.deep.eq([
                numericValueListHeaders,
                [
                  getNumericCellFormat({ value: '4000' }),
                  getNumericCellFormat({ value: '5000' }),
                  getNumericCellFormat({ value: 9000 }),
                  unit.default,
                  '',
                ],
                [],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                numericValueListHeaders,
                [
                  getNumericCellFormat({ value: '4' }),
                  getNumericCellFormat({ value: '5' }),
                  getNumericCellFormat({ value: 9 }),
                  unit.input,
                  '',
                ],
                [],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([numericValueListHeaders, [
                getNumericCellFormat({ value: 0.004 }),
                getNumericCellFormat({ value: 0.005 }),
                getNumericCellFormat({ value: 0.009 }),
                unit.override, ''], []]);
            });
          });

          describe('valueType: single-row table', () => {
            beforeEach(() => {
              const tableUtrv = createSingleRowTableUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [tableUtrv],
              });
            });

            it('should return user input data if use system default', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
              });
              expect(records).to.deep.eq([
                tableHeaders,
                [getNumericCellFormat({ value: '2' }), 'USD 0.0002 Millions', '2000 Cubic meters', '2 %', undefined, undefined],
                [],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                tableHeaders,
                [getNumericCellFormat({ value: '2' }), 'USD 2 Hundreds', '2 Megalitres', '2 %', undefined, undefined],
                [],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([
                tableHeaders,
                [
                  '0.002 Thousands',
                  'USD 0.2 Thousands',
                  '0.002 Million cubic meters',
                  '0.002 Thousands %',
                  undefined,
                  undefined,
                ],
                [],
              ]);
            });
          });

          describe('valueType: single-row table - same unitType across columns', () => {
            beforeEach(() => {
              const tableUtrv = createSingleRowTableUtrv({
                utr: utrTableWithMatchingUnits,
                data: valueDataTableWithSameUnitType,
                inputData: valueDataInputTableWithSameUnitType,
              });
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [tableUtrv],
              });
            });

            it('should return default data if use system default', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                [
                  { options: { type: 'n' }, value: '2' },
                  { options: { type: 'n' }, value: '21' },
                  { options: { type: 'n' }, value: '22' },
                  unit.default,
                  '',
                ],
                [],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                [
                  { options: { type: 'n' }, value: '2000' },
                  { options: { type: 'n' }, value: '2001' },
                  { options: { type: 'n' }, value: '2002' },
                  unit.input,
                  '',
                ],
                [],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              // mismatch unit, match numberScale
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                [
                  '0.02 Million cubic meters', // Mcm
                  '0.02001 Million cubic meters', // Mcm
                  '20.02 Megalitres', // Ml
                  undefined,
                  'hundreds',
                ],
                [],
              ]);
            });
          });

          describe('valueType: multi-row table', () => {
            beforeEach(() => {
              const tableUtrv = createMultiRowTableUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [tableUtrv],
              });
            });

            it('should return default data if use system default', async () => {
              const { records } = await tabularReportGenerator.getDownloadData(downloadParams);
              expect(records).to.deep.eq([
                tableHeaders,
                [getNumericCellFormat({ value: '1' }), 'USD 0.0001 Millions', '1000 Cubic meters', '1 %', undefined, undefined],
                [getNumericCellFormat({ value: '2' }), 'USD 0.0002 Millions', '2000 Cubic meters', '2 %', undefined, undefined],
                [],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                tableHeaders,
                [getNumericCellFormat({ value: '1' }), 'USD 1 Hundred', '1 Megalitre', '1 %', undefined, undefined],
                [getNumericCellFormat({ value: '2' }), 'USD 2 Hundreds', '2 Megalitres', '2 %', undefined, undefined],
                [],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([
                tableHeaders,
                [
                  '0.001 Thousands',
                  'USD 0.1 Thousands',
                  '0.001 Million cubic meters',
                  '0.001 Thousands %',
                  undefined,
                  undefined,
                ],
                [
                  '0.002 Thousands',
                  'USD 0.2 Thousands',
                  '0.002 Million cubic meters',
                  '0.002 Thousands %',
                  undefined,
                  undefined,
                ],
                [],
              ]);
            });
          });

          describe('valueType: multi-row table - same unitType across columns', () => {
            beforeEach(() => {
              const tableUtrv = createMultiRowTableUtrv({
                utr: utrTableWithMatchingUnits,
                data: valueDataTableWithSameUnitType,
                inputData: valueDataInputTableWithSameUnitType,
              });
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [tableUtrv],
              });
            });

            it('should return default data if use system default', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                [
                  { options: { type: 'n' }, value: '1' },
                  { options: { type: 'n' }, value: '11' },
                  { options: { type: 'n' }, value: '12' },
                  unit.default,
                  '',
                ],
                [
                  { options: { type: 'n' }, value: '2' },
                  { options: { type: 'n' }, value: '21' },
                  { options: { type: 'n' }, value: '22' },
                  unit.default,
                  '',
                ],
                [{ options: { type: 'n' }, value: '3' }, '', '', unit.default, ''],
                [
                  { options: { type: 'n' }, value: '4' },
                  { options: { type: 'n' }, value: '41' },
                  { options: { type: 'n' }, value: '42' },
                  unit.default,
                  '',
                ],
                [
                  { options: { type: 'n' }, value: '5' },
                  { options: { type: 'n' }, value: '51' },
                  { options: { type: 'n' }, value: '52' },
                  unit.default,
                  '',
                ],
                [],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                // mix unit: Mcm | Ml | m3
                ['1000 Million cubic meters', '1001 Megalitres', '1002 Cubic meters', undefined, ''],
                // all matched unit
                [
                  { options: { type: 'n' }, value: '2000' },
                  { options: { type: 'n' }, value: '2001' },
                  { options: { type: 'n' }, value: '2002' },
                  unit.input,
                  '',
                ],
                // partial answered row -> matched unit
                [{ options: { type: 'n' }, value: '3000' }, '', '', unit.input, ''],
                // mix unit: Ml | Ml | Mcm
                ['4000 Megalitres', '4001 Megalitres', '4002 Million cubic meters', undefined, ''],
                // mix numberScale: hundreds | thousands | single
                [
                  '5000 Hundreds',
                  '5001 Thousands',
                  {
                    options: {
                      type: 'n',
                    },
                    value: '5002',
                  },
                  unit.input,
                  undefined,
                ],
                [],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await tabularReportGenerator.getDownloadData({
                ...downloadParams,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([
                ['Col 1', 'Col 2', 'Col 3', 'Unit', 'Number Scale'],
                // match numberScale
                [
                  '10 Million cubic meters',
                  '0.01001 Million cubic meters',
                  '10.02 Cubic meters',
                  undefined,
                  'hundreds',
                ],
                // match numberScale
                [
                  '0.02 Million cubic meters',
                  '0.02001 Million cubic meters',
                  '20.02 Megalitres',
                  undefined,
                  'hundreds',
                ],
                // partial answered row, match unit & numberScale
                [
                  {
                    options: {
                      format: '0.00',
                      type: 'n',
                    },
                    value: '0.03',
                  },
                  '',
                  '',
                  'Mcm',
                  'hundreds',
                ],
                // match unit & numberScale
                [
                  {
                    options: {
                      format: '0.00',
                      type: 'n',
                    },
                    value: '0.04',
                  },
                  {
                    options: {
                      format: '0.00000',
                      type: 'n',
                    },
                    value: '0.04001',
                  },
                  // no unit overrides but input's unit = unit override -> matched unit
                  {
                    options: {
                      format: '0.00',
                      type: 'n',
                    },
                    value: '40.02',
                  },
                  'Mcm',
                  'hundreds',
                ],
                // match numberScale
                ['5 Million cubic meters', '50.01 Million cubic meters', '50.02 Megalitres', undefined, 'hundreds'],
                [],
              ]);
            });
          });
        });
      });

      describe('isHistoryIncluded', () => {
        const columnCodes = ['updatedDate', 'status', 'input'];
        const columnHeaders = ['Updated Date', 'Status', 'Answer'];

        const downloadParams = {
          ...defaultDownloadParams,
          columns: columnCodes.map((c) => ({ code: c } as Column)),
        };
        const historyIncludedDownloadParams = {
          ...defaultDownloadParams,
          columns: columnCodes.map(
            (c) => ({ code: c, ...(c === 'input' ? { rule: InputColumnRule.All } : {}) } as Column)
          ),
        };

        describe('valueType: text', () => {
          beforeEach(() => {
            const textUtrv = createTextUtrv();
            baseReportDownload.getReportDataWithExtras.resolves({
              ...mockReportDataWithExtras,
              reportDataWithAltCode: [textUtrv] as CustomReportData[],
            });
          });

          it('should return the latest records only when history is not included', async () => {
            const { records } = await tabularReportGenerator.getDownloadData(downloadParams);
            expect(records).to.deep.eq([columnHeaders, ['25/12/2024 02:00:00', 'Verified', 'text latest'], []]);
          });

          it('should return the all history records when history is included', async () => {
            const { records } = await tabularReportGenerator.getDownloadData(historyIncludedDownloadParams);
            expect(records).to.deep.eq([
              columnHeaders,
              ['25/12/2024 01:00:00', 'Submitted', 'text oldest'],
              ['25/12/2024 02:00:00', 'Submitted', 'text latest'],
              ['25/12/2024 02:00:00', 'Verified', 'text latest'],
              [],
            ]);
          });
        });

        describe('valueType: valueList/valueListMulti', () => {
          beforeEach(() => {
            const valueListUtrv = createValueListUtrv();
            baseReportDownload.getReportDataWithExtras.resolves({
              ...mockReportDataWithExtras,
              valueLists: [yesNoList],
              reportDataWithAltCode: [valueListUtrv] as CustomReportData[],
            });
          });

          it('should return the latest records only when history is not included', async () => {
            const { records } = await tabularReportGenerator.getDownloadData(downloadParams);
            expect(records).to.deep.eq([columnHeaders, ['25/12/2024 02:00:00', 'Verified', 'Yes'], []]);
          });

          it('should return the all history records when history is included', async () => {
            const { records } = await tabularReportGenerator.getDownloadData(historyIncludedDownloadParams);

            expect(records).to.deep.eq([
              columnHeaders,
              ['25/12/2024 01:00:00', 'Submitted', 'No'],
              ['25/12/2024 02:00:00', 'Submitted', 'Yes'],
              ['25/12/2024 02:00:00', 'Verified', 'Yes'],
              [],
            ]);
          });
        });
      });
    });
  });
});
