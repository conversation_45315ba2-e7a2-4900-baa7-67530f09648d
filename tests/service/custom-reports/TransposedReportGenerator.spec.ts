import { expect } from 'chai';
import sinon from 'sinon';
import { AggregationGroupByRule, Column, InputColumnRule } from '../../../server/models/customReport';
import { ValueData } from '../../../server/models/public/universalTrackerValueType';
import { CustomReportData } from '../../../server/service/custom-report/ReportDataResolver';
import { ReportUtrv } from '../../../server/service/custom-report/reportTypes';
import { getTransposedReportDataResolver } from '../../../server/service/custom-report/TransposedReportDataResolver';
import { TransposedReportGenerator } from '../../../server/service/custom-report/TransposedReportGenerator';
import { blueprintDefaultUnitConfig } from '../../../server/service/units/unitTypes';
import { ActionList } from '../../../server/service/utr/constants';
import {
  createCurrencyNumberUtrv,
  createMultiRowTableUtrv,
  createMultiRowTableUtrvWithAggregatedRow,
  createNumberUtrv,
  createNumericValueListUtrv,
  createPercentageUtrv,
  createSingleRowTableUtrv,
  createTextUtrv,
  createUnitNumberUtrv,
  createValueListUtrv,
  defaultDownloadParams,
  downloadScopeWithOverrides,
  downloadScopeWithUserInput,
  numberScale,
  surveyId,
  unit,
} from '../../fixtures/customReportFixtures';
import { valueListTestTable, yesNoList } from '../../fixtures/valueListFixtures';
import { initiativeOneSimple, initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { SURVEY } from '../../../server/util/terminology';
import { getNumericCellFormat } from '../../fixtures/universalTrackerValueFixtures';
import { createHistory } from '../../factories/valueHistory';

describe('TransposedReportGenerator', () => {
  let baseReportDownload: any;
  const transposedReportDataResolver = getTransposedReportDataResolver();
  let transposedReportGenerator: TransposedReportGenerator;

  beforeEach(() => {
    baseReportDownload = {
      getReportDataWithExtras: sinon.stub(),
    };

    transposedReportGenerator = new TransposedReportGenerator(baseReportDownload, transposedReportDataResolver);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDownloadData', () => {
    const mockReportDataWithExtras = {
      reportDataWithAltCode: [],
      valueLists: [],
      utrTagMap: new Map(),
      assuranceMap: new Map(),
      surveyUnitConfigMap: new Map([[surveyId.toHexString(), blueprintDefaultUnitConfig]]),
      utrvAssurances: [],
    };
    const history = [createHistory({ action: ActionList.Created, value: undefined })];

    describe('headers', () => {
      beforeEach(() => {
        baseReportDownload.getReportDataWithExtras.resolves(mockReportDataWithExtras);
      });

      it('should return empty headers if columns is empty', async () => {
        const { headers } = await transposedReportGenerator.getDownloadData(defaultDownloadParams);
        expect(headers).to.deep.eq([]);
      });

      it('should return supported headers if columns is not empty', async () => {
        const { headers } = await transposedReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'mapping', 'surveyName', 'not-support'].map((c) => ({ code: c } as Column)),
        });
        expect(headers).to.deep.eq(['Code', 'Mapping', `${SURVEY.CAPITALIZED_SINGULAR} Name`]);
      });
    });

    describe('records', () => {
      it('should return empty records if data is empty', async () => {
        baseReportDownload.getReportDataWithExtras.resolves(mockReportDataWithExtras);
        const { records } = await transposedReportGenerator.getDownloadData(defaultDownloadParams);
        expect(records).to.deep.eq([]);
      });

      it('should return records if data is not empty', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            { typeCode: 'a', name: 'A', utrvs: [{ status: ActionList.Created, surveyId, history }] },
            { typeCode: 'b', name: 'B', utrvs: [{ status: ActionList.Created, surveyId, history }] },
          ] as CustomReportData[],
        });

        const { records } = await transposedReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input'].map((c) => ({ code: c } as Column)),
        });
        expect(records).to.deep.eq([
          ['a', 'A', undefined],
          ['b', 'B', undefined],
        ]);
      });

      it('should use surveyName when available', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            { typeCode: 'a', name: 'A', utrvs: [{ status: ActionList.Created, surveyId, surveyName: 'Test Survey', history }] },
          ] as CustomReportData[],
          initiativeMap: new Map([[initiativeOneSimpleId.toString(), initiativeOneSimple]]),
        });

        const { records } = await transposedReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input', 'surveyName'].map((c) => ({ code: c } as Column)),
        });
        expect(records).to.deep.eq([['a', 'A', undefined, 'Test Survey']]);
      });

      it('should fallback to generated name when surveyName is missing', async () => {
        baseReportDownload.getReportDataWithExtras.resolves({
          ...mockReportDataWithExtras,
          reportDataWithAltCode: [
            {
              typeCode: 'b',
              name: 'B',
              utrvs: [{ status: ActionList.Created, surveyId, initiativeId: initiativeOneSimpleId, history }],
            },
          ] as CustomReportData[],
          initiativeMap: new Map([[initiativeOneSimpleId.toString(), initiativeOneSimple]]),
        });

        const { records } = await transposedReportGenerator.getDownloadData({
          ...defaultDownloadParams,
          columns: ['code', 'name', 'input', 'surveyName'].map((c) => ({ code: c } as Column)),
        });
        expect(records).to.deep.eq([['b', 'B', undefined, initiativeOneSimple.name]]);
      });

      describe('unit and numberScale and input alignment - unitInput/numberScaleInput is set', () => {
        describe('unanswered', () => {
          const history = [createHistory({ action: ActionList.Created, value: undefined })];
          const utrvs = [
            {
              status: ActionList.Created,
              surveyId,
              history,
            },
          ] as ReportUtrv[];
          const tableUtrvs = [
            { status: ActionList.Created, surveyId, valueData: { table: [] }, history } as ValueData,
          ] as ReportUtrv[];
          const reportDataWithAltCode = [
            createNumberUtrv({ utrvs }),
            createUnitNumberUtrv({ utrvs }),
            createCurrencyNumberUtrv({ utrvs }),
            createPercentageUtrv({ utrvs }),
            createNumericValueListUtrv({ utrvs }),
            createSingleRowTableUtrv({ overrides: { utrvs: tableUtrvs } }),
          ];

          const columns = ['valueLabel', 'columnLabel', 'unit', 'numberScale'].map((c) => ({ code: c } as Column));

          it('should return default unit/numberScale if not use metric override preferences', async () => {
            baseReportDownload.getReportDataWithExtras.resolves({ ...mockReportDataWithExtras, reportDataWithAltCode });
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns,
            });

            const expectedRecords = [
              ['Number value label', undefined, '', ''],
              ['Number with unit value label', undefined, unit.default, ''],
              ['Number with currency/numberScale value label', undefined, 'USD', numberScale.default],
              ['Percentage value label', undefined, '%', ''],
              // numericValueList
              ['Numeric value list value label', 'Surface Water', unit.default, ''],
              ['Numeric value list value label', 'Ground Water', unit.default, ''],
              ['Numeric value list value label', 'Total', unit.default, ''],
              // table
              ['Numeric table value label', 'Col Num', '', ''],
              ['Numeric table value label', 'Col Currency', 'USD', numberScale.default],
              ['Numeric table value label', 'Col Unit', unit.default, ''],
              ['Numeric table value label', 'Col Percentage', '%', ''],
            ];
            expect(records).to.deep.eq(expectedRecords);
          });

          it('should return overridden unit/numberScale if use metric override preferences', async () => {
            baseReportDownload.getReportDataWithExtras.resolves({ ...mockReportDataWithExtras, reportDataWithAltCode });
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns,
              downloadScope: downloadScopeWithOverrides,
            });

            const expectedRecords = [
              ['Number value label', undefined, '', numberScale.override],
              ['Number with unit value label', undefined, unit.override, ''],
              ['Number with currency/numberScale value label', undefined, 'USD', numberScale.override],
              ['Percentage value label', undefined, '%', numberScale.override],
              // numericValueList
              ['Numeric value list value label', 'Surface Water', unit.override, ''],
              ['Numeric value list value label', 'Ground Water', unit.override, ''],
              ['Numeric value list value label', 'Total', unit.override, ''],
              // table
              ['Numeric table value label', 'Col Num', '', numberScale.override],
              ['Numeric table value label', 'Col Currency', 'USD', numberScale.override],
              ['Numeric table value label', 'Col Unit', unit.override, ''],
              ['Numeric table value label', 'Col Percentage', '%', numberScale.override],
            ];
            expect(records).to.deep.eq(expectedRecords);
          });
        });

        describe('answered', () => {
          describe('valueType: number', () => {
            describe('no unit, no numberScale', () => {
              beforeEach(() => {
                const numberUtrv = createNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [numberUtrv],
                });
              });

              const columns = ['input', 'numberScale'].map((c) => ({ code: c } as Column));

              it('should return user input data if use user input', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 456 }), '']]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 0.456 }), numberScale.override]]);
              });
            });

            describe('has unit', () => {
              beforeEach(() => {
                const unitNumberUtrv = createUnitNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [unitNumberUtrv],
                });
              });

              const columns = ['input', 'unit'].map((c) => ({ code: c } as Column));

              it('should return converted data if use system default', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 456000 }), unit.default]]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 456 }), unit.input]]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 0.456 }), unit.override]]);
              });
            });

            describe('has currency - numberScale', () => {
              beforeEach(() => {
                const currencyNumberUtrv = createCurrencyNumberUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [currencyNumberUtrv],
                });
              });

              const columns = ['input', 'unit', 'numberScale'].map((c) => ({ code: c } as Column));

              it('should return converted data if use system default', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 0.0456 }), 'USD', numberScale.default]]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 456 }), 'USD', numberScale.input]]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...defaultDownloadParams,
                  columns,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([[getNumericCellFormat({ value: 45.6 }), 'USD', numberScale.override]]);
              });
            });
          });

          describe('valueType: percentage', () => {
            beforeEach(() => {
              const percentageUtrv = createPercentageUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [percentageUtrv],
              });
            });

            const columns = ['input', 'unit', 'numberScale'].map((c) => ({ code: c } as Column));

            it('should return user input data if use user input', async () => {
              const { records } = await transposedReportGenerator.getDownloadData({
                ...defaultDownloadParams,
                columns,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([[getNumericCellFormat({ value: 20 }), '%', '']]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await transposedReportGenerator.getDownloadData({
                ...defaultDownloadParams,
                columns,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([[getNumericCellFormat({ value: 0.02 }), '%', numberScale.override]]);
            });
          });

          describe('valueType: numericValueList', () => {
            beforeEach(() => {
              const numericValueListUtrv = createNumericValueListUtrv();
              baseReportDownload.getReportDataWithExtras.resolves({
                ...mockReportDataWithExtras,
                reportDataWithAltCode: [numericValueListUtrv],
              });
            });

            const columns = ['columnLabel', 'input', 'unit'].map((c) => ({ code: c } as Column));

            it('should return user input data if use system default', async () => {
              const { records } = await transposedReportGenerator.getDownloadData({
                ...defaultDownloadParams,
                columns,
              });
              expect(records).to.deep.eq([
                ['Surface Water', getNumericCellFormat({ value: '4000' }), unit.default],
                ['Ground Water', getNumericCellFormat({ value: '5000' }), unit.default],
                ['Total', getNumericCellFormat({ value: 9000 }), unit.default],
              ]);
            });

            it('should return user input data if use user input', async () => {
              const { records } = await transposedReportGenerator.getDownloadData({
                ...defaultDownloadParams,
                columns,
                downloadScope: downloadScopeWithUserInput,
              });
              expect(records).to.deep.eq([
                ['Surface Water', getNumericCellFormat({ value: '4' }), unit.input],
                ['Ground Water', getNumericCellFormat({ value: '5' }), unit.input],
                ['Total', getNumericCellFormat({ value: 9 }), unit.input],
              ]);
            });

            it('should return converted data if use metric override preferences', async () => {
              const { records } = await transposedReportGenerator.getDownloadData({
                ...defaultDownloadParams,
                columns,
                downloadScope: downloadScopeWithOverrides,
              });
              expect(records).to.deep.eq([
                ['Surface Water', getNumericCellFormat({ value: 0.004 }), unit.override],
                ['Ground Water', getNumericCellFormat({ value: 0.005 }), unit.override],
                ['Total', getNumericCellFormat({ value: 0.009 }), unit.override],
              ]);
            });
          });

          describe('valueType: table', () => {
            const columns = ['row', 'columnLabel', 'input', 'unit', 'numberScale'].map((c) => ({ code: c } as Column));
            const downloadParams = { ...defaultDownloadParams, columns };

            describe('single-row table', () => {
              beforeEach(() => {
                const tableUtrv = createSingleRowTableUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [tableUtrv],
                });
              });

              it('should return user input data if use system default', async () => {
                const { records } = await transposedReportGenerator.getDownloadData(downloadParams);
                expect(records).to.deep.eq([
                  ['', 'Col Num', getNumericCellFormat({ value: '2' }), '', ''],
                  ['', 'Col Currency', getNumericCellFormat({ value: '0.0002', 'format': '0.0000' }), 'USD', numberScale.default],
                  ['', 'Col Unit', getNumericCellFormat({ value: '2000' }), unit.default, ''],
                  ['', 'Col Percentage', getNumericCellFormat({ value: '2' }), '%', ''],
                ]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([
                  ['', 'Col Num', getNumericCellFormat({ value: '2' }), '', ''],
                  ['', 'Col Currency', getNumericCellFormat({ value: '2' }), 'USD', numberScale.input],
                  ['', 'Col Unit', getNumericCellFormat({ value: '2' }), unit.input, ''],
                  ['', 'Col Percentage', getNumericCellFormat({ value: '2' }), '%', ''],
                ]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([
                  ['', 'Col Num', getNumericCellFormat({ value: 0.002, format: '0.000' }), '', numberScale.override],
                  ['', 'Col Currency', getNumericCellFormat({ value: 0.2, format: '0.0' }), 'USD', numberScale.override],
                  ['', 'Col Unit', getNumericCellFormat({ value: 0.002, format: '0.000' }), unit.override, ''],
                  ['', 'Col Percentage', getNumericCellFormat({ value: 0.002, format: '0.000' }), '%', numberScale.override],
                ]);
              });
            });

            describe('valueType: multi-row table', () => {
              beforeEach(() => {
                const tableUtrv = createMultiRowTableUtrv();
                baseReportDownload.getReportDataWithExtras.resolves({
                  ...mockReportDataWithExtras,
                  reportDataWithAltCode: [tableUtrv],
                });
              });

              it('should return user input data if use system default', async () => {
                const { records } = await transposedReportGenerator.getDownloadData(downloadParams);
                expect(records).to.deep.eq([
                  [1, 'Col Num', getNumericCellFormat({ value: '1' }), '', ''],
                  [1, 'Col Currency', getNumericCellFormat({ value: '0.0001', format: '0.0000' }), 'USD', numberScale.default],
                  [1, 'Col Unit', getNumericCellFormat({ value: '1000' }), unit.default, ''],
                  [1, 'Col Percentage', getNumericCellFormat({ value: '1' }), '%', ''],
                  [2, 'Col Num', getNumericCellFormat({ value: '2' }), '', ''],
                  [2, 'Col Currency', getNumericCellFormat({ value: '0.0002', format: '0.0000' }), 'USD', numberScale.default],
                  [2, 'Col Unit', getNumericCellFormat({ value: '2000' }), unit.default, ''],
                  [2, 'Col Percentage', getNumericCellFormat({ value: '2' }), '%', ''],
                ]);
              });

              it('should return user input data if use user input', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithUserInput,
                });
                expect(records).to.deep.eq([
                  [1, 'Col Num', getNumericCellFormat({ value: '1' }), '', ''],
                  [1, 'Col Currency', getNumericCellFormat({ value: '1' }), 'USD', numberScale.input],
                  [1, 'Col Unit', getNumericCellFormat({ value: '1' }), unit.input, ''],
                  [1, 'Col Percentage', getNumericCellFormat({ value: '1' }), '%', ''],
                  [2, 'Col Num', getNumericCellFormat({ value: '2' }), '', ''],
                  [2, 'Col Currency', getNumericCellFormat({ value: '2' }), 'USD', numberScale.input],
                  [2, 'Col Unit', getNumericCellFormat({ value: '2' }), unit.input, ''],
                  [2, 'Col Percentage', getNumericCellFormat({ value: '2' }), '%', ''],
                ]);
              });

              it('should return converted data if use metric override preferences', async () => {
                const { records } = await transposedReportGenerator.getDownloadData({
                  ...downloadParams,
                  downloadScope: downloadScopeWithOverrides,
                });
                expect(records).to.deep.eq([
                  [1, 'Col Num', getNumericCellFormat({ value: 0.001, format: '0.000' }), '', numberScale.override],
                  [
                    1,
                    'Col Currency',
                    getNumericCellFormat({ value: 0.1, format: '0.0' }),
                    'USD',
                    numberScale.override,
                  ],
                  [1, 'Col Unit', getNumericCellFormat({ value: 0.001, format: '0.000' }), unit.override, ''],
                  [
                    1,
                    'Col Percentage',
                    getNumericCellFormat({ value: 0.001, format: '0.000' }),
                    '%',
                    numberScale.override,
                  ],
                  [2, 'Col Num', getNumericCellFormat({ value: 0.002, format: '0.000' }), '', numberScale.override],
                  [
                    2,
                    'Col Currency',
                    getNumericCellFormat({ value: 0.2, format: '0.0' }),
                    'USD',
                    numberScale.override,
                  ],
                  [2, 'Col Unit', getNumericCellFormat({ value: 0.002, format: '0.000' }), unit.override, ''],
                  [
                    2,
                    'Col Percentage',
                    getNumericCellFormat({ value: 0.002, format: '0.000' }),
                    '%',
                    numberScale.override,
                  ],
                ]);
              });
            });
          });
        });
      });

      describe('isHistoryIncluded', () => {
        const columns = ['updatedDate', 'status', 'input'].map((c) => ({ code: c } as Column));
        const historyIncludedColumns: Column[] = [
          { code: 'updatedDate' },
          { code: 'status' },
          { code: 'input', rule: InputColumnRule.All },
        ];

        describe('valueType: text', () => {
          beforeEach(() => {
            const textUtrv = createTextUtrv();
            baseReportDownload.getReportDataWithExtras.resolves({
              ...mockReportDataWithExtras,
              reportDataWithAltCode: [textUtrv] as CustomReportData[],
            });
          });

          it('should return the latest records only when history is not included', async () => {
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns,
            });

            expect(records).to.deep.eq([['25/12/2024 02:00:00', 'Verified', 'text latest']]);
          });

          it('should return the all history records when history is included', async () => {
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns: historyIncludedColumns,
            });

            expect(records).to.deep.eq([
              ['25/12/2024 01:00:00', 'Submitted', 'text oldest'],
              ['25/12/2024 02:00:00', 'Submitted', 'text latest'],
              ['25/12/2024 02:00:00', 'Verified', 'text latest'],
            ]);
          });
        });

        describe('valueType: valueList/valueListMulti', () => {
          beforeEach(() => {
            const valueListUtrv = createValueListUtrv();
            baseReportDownload.getReportDataWithExtras.resolves({
              ...mockReportDataWithExtras,
              valueLists: [yesNoList],
              reportDataWithAltCode: [valueListUtrv] as CustomReportData[],
            });
          });

          it('should return the latest records only when history is not included', async () => {
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns,
            });

            expect(records).to.deep.eq([['25/12/2024 02:00:00', 'Verified', 'Yes']]);
          });

          it('should return the all history records when history is included', async () => {
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns: historyIncludedColumns,
            });

            expect(records).to.deep.eq([
              ['25/12/2024 01:00:00', 'Submitted', 'No'],
              ['25/12/2024 02:00:00', 'Submitted', 'Yes'],
              ['25/12/2024 02:00:00', 'Verified', 'Yes'],
            ]);
          });
        });
      });

      describe('groupByAggregationColumn', () => {
        const aggergationGroupByColumns: Column[] = [
          { code: 'columnLabel' },
          { code: 'status' },
          { code: 'input', rule: InputColumnRule.All },
          { code: 'aggregationGroupBy', rule: AggregationGroupByRule.Exclude },
        ];
        describe('valueType: table', () => {
          beforeEach(() => {
            const multiRowTableUtrv = createMultiRowTableUtrvWithAggregatedRow();
            baseReportDownload.getReportDataWithExtras.resolves({
              ...mockReportDataWithExtras,
              reportDataWithAltCode: [multiRowTableUtrv] as CustomReportData[],
              valueLists: [valueListTestTable],
            });
          });
          it('should return records grouped by aggregation columns', async () => {
            const { records } = await transposedReportGenerator.getDownloadData({
              ...defaultDownloadParams,
              columns: aggergationGroupByColumns,
            });

            // "Surface Water, Male":
            // Currency: 1 + 5 = 6
            // Unit: 1000 + 5000 = 6000
            // Percentage: (1 + 5) / 2 = 3
            // --------------------------------
            // "Ground Water, Female":
            // Currency: 2 + 3 = 5
            // Unit: 2000 + 3000 = 5000
            // Percentage: (2 + 3) / 2 = 2.5
            // --------------------------------
            // "Surface Water, Female":
            // Currency: 4
            // Unit: 4000
            // Percentage: 4
            expect(records).to.deep.eq([
              ['Col Currency', 'Verified', getNumericCellFormat({ value: 6 }), 'Surface Water, Male'],
              ['Col Unit', 'Verified', getNumericCellFormat({ value: 6000 }), 'Surface Water, Male'],
              ['Col Percentage', 'Verified', getNumericCellFormat({ value: 3 }), 'Surface Water, Male'],

              ['Col Currency', 'Verified', getNumericCellFormat({ value: 5 }), 'Ground Water, Female'],
              ['Col Unit', 'Verified', getNumericCellFormat({ value: 5000 }), 'Ground Water, Female'],
              // float number automatically adds format: '0.0' from string in getCellValueForTableInputWithFormat
              ['Col Percentage', 'Verified', getNumericCellFormat({ value: 2.5, format: '0.0' }), 'Ground Water, Female'],

              ['Col Currency', 'Verified', getNumericCellFormat({ value: 4 }), 'Surface Water, Female'],
              ['Col Unit', 'Verified', getNumericCellFormat({ value: 4000 }), 'Surface Water, Female'],
              ['Col Percentage', 'Verified', getNumericCellFormat({ value: 4 }), 'Surface Water, Female'],
            ]);
          });
        });
      });
    });
  });
});
