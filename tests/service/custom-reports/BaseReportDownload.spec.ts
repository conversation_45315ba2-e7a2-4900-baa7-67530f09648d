import { createSandbox } from 'sinon';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import { BaseReportDownload } from '../../../server/service/custom-report/BaseReportDownload';
import { DataScopeAccess } from '../../../server/models/dataShare';
import { ValueListRepository } from '../../../server/repository/ValueListRepository';
import { CustomTagManager } from '../../../server/service/metric/CustomTagManager';
import { AssuranceRepository } from '../../../server/repository/AssuranceRepository';
import { getBreadcrumbs } from '../../../server/util/string';
import { UserRepository } from '../../../server/repository/UserRepository';
import { userOne, userTwo } from '../../fixtures/userFixtures';

describe('BaseReportDownload', () => {
  const sandbox = createSandbox();
  let instance: BaseReportDownload;
  let initiativeUtrService: any;
  let initiativeTreeService: any;

  beforeEach(() => {
    initiativeUtrService = {
      getUtrOverridesMap: sandbox.stub(),
      getPipelineStagesOnOverride: sandbox.stub(),
    };
    initiativeTreeService = {
      getFullTreeInitiativeMap: sandbox.stub(),
    };
    instance = new BaseReportDownload(initiativeUtrService, initiativeTreeService);
  });

  afterEach(async () => {
    sandbox.restore();
  });

  describe('getReportDataWithExtras', () => {
    // TODO: write tests for other data.
    it('should return initiativeMap that contains subsidiaryHierarchy', async () => {
      sandbox.stub(ValueListRepository, 'findByIdsMin').resolves([]);
      sandbox.stub(CustomTagManager, 'getUtrTagMap').resolves(new Map());
      sandbox.stub(AssuranceRepository, 'getSurveyAssuranceUtrvOrganizations').resolves([]);
      sandbox.stub(AssuranceRepository, 'getSurveyAssuranceUtrvs').resolves([]);
      initiativeUtrService.getUtrOverridesMap.resolves(new Map());
      initiativeUtrService.getPipelineStagesOnOverride.resolves([]);

      const root = { _id: new ObjectId(), name: 'Root' };
      const child = { _id: new ObjectId(), name: 'Child', parentId: root._id };
      const grandChild = { _id: new ObjectId(), name: 'Grand Child', parentId: child._id };
      const expectation = new Map([
        [root._id.toString(), { ...root, subsidiaryHierarchy: getBreadcrumbs([root.name]) }],
        [child._id.toString(), { ...child, subsidiaryHierarchy: getBreadcrumbs([root.name, child.name]) }],
        [
          grandChild._id.toString(),
          { ...grandChild, subsidiaryHierarchy: getBreadcrumbs([root.name, child.name, grandChild.name]) },
        ],
      ]);

      initiativeTreeService.getFullTreeInitiativeMap.resolves(expectation);

      const { initiativeMap } = await instance.getReportDataWithExtras({
        surveys: [],
        downloadScope: { access: DataScopeAccess.Full, scope: {} },
        initiativeId: root._id,
      });

      expect(initiativeMap).to.deep.eq(expectation);
    });

    it('should return userMap if reporter column is included', async () => {
      sandbox.stub(ValueListRepository, 'findByIdsMin').resolves([]);
      sandbox.stub(CustomTagManager, 'getUtrTagMap').resolves(new Map());
      sandbox.stub(AssuranceRepository, 'getSurveyAssuranceUtrvOrganizations').resolves([]);
      sandbox.stub(AssuranceRepository, 'getSurveyAssuranceUtrvs').resolves([]);
      initiativeUtrService.getUtrOverridesMap.resolves(new Map());
      initiativeUtrService.getPipelineStagesOnOverride.resolves([]);

      const root = { _id: new ObjectId(), name: 'Root' };

      sandbox.stub(UserRepository, 'findByIds').resolves([userOne, userTwo]);

      const expectation = new Map([
        [userOne._id.toString(), userOne],
        [userTwo._id.toString(), userTwo],
      ]);

      const { userMap } = await instance.getReportDataWithExtras({
        surveys: [],
        downloadScope: { access: DataScopeAccess.Full, scope: {} },
        initiativeId: root._id,
        columns: [{ code: 'reporter' }],
      });

      expect(userMap).to.deep.eq(expectation);
    });
  });
});
