/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { extractImageFromUrl } from '../../../server/service/pptx-report/utils';

describe('pptx-report utils', () => {
  describe('extractImageFromUrl', () => {
    it('should extract filename from URL with complex path and md5Hash', () => {
      const url = 'https://storage.googleapis.com/my-bucket/subfolder/document.pdf?md5hash123456789';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('document.pdf');
    });

    it('should extract filename from URL with md5Hash containing forward slashes', () => {
      const url = 'https://example.com/bucket/image.png?abc/def/ghi123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.png');
    });

    it('should extract filename from URL with special characters in filename', () => {
      const url = 'https://example.com/bucket/my-file_name.with-special.chars.jpg?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('my-file_name.with-special.chars.jpg');
    });

    it('should extract filename from URL without query parameters', () => {
      const url = 'https://example.com/bucket/image.jpg';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.jpg');
    });

    it('should extract filename from URL with empty query parameters', () => {
      const url = 'https://example.com/bucket/image.jpg?';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.jpg');
    });

    it('should extract filename from URL with multiple query parameters', () => {
      const url = 'https://example.com/bucket/image.jpg?hash=abc123&version=1&timestamp=123456';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.jpg');
    });

    it('should extract filename from URL with question mark in md5Hash', () => {
      const url = 'https://example.com/bucket/image.jpg?abc?def123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.jpg');
    });

    it('should handle URL with filename starting with dot', () => {
      const url = 'https://example.com/bucket/.hidden-file.txt?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('.hidden-file.txt');
    });

    it('should handle URL with complex md5Hash containing multiple special characters', () => {
      const url = 'https://example.com/bucket/image.jpg?md5=abc/def?ghi&xyz=123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('image.jpg');
    });

    it('should handle URL with filename containing dots', () => {
      const url = 'https://example.com/bucket/my.file.with.many.dots.jpg?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('my.file.with.many.dots.jpg');
    });

    it('should extract filename from Google Cloud Storage URL format', () => {
      const url =
        'https://storage.googleapis.com/my-bucket-name/uploads/user123/profile-image.png?GoogleAccessId=<EMAIL>&Expires=1234567890&Signature=abc123def456';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('profile-image.png');
    });

    it('should extract filename from AWS S3 URL format', () => {
      const url =
        'https://my-bucket.s3.amazonaws.com/uploads/documents/report.pdf?AWSAccessKeyId=AKIAIOSFODNN7EXAMPLE&Expires=1234567890&Signature=abc123def456';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('report.pdf');
    });

    it('should extract filename from URL with encoded characters', () => {
      const url = 'https://example.com/bucket/my%20file%20name.jpg?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('my%20file%20name.jpg');
    });

    it('should extract filename from URL with numeric filename', () => {
      const url = 'https://example.com/bucket/123456789.jpg?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('123456789.jpg');
    });

    it('should extract filename from URL with UUID-like filename', () => {
      const url = 'https://example.com/bucket/550e8400-e29b-41d4-a716-************.png?hash123';
      const result = extractImageFromUrl(url);
      expect(result).to.equal('550e8400-e29b-41d4-a716-************.png');
    });
  });
});
