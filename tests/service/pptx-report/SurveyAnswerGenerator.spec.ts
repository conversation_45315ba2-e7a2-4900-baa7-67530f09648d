import { expect } from 'chai';
import Sinon, { createSandbox } from 'sinon';
import { ColumnType, UtrValueType, ValueValidationType } from '../../../server/models/public/universalTrackerType';
import { CT_STARTER_SLIDE_UTRS, ReportOffset } from '../../../server/service/pptx-report/templates/builders/constants';
import { SurveyAnswerGenerator } from '../../../server/service/pptx-report/templates/builders/SurveyAnswerGenerator';
import {
  InputMapData,
  TableInputData,
  ValueListInputData,
} from '../../../server/service/pptx-report/templates/PPTXTemplateInterface';
import { PPTXTemplateSurveyCache } from '../../../server/service/pptx-report/templates/PPTXTemplateSurveyCache';
import { PPTXTemplateSurveyCacheManager } from '../../../server/service/pptx-report/templates/PPTXTemplateSurveyCacheManager';
import { generateMapKey } from '../../../server/service/pptx-report/utils';
import { NumberScale, SupportedMeasureUnits, UnitConfig } from '../../../server/service/units/unitTypes';
import { createUtrv } from './utils';

describe('SurveyAnswerGenerator', () => {
  const sandbox = createSandbox();

  let surveyAnswerGenerator: SurveyAnswerGenerator;
  let survey: Sinon.SinonStubbedInstance<PPTXTemplateSurveyCache>;
  let prevSurvey: Sinon.SinonStubbedInstance<PPTXTemplateSurveyCache>;
  let repositoryManager: Sinon.SinonStubbedInstance<PPTXTemplateSurveyCacheManager>;

  describe('loadSlidesData', () => {
    beforeEach(() => {
      repositoryManager = sandbox.createStubInstance(PPTXTemplateSurveyCacheManager);
      survey = sandbox.createStubInstance(PPTXTemplateSurveyCache);
      prevSurvey = sandbox.createStubInstance(PPTXTemplateSurveyCache);
      repositoryManager.getCachedSurvey.returns(survey);
      repositoryManager.getCachedSurvey.withArgs(-1).returns(prevSurvey);
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should return a map of empty data', async () => {
      survey.getUTRV.resolves(undefined);
      prevSurvey.getUTRV.resolves(undefined);
      surveyAnswerGenerator = new SurveyAnswerGenerator(CT_STARTER_SLIDE_UTRS, repositoryManager);

      const result = await surveyAnswerGenerator.loadSlidesData();

      expect(result).to.be.instanceOf(Map);
      expect(result.size).to.equal(Object.keys(CT_STARTER_SLIDE_UTRS).length);

      Object.values(result).forEach(({ utrsInputMap, unit, numberScale }) => {
        expect(utrsInputMap).to.be.instanceOf(Map);
        expect(utrsInputMap.size).to.equal(0);
        expect(unit).to.equal(undefined);
        expect(numberScale).to.equal(undefined);
      });
    });

    describe('number utrs', () => {
      const slideId = 20;
      const utrCode = CT_STARTER_SLIDE_UTRS[slideId].utrs[0].utrCode;
      const universalTracker = { code: generateMapKey(utrCode), valueType: UtrValueType.Number };
      const universalTrackerWithUnit = {
        ...universalTracker,
        unitType: SupportedMeasureUnits.mass,
        unit: 'mt',
      };

      const testCases = [
        {
          name: 'should return user input if no unit/numberScale',
          utrv: createUtrv({
            universalTracker,
            valueData: { input: { value: 1000, unit: undefined, numberScale: undefined } },
            value: 1,
          }),
          prevUtrv: createUtrv({
            universalTracker,
            valueData: { input: { value: 2000, unit: undefined, numberScale: undefined } },
            value: 2,
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1000, unit: undefined, numberScale: undefined }],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                { value: 2000, unit: undefined, numberScale: undefined },
              ],
            ]),
            unit: undefined,
            numberScale: undefined,
          },
        },
        {
          name: 'should return default data if unit/numberScale is mismatched',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { value: 1000, unit: 'kg', numberScale: undefined } },
            unit: 'mt',
            value: 1,
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { value: 2000, unit: 'lb', numberScale: undefined } },
            unit: 'mt',
            value: 2,
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1, unit: 'mt', numberScale: undefined }],
              [generateMapKey(utrCode, ReportOffset.Previous), { value: 2, unit: 'mt', numberScale: undefined }],
            ]),
            unit: 'mt',
            numberScale: undefined,
          },
        },
        {
          name: 'should return user input if unit/numberScale is matched',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { value: 1000, unit: 'kg', numberScale: 'single' } },
            unit: 'mt',
            value: 1,
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { value: 2000, unit: 'kg', numberScale: undefined } },
            unit: 'mt',
            value: 2,
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1000, unit: 'kg', numberScale: 'single' }],
              [generateMapKey(utrCode, ReportOffset.Previous), { value: 2000, unit: 'kg', numberScale: undefined }],
            ]),
            unit: 'kg',
            numberScale: 'single',
          },
        },
        {
          name: 'should return user input if unit/numberScale is matched - ignore unanswered utrv',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { value: 1000, unit: 'kg', numberScale: 'single' } },
            unit: 'mt',
            value: 1,
          }),
          prevUtrv: createUtrv({ universalTracker: universalTrackerWithUnit }), // unanswered utrv
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1000, unit: 'kg', numberScale: 'single' }],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                { value: undefined, unit: 'mt', numberScale: undefined },
              ],
            ]),
            unit: 'kg',
            numberScale: 'single',
          },
        },
      ];

      testCases.forEach(({ name, utrv, prevUtrv, expectedResult }) => {
        it(name, async () => {
          survey.getUTRV.resolves(createUtrv(utrv));
          prevSurvey.getUTRV.resolves(createUtrv(prevUtrv));
          surveyAnswerGenerator = new SurveyAnswerGenerator(
            { [slideId]: CT_STARTER_SLIDE_UTRS[slideId] },
            repositoryManager
          );

          const result = await surveyAnswerGenerator.loadSlidesData();
          const { utrsInputMap = new Map(), unit, numberScale } = result.get(slideId) ?? {};
          expect(Array.from(utrsInputMap)).to.deep.equal(
            Array.from(expectedResult.utrsInputMap as Map<string, InputMapData>)
          );
          expect(unit).to.equal(expectedResult.unit);
          expect(numberScale).to.equal(expectedResult.numberScale);
        });
      });
    });

    describe('currency utr', () => {
      const slideId = 20;
      const utrCode = CT_STARTER_SLIDE_UTRS[slideId].utrs[0].utrCode;
      const currencyUtr = {
        code: generateMapKey(utrCode),
        valueType: UtrValueType.Number,
        unitType: SupportedMeasureUnits.currency,
        unit: 'USD',
        numberScale: NumberScale.Millions,
      };

      const testCases = [
        {
          name: 'should return current survey currency if previous survey does not exist',
          utrv: createUtrv({ universalTracker: currencyUtr }),
          prevUtrv: undefined,
          unitConfig: { currency: 'JPY' },
          prevUnitConfig: undefined,
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: undefined, unit: 'JPY', numberScale: NumberScale.Millions }],
            ]),
            unit: 'JPY',
            numberScale: NumberScale.Millions,
          },
        },
        {
          name: 'should return current survey currency if current survey / previous survey has the same currency',
          utrv: createUtrv({
            universalTracker: currencyUtr,
            valueData: { input: { value: 1000, unit: 'JPY', numberScale: NumberScale.Millions } },
          }),
          prevUtrv: createUtrv({
            universalTracker: currencyUtr,
            valueData: { input: { value: 2000, unit: 'CAD', numberScale: NumberScale.Millions } },
          }),
          unitConfig: { currency: 'JPY' },
          prevUnitConfig: { currency: 'CAD' },
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1000, unit: 'USD', numberScale: NumberScale.Millions }],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                { value: 2000, unit: 'USD', numberScale: NumberScale.Millions },
              ],
            ]),
            unit: 'USD',
            numberScale: NumberScale.Millions,
          },
        },
        {
          name: 'should return default utr currency if current survey / previous survey has different currency',
          utrv: createUtrv({
            universalTracker: currencyUtr,
            valueData: { input: { value: 1000, unit: 'JPY', numberScale: NumberScale.Millions } },
          }),
          prevUtrv: createUtrv({
            universalTracker: currencyUtr,
            valueData: { input: { value: 2000, unit: 'JPY', numberScale: NumberScale.Millions } },
          }),
          unitConfig: { currency: 'JPY' },
          prevUnitConfig: { currency: 'JPY' },
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), { value: 1000, unit: 'JPY', numberScale: NumberScale.Millions }],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                { value: 2000, unit: 'JPY', numberScale: NumberScale.Millions },
              ],
            ]),
            unit: 'JPY',
            numberScale: NumberScale.Millions,
          },
        },
      ];

      testCases.forEach(({ name, utrv, prevUtrv, unitConfig, prevUnitConfig, expectedResult }) => {
        it(name, async () => {
          survey.getUTRV.resolves(createUtrv(utrv));
          prevSurvey.getUTRV.resolves(createUtrv(prevUtrv));
          survey.getUnitConfig.resolves(unitConfig as UnitConfig);
          prevSurvey.getUnitConfig.resolves(prevUnitConfig as UnitConfig);
          surveyAnswerGenerator = new SurveyAnswerGenerator(
            { [slideId]: CT_STARTER_SLIDE_UTRS[slideId] },
            repositoryManager
          );

          const result = await surveyAnswerGenerator.loadSlidesData();
          const { utrsInputMap = new Map(), unit, numberScale } = result.get(slideId) ?? {};
          expect(Array.from(utrsInputMap)).to.deep.equal(
            Array.from(expectedResult.utrsInputMap as Map<string, InputMapData>)
          );
          expect(unit).to.equal(expectedResult.unit);
          expect(numberScale).to.equal(expectedResult.numberScale);
        });
      });
    });

    describe('table utrs', () => {
      const slideId = 24;
      const utrCode = CT_STARTER_SLIDE_UTRS[slideId].utrs[0].utrCode;
      const [colCode1, colCode2] = CT_STARTER_SLIDE_UTRS[slideId].utrs[0].columnCodes ?? [];
      const columns = [
        {
          type: ColumnType.Number,
          code: colCode1,
          name: 'Hazardous Waste Generated',
        },
        {
          type: ColumnType.Number,
          code: colCode2,
          name: 'Non-hazardous Waste Generated',
        },
      ];

      const columnsWithUnit = columns.map((col) => ({ ...col, unitType: 'mass', unit: 'mt' }));
      const universalTracker = {
        code: generateMapKey(utrCode),
        valueType: UtrValueType.Table,
        valueValidation: { table: { columns } },
      };

      const universalTrackerWithUnit = {
        ...universalTracker,
        valueValidation: { table: { columns: columnsWithUnit } },
      };

      const testCases = [
        {
          name: 'should return user input if no unit/numberScale',
          utrv: createUtrv({
            universalTracker,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 1000, unit: undefined, numberScale: undefined },
                    { code: colCode2, value: 1100, unit: undefined, numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 10 },
                  { code: colCode2, value: 11 },
                ],
              ],
            },
          }),
          prevUtrv: createUtrv({
            universalTracker,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 2000, unit: undefined, numberScale: undefined },
                    { code: colCode2, value: 2200, unit: undefined, numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 20 },
                  { code: colCode2, value: 22 },
                ],
              ],
            },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  [
                    { code: colCode1, value: 1000, unit: '', numberScale: '' },
                    { code: colCode2, value: 1100, unit: '', numberScale: '' },
                  ],
                ],
              ],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                [
                  [
                    { code: colCode1, value: 2000, unit: '', numberScale: '' },
                    { code: colCode2, value: 2200, unit: '', numberScale: '' },
                  ],
                ],
              ],
            ]),
            unit: undefined,
            numberScale: undefined,
          },
        },
        {
          name: 'should return default data if unit/numberScale is mismatched',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 1000, unit: 'kg', numberScale: undefined },
                    { code: colCode2, value: 1100, unit: 'mt', numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 10 },
                  { code: colCode2, value: 11 },
                ],
              ],
            },
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 2000, unit: 'kg', numberScale: undefined },
                    { code: colCode2, value: 2200, unit: 'kg', numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 20 },
                  { code: colCode2, value: 22 },
                ],
              ],
            },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  [
                    { code: colCode1, value: 10, unit: 'mt', numberScale: '' },
                    { code: colCode2, value: 11, unit: 'mt', numberScale: '' },
                  ],
                ],
              ],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                [
                  [
                    { code: colCode1, value: 20, unit: 'mt', numberScale: '' },
                    { code: colCode2, value: 22, unit: 'mt', numberScale: '' },
                  ],
                ],
              ],
            ]),
            unit: 'mt',
            numberScale: undefined,
          },
        },
        {
          name: 'should return user input if unit/numberScale is matched',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 1000, unit: 'kg', numberScale: undefined },
                    { code: colCode2, value: 1100, unit: 'kg', numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 10 },
                  { code: colCode2, value: 11 },
                ],
              ],
            },
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 2000, unit: 'kg', numberScale: undefined },
                    { code: colCode2, value: 2200, unit: 'kg', numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 20 },
                  { code: colCode2, value: 22 },
                ],
              ],
            },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  [
                    { code: colCode1, value: 1000, unit: 'kg', numberScale: '' },
                    { code: colCode2, value: 1100, unit: 'kg', numberScale: '' },
                  ],
                ],
              ],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                [
                  [
                    { code: colCode1, value: 2000, unit: 'kg', numberScale: '' },
                    { code: colCode2, value: 2200, unit: 'kg', numberScale: '' },
                  ],
                ],
              ],
            ]),
            unit: 'kg',
            numberScale: undefined,
          },
        },
        {
          name: 'should return user input if unit/numberScale is matched - ignore unanswered column',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: 1000, unit: 'kg', numberScale: undefined },
                    { code: colCode2, value: undefined, unit: 'mt', numberScale: undefined }, // unanswered column
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: 10 },
                  { code: colCode2, value: undefined },
                ],
              ],
            },
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: {
              input: {
                table: [
                  [
                    { code: colCode1, value: undefined, unit: 'mt', numberScale: undefined }, // unanswered column
                    { code: colCode2, value: 2200, unit: 'kg', numberScale: undefined },
                  ],
                ],
                unit: undefined,
                numberScale: undefined,
              },
              table: [
                [
                  { code: colCode1, value: undefined },
                  { code: colCode2, value: 22 },
                ],
              ],
            },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  [
                    { code: colCode1, value: 1000, unit: 'kg', numberScale: '' },
                    { code: colCode2, value: undefined, unit: 'mt', numberScale: '' },
                  ],
                ],
              ],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                [
                  [
                    { code: colCode1, value: undefined, unit: 'mt', numberScale: '' },
                    { code: colCode2, value: 2200, unit: 'kg', numberScale: '' },
                  ],
                ],
              ],
            ]),
            unit: 'kg',
            numberScale: undefined,
          },
        },
        {
          name: 'should return default unit/numberScale if utrv is unanswered',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { table: [] },
          }),
          prevUtrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  [
                    { code: colCode1, value: undefined, unit: 'mt', numberScale: '' },
                    { code: colCode2, value: undefined, unit: 'mt', numberScale: '' },
                  ],
                ],
              ],
              [
                generateMapKey(utrCode, ReportOffset.Previous),
                [
                  [
                    { code: colCode1, value: undefined, unit: 'mt', numberScale: '' },
                    { code: colCode2, value: undefined, unit: 'mt', numberScale: '' },
                  ],
                ],
              ],
            ]),
            unit: 'mt',
            numberScale: undefined,
          },
        },
      ];

      testCases.forEach(({ name, utrv, prevUtrv, expectedResult }) => {
        it(name, async () => {
          survey.getUTRV.resolves(createUtrv(utrv));
          prevSurvey.getUTRV.resolves(createUtrv(prevUtrv));
          surveyAnswerGenerator = new SurveyAnswerGenerator(
            { [slideId]: CT_STARTER_SLIDE_UTRS[slideId] },
            repositoryManager
          );

          const result = await surveyAnswerGenerator.loadSlidesData();
          const { utrsInputMap = new Map(), unit, numberScale } = result.get(slideId) ?? {};
          expect(Array.from(utrsInputMap)).to.deep.equal(
            Array.from(expectedResult.utrsInputMap as Map<string, TableInputData[][]>)
          );
          expect(unit).to.equal(expectedResult.unit);
          expect(numberScale).to.equal(expectedResult.numberScale);
        });
      });
    });

    describe('numericValueList utrs - prev year survey excluded', () => {
      const slideId = 21;
      const utrCode = CT_STARTER_SLIDE_UTRS[slideId].utrs[0].utrCode;
      const options = [
        { code: 'co', name: '(1) CO' },
        { code: 'nox', name: '(2) NOx (excluding N2O)' },
      ];
      const universalTracker = {
        code: generateMapKey(utrCode),
        valueType: UtrValueType.NumericValueList,
        valueValidation: { valueList: { type: ValueValidationType.List, list: options } },
      };

      const universalTrackerWithUnit = {
        ...universalTracker,
        unitType: SupportedMeasureUnits.mass,
        unit: 'mt',
      };

      const testCases = [
        {
          name: 'should return user input if no unit/numberScale',
          utrv: createUtrv({
            universalTracker,
            valueData: { input: { data: { co: '1000', nox: '1100' }, unit: undefined, numberScale: undefined }, data: { co: '10', nox: '11' } },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  { valueListCode: 'co', value: '1000', unit: undefined, numberScale: undefined },
                  { valueListCode: 'nox', value: '1100', unit: undefined, numberScale: undefined },
                ],
              ],
            ]),
            unit: undefined,
            numberScale: undefined,
          },
        },
        {
          name: 'should return user input if have unit/numberScale',
          utrv: createUtrv({
            universalTracker: universalTrackerWithUnit,
            valueData: { input: { data: { co: '1000', nox: '1100' }, unit: 'kg', numberScale: undefined }, data: { co: '10', nox: '11' } },
          }),
          expectedResult: {
            utrsInputMap: new Map([
              [
                generateMapKey(utrCode),
                [
                  { valueListCode: 'co', value: '1000', unit: 'kg', numberScale: undefined },
                  { valueListCode: 'nox', value: '1100', unit: 'kg', numberScale: undefined },
                ],
              ],
            ]),
            unit: 'kg',
            numberScale: undefined,
          },
        },
        {
          name: 'should return default unit/numberScale if utrv is unanswered',
          utrv: createUtrv({ universalTracker: universalTrackerWithUnit }),
          expectedResult: {
            utrsInputMap: new Map([
              [generateMapKey(utrCode), [{ valueListCode: '', value: undefined, unit: 'mt', numberScale: undefined }]],
            ]),
            unit: 'mt',
            numberScale: undefined,
          },
        },
      ];

      testCases.forEach(({ name, utrv, expectedResult }) => {
        it(name, async () => {
          survey.getUTRV.resolves(createUtrv(utrv));
          surveyAnswerGenerator = new SurveyAnswerGenerator(
            { [slideId]: CT_STARTER_SLIDE_UTRS[slideId] },
            repositoryManager
          );

          const result = await surveyAnswerGenerator.loadSlidesData();
          const { utrsInputMap = new Map(), unit, numberScale } = result.get(slideId) ?? {};
          expect(Array.from(utrsInputMap)).to.deep.equal(
            Array.from(expectedResult.utrsInputMap as Map<string, ValueListInputData[]>)
          );
          expect(unit).to.equal(expectedResult.unit);
          expect(numberScale).to.equal(expectedResult.numberScale);
        });
      });
    });
  });
});
