/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox, type SinonSandbox, type SinonStub } from 'sinon';
import { AISlideRecommendationBuilder } from '../../../../../server/service/pptx-report/templates/builders/AISlideRecommendationBuilder';
import { PPTXTemplateSurveyCacheManager } from '../../../../../server/service/pptx-report/templates/PPTXTemplateSurveyCacheManager';
import { type PPTXTemplateSurveyCache } from '../../../../../server/service/pptx-report/templates/PPTXTemplateSurveyCache';
import { type AIModel } from '../../../../../server/service/ai/models/AIModel';
import { ActionList } from '../../../../../server/types/constants';
import { createUtrv } from '../../utils';
import { UtrValueType } from '../../../../../server/models/public/universalTrackerType';
import { getAIModelFactory , AIModelType } from '../../../../../server/service/ai/AIModelFactory';
import { ObjectId } from 'bson';
import { SurveyAnswerGenerator } from '../../../../../server/service/pptx-report/templates/builders/SurveyAnswerGenerator';

describe('AISlideRecommendationBuilder', () => {
  let sandbox: SinonSandbox;
  const aiModel: AIModel = getAIModelFactory().getAiModel(AIModelType.ChatGPT);
  const repositoryManager = new PPTXTemplateSurveyCacheManager(new ObjectId(), new ObjectId());
  const surveyAnswerGenerator = new SurveyAnswerGenerator({}, repositoryManager);
  let surveyCache: PPTXTemplateSurveyCache;
  let builder: AISlideRecommendationBuilder;
  let parseCompletionStub: SinonStub;

  beforeEach(() => {
    sandbox = createSandbox();

    parseCompletionStub = sandbox.stub(aiModel, 'parseCompletion').resolves({ content: { result: [] } });

    // Mock survey cache
    surveyCache = {
      getUtrvsMap: sandbox.stub(),
    } as any;

    // Mock repository manager
    sandbox.stub(repositoryManager, 'getCachedSurvey').returns(surveyCache);

    builder = new AISlideRecommendationBuilder(aiModel, repositoryManager, surveyAnswerGenerator);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getSlideRecommendations', () => {
    it('should exclude private metrics from AI processing', async () => {
      const publicUtrv = createUtrv({
        status: ActionList.Verified,
        isPrivate: false,
        universalTracker: {
          code: 'public-metric',
          valueType: UtrValueType.Number,
          name: 'Public Metric',
          valueLabel: 'Public Value',
        },
      });

      const privateUtrv = createUtrv({
        status: ActionList.Verified,
        isPrivate: true,
        universalTracker: {
          code: 'private-metric',
          valueType: UtrValueType.Number,
          name: 'Private Metric',
          valueLabel: 'Private Value',
        },
      });

      const utrvMap = new Map([
        ['public-metric', publicUtrv],
        ['private-metric', privateUtrv],
      ]);
      
      (surveyCache.getUtrvsMap as sinon.SinonStub).resolves(utrvMap);

      await builder.getSlideRecommendations();

      expect(parseCompletionStub.calledOnce).to.be.true;
      const [prompts] = parseCompletionStub.firstCall.args;
      const promptContent = prompts[0].content;

      // The prompt should contain the public metric but not the private one
      expect(promptContent).to.include('public-metric');
      expect(promptContent).to.not.include('private-metric');
    });

    it('should return empty map when no UTRVs are available', async () => {
      (surveyCache.getUtrvsMap as sinon.SinonStub).resolves(null);
      const result = await builder.getSlideRecommendations();

      expect(result).to.be.instanceOf(Map);
      expect(result.size).to.equal(0);
      expect(parseCompletionStub.calledOnce).to.be.false;
    });

    it('should exclude UTRVs with Created status', async () => {
      const createdUtrv = createUtrv({
        status: ActionList.Created,
        isPrivate: false,
        universalTracker: {
          code: 'created-metric',
          valueType: UtrValueType.Number,
        },
      });

      const verifiedUtrv = createUtrv({
        status: ActionList.Verified,
        isPrivate: false,
        universalTracker: {
          code: 'verified-metric',
          valueType: UtrValueType.Number,
        },
      });

      const utrvMap = new Map([
        ['created-metric', createdUtrv],
        ['verified-metric', verifiedUtrv],
      ]);
      
      (surveyCache.getUtrvsMap as sinon.SinonStub).resolves(utrvMap);

      await builder.getSlideRecommendations();

      expect(parseCompletionStub.calledOnce).to.be.true;
      const [prompts] = parseCompletionStub.firstCall.args;
      const promptContent = prompts[0].content;

      // Only verified metric should be included
      expect(promptContent).to.include('verified-metric');
      expect(promptContent).to.not.include('created-metric');
    });

    it('should exclude UTRVs without universalTracker', async () => {
      const utrvWithoutTracker = createUtrv({
        status: ActionList.Verified,
        isPrivate: false,
        universalTracker: undefined,
      });

      const utrvWithTracker = createUtrv({
        status: ActionList.Verified,
        isPrivate: false,
        universalTracker: {
          code: 'valid-metric',
          valueType: UtrValueType.Number,
        },
      });

      const utrvMap = new Map([
        ['invalid-metric', utrvWithoutTracker],
        ['valid-metric', utrvWithTracker],
      ]);

      (surveyCache.getUtrvsMap as sinon.SinonStub).resolves(utrvMap);

      await builder.getSlideRecommendations();

      expect(parseCompletionStub.calledOnce).to.be.true;
      const [prompts] = parseCompletionStub.firstCall.args;
      const promptContent = prompts[0].content;

      // Only valid metric should be included
      expect(promptContent).to.include('valid-metric');
      expect(promptContent).to.not.include('invalid-metric');
    });
  });
});
