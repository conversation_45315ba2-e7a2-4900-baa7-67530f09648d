import { ObjectId } from 'bson';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import Initiative, { InitiativeTags } from '../../../server/models/initiative';
import InitiativeSettings, { InitiativeSettingsModel } from '../../../server/models/initiativeSettings';
import { InitiativeRepository } from '../../../server/repository/InitiativeRepository';
import { getDefaultConfig, SurveyConfigService } from '../../../server/service/initiative/SurveyConfigService';
import { blueprintDefaultUnitConfig, NumberScale, UnitConfig } from '../../../server/service/units/unitTypes';
import { createInitiative } from '../../fixtures/initiativeFixtures';
import { createMongooseModel } from '../../setup';

describe('SurveyConfigService', () => {
  const sandbox = createSandbox();

  const CURRENCY = { DEFAULT: 'USD', ROOT: 'GBP' };

  const root = createInitiative({ name: 'Root', tags: [InitiativeTags.Organization] });
  const child = createInitiative({ name: 'Child', tags: [], parentId: root._id });
  const grandChild = createInitiative({ name: 'Grand Child', tags: [], parentId: child._id });

  const defaultSurveyConfig = { ...getDefaultConfig(grandChild), isEnforced: false };

  const rootSettings: InitiativeSettingsModel = {
    _id: new ObjectId(),
    initiativeId: root._id,
    surveyConfig: {
      ...defaultSurveyConfig,
      unitConfig: { ...blueprintDefaultUnitConfig, numberScale: NumberScale.Single, currency: CURRENCY.ROOT },
    },
  };

  const childSettings: InitiativeSettingsModel = {
    _id: new ObjectId(),
    initiativeId: child._id,
    surveyConfig: {
      ...defaultSurveyConfig,
      unitConfig: { ...blueprintDefaultUnitConfig, numberScale: NumberScale.Hundreds },
    },
  };

  const grandChildSettings: InitiativeSettingsModel = {
    _id: new ObjectId(),
    initiativeId: grandChild._id,
    surveyConfig: {
      ...defaultSurveyConfig,
      unitConfig: { ...blueprintDefaultUnitConfig, numberScale: NumberScale.Thousands, currency: 'random-value' },
    },
  };

  const rootSettingsWithSubsidiaryEnforced = {
    ...rootSettings,
    surveyConfig: { ...rootSettings.surveyConfig, subsidiariesEnforced: true },
  };

  afterEach(() => {
    sandbox.restore();
  });

  describe('findByInitiative', () => {
    beforeEach(() => {
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel(grandChild));
      sandbox.stub(InitiativeRepository, 'getSortedParentsUntilRootOrganization').resolves([root, child]);
    });

    it('should return default unit config when no initiative settings are found', async () => {
      sandbox.stub(InitiativeSettings, 'find').returns(createMongooseModel([]));
      const grandChildSurveyConfig = await SurveyConfigService.findByInitiative(grandChild._id.toString());

      expect(grandChildSurveyConfig).to.deep.equal(defaultSurveyConfig);
    });

    it('should return config from initiative settings when present - use default root currency', async () => {
      sandbox.stub(InitiativeSettings, 'find').returns(createMongooseModel([grandChildSettings]));
      const grandChildSurveyConfig = await SurveyConfigService.findByInitiative(grandChild._id.toString());

      expect(grandChildSurveyConfig).to.deep.equal({
        ...grandChildSettings.surveyConfig,
        unitConfig: { ...grandChildSettings.surveyConfig?.unitConfig, currency: CURRENCY.DEFAULT },
        isEnforced: false,
      });
    });

    it('should return config from initiative settings - no enforcement from parents - use root currency', async () => {
      sandbox
        .stub(InitiativeSettings, 'find')
        .returns(createMongooseModel([rootSettings, childSettings, grandChildSettings]));
      const grandChildSurveyConfig = await SurveyConfigService.findByInitiative(grandChild._id.toString());

      expect(grandChildSurveyConfig).to.deep.equal({
        ...grandChildSettings.surveyConfig,
        unitConfig: { ...grandChildSettings.surveyConfig?.unitConfig, currency: CURRENCY.ROOT },
        isEnforced: false,
      });
    });

    it('should return config from inherited parent settings with enforcement enabled', async () => {
      sandbox
        .stub(InitiativeSettings, 'find')
        .returns(createMongooseModel([rootSettingsWithSubsidiaryEnforced, childSettings, grandChildSettings]));
      const grandChildSurveyConfig = await SurveyConfigService.findByInitiative(grandChild._id.toString());
      expect(grandChildSurveyConfig).to.deep.equal({
        ...rootSettingsWithSubsidiaryEnforced.surveyConfig,
        isEnforced: true,
      });
    });
  });

  describe('getInitiativeUnitConfigMap', () => {
    const testCases = [
      {
        initiativeSettings: [],
        initiativeIds: [root._id, child._id, grandChild._id],
        expectedResult: new Map(
          [root._id, child._id, grandChild._id].map((id) => [id.toString(), defaultSurveyConfig.unitConfig])
        ),
      },
      {
        initiativeSettings: [rootSettings],
        initiativeIds: [root._id],
        expectedResult: new Map([[root._id.toString(), rootSettings.surveyConfig?.unitConfig]]) as Map<
          string,
          UnitConfig
        >,
      },
      {
        initiativeSettings: [rootSettings, childSettings, grandChildSettings],
        initiativeIds: [child._id, grandChild._id],
        expectedResult: new Map([
          [child._id.toString(), { ...childSettings.surveyConfig?.unitConfig, currency: CURRENCY.ROOT }],
          [grandChild._id.toString(), { ...grandChildSettings.surveyConfig?.unitConfig, currency: CURRENCY.ROOT }],
        ]),
      },
      {
        initiativeSettings: [rootSettingsWithSubsidiaryEnforced, childSettings, grandChildSettings],
        initiativeIds: [child._id, grandChild._id],
        expectedResult: new Map([
          [child._id.toString(), { ...rootSettings.surveyConfig?.unitConfig }],
          [grandChild._id.toString(), { ...rootSettings.surveyConfig?.unitConfig }],
        ]),
      },
    ];

    testCases.forEach(({ initiativeIds, initiativeSettings, expectedResult }) => {
      it('should generate initiative unit config Map', async () => {
        sandbox.stub(InitiativeRepository, 'getOrganizationWithChildren').resolves([root, child, grandChild]);
        sandbox.stub(InitiativeSettings, 'find').returns(createMongooseModel(initiativeSettings));
        const unitConfigMap = await SurveyConfigService.getInitiativeUnitConfigMap(initiativeIds);
        expect(Array.from(unitConfigMap)).to.deep.equal(Array.from(expectedResult));
      });
    });
  });
});
