import * as chai from 'chai';
import { normalizeNamePath } from "../../../../server/service/initiative/export/utils"

describe('normalizeNamePath', () => {
  const testCases = [
    { desc: 'Empty string should return "_unnamed"', input: '', expect: '_unnamed' },
    { desc: 'String with only spaces should return "_unnamed"', input: '   ', expect: '_unnamed' },
    { desc: 'String with control characters should be removed', input: 'Valid\u0000Name', expect: 'ValidName' },
    { desc: 'String with leading/trailing spaces should be trimed', input: '  leading and trailing  ', expect: 'leading and trailing' },
    { desc: 'String with multiple spaces should be replaced with single underscores', input: 'file   name  test', expect: 'file_name_test' },
    { desc: 'String with invalid characters should remove them', input: 'file<>:"/\\|?*name', expect: 'filename' },
    { desc: 'String with mix of valid and invalid characters', input: 'test<>|name*with:special/chars?', expect: 'testnamewithspecialchars' },
    { desc: 'Reserved Windows filename should be prefixed with underscore', input: 'CON', expect: '_CON' },
    { desc: 'Reserved Windows filename with lowercase should be prefixed with underscore', input: 'prn', expect: '_prn' },
    { desc: 'Reserved Windows filename in a phrase should not be changed', input: 'CON_test', expect: 'CON_test' },
    { desc: 'Non-reserved filename should remain unchanged', input: 'valid_filename', expect: 'valid_filename' },
    { desc: 'String with numbers and underscores should remain unchanged', input: 'file_123_test', expect: 'file_123_test' },
    { desc: 'String containing multiple underscores should not be affected', input: 'file___test', expect: 'file___test' },
    { desc: 'Single valid character should remain unchanged', input: 'a', expect: 'a' },
    { desc: 'Long valid filename should remain unchanged', input: 'this_is_a_very_long_filename_with_no_issues', expect: 'this_is_a_very_long_filename_with_no_issues' },
    { desc: 'String with control characters and invalid characters should be sanitized', input: '\u0000file<>:"/\\|?*name\u0001', expect: 'filename' }
  ];

  testCases.forEach(({ desc, input, expect }) => {
    it(desc, () => {
      const result = normalizeNamePath(input);
      chai.expect(result).to.be.eq(expect);
    });
  });
})