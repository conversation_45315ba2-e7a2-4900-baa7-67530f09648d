/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { prepareNotificationEmail } from '../../../../server/service/notification/templates/email';
import { userOne } from '../../../fixtures/userFixtures';
import {
  NotificationCategory,
  type NotificationDataExpanded,
} from '../../../../server/service/notification/NotificationTypes';

describe('email templates', function () {


  it('should generate default template', function () {

    const data: NotificationDataExpanded = {
      domain: undefined,
      category: NotificationCategory.QuestionReject,
      title: 'Great',
      content: 'Rejected',
      recipients: [],
      appConfig: undefined
    }

    const { html } = prepareNotificationEmail(data, userOne)
    expect(html).to.be.not.null;
  });

  it('should render bottom content before CTA button', function () {
    const ctaButtonText = 'GO TO PLATFORM';
    const bottomContent = 'Root Initiative: 2 questions verified';

    const data: NotificationDataExpanded = {
      domain: 'g17.eco',
      category: NotificationCategory.QuestionReject,
      title: 'Test Title',
      content: '<p>Here is your 7 day summary of G17Eco notifications:</p>',
      recipients: [],
      appConfig: undefined,
      actionUrl: 'https://g17.eco/some-action',
      overrides: {
        email: {
          actionText: ctaButtonText,
          bottomContent: `<p>${bottomContent}</p>`,
        },
      },
    };

    const { html } = prepareNotificationEmail(data, userOne);

    expect(html).to.contain(bottomContent);
    expect(html).to.contain(ctaButtonText);

    const bottomContentPosition = html.indexOf(bottomContent);
    const ctaButtonPosition = html.lastIndexOf(ctaButtonText);

    expect(bottomContentPosition).to.be.greaterThan(-1);
    expect(bottomContentPosition).to.be.lessThan(ctaButtonPosition);
  });
});
