/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import Sinon, { createSandbox, assert, match } from 'sinon';
import { userOne, userTwo } from '../../fixtures/userFixtures';
import {
  NotificationCategory,
  NotificationModel,
  NotificationPage,
} from '../../../server/service/notification/NotificationTypes';
import { ObjectId } from 'bson';
import { TimePeriod } from '../../../server/util/date';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import Notification from '../../../server/models/notification';
import { wrapStub } from '../../setup';
import { getEmailScheduleService } from '../../../server/service/notification/EmailScheduleService';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { getRecipientsService } from '../../../server/service/notification/delivery/Recipients';
import { getEmailProvider } from '../../../server/service/notification/delivery/Email';
import NotificationSchedule, { NotificationScheduleExtended } from '../../../server/models/notification-schedule';

describe('EmailSchedulingService', () => {
  const service = getEmailScheduleService();
  const recipientsService = getRecipientsService();
  const emailProvider = getEmailProvider();

  const mockNotifications: NotificationModel[] = [
    {
      _id: new ObjectId(),
      title: 'Test Notification',
      content: 'This is a test notification',
      category: NotificationCategory.QuestionComment,
      topic: 'test-topic',
      customAttributes: {
        page: NotificationPage.SurveyOverview,
        domain: '',
        initiativeId: initiativeOneSimple._id.toHexString(),
        appConfigCode: '',
      },
      actionUrl: 'http://example.com',
      created: new Date(),
      recipients: [],
    },
  ];

  const mockSchedules: NotificationScheduleExtended[] = [
    {
      _id: new ObjectId(),
      userId: userOne._id,
      initiativeId: initiativeOneSimple._id,
      notificationIds: [new ObjectId(), new ObjectId()],
      initiative: initiativeOneSimple,
      notificationPreferences: {
        userId: userOne._id,
        magicBell: {
          notification_preferences: {
            categories: {},
          },
        },
        notificationSchedule: {
          isSummary: true,
          period: TimePeriod.Daily,
        },
      },
    },
    {
      _id: new ObjectId(),
      userId: userTwo._id,
      initiativeId: initiativeOneSimple._id,
      notificationIds: [new ObjectId(), new ObjectId(), new ObjectId()],
      initiative: initiativeOneSimple,
      notificationPreferences: {
        userId: userTwo._id,
        magicBell: {
          notification_preferences: {
            categories: {},
          },
        },
        notificationSchedule: {
          isSummary: true,
          period: TimePeriod.Hourly,
        },
      },
    },
  ];

  describe('sendSummary func', () => {
    const sandbox = createSandbox();

    afterEach(() => sandbox.restore());

    it('should create an email with correct data', async () => {
      const params = {
        userId: userOne._id,
        initiative: initiativeOneSimple,
        processNotifications: mockNotifications,
        period: TimePeriod.Weekly,
      };

      sandbox.stub(recipientsService, 'getRecipients').resolves([]);
      const createSpy = sandbox.spy(emailProvider, 'create');

      await service.sendSummary(params);

      assert.calledWith(
        createSpy,
        match({
          title: 'Weekly email summaries',
          content: 'Here is your 7 day summary of G17Eco notifications:',
          category: NotificationCategory.ScheduleEmailSummaries,
          recipients: [],
          domain: undefined,
          appConfig: undefined,
          overrides: {
            email: {
              title: '',
              actionText: 'GO TO PLATFORM',
              bottomContent: match(initiativeOneSimple.name),
            },
          },
        })
      );
    });
  });

  describe('processNotificationSchedules func', () => {
    const sandbox = createSandbox();

    afterEach(() => sandbox.restore());

    it('should not process anything when schedules array is empty', async () => {
      const loggerSpy = sandbox.spy(wwgLogger, 'info');

      await service.processNotificationSchedules([]);
      assert.notCalled(loggerSpy);
    });

    it('should process notification schedules correctly when valid data is provided', async () => {

      wrapStub(sandbox, Notification, 'aggregate', () => ({ exec: () => Promise.resolve(mockNotifications) }));
      wrapStub(sandbox, NotificationSchedule, 'findOneAndUpdate', () => Promise.resolve());
      const sendSummaryStub = sandbox.stub(service, 'sendSummary').resolves();

      await service.processNotificationSchedules(mockSchedules);

      mockSchedules.forEach((schedule) => {
        const { userId, initiative = initiativeOneSimple, notificationPreferences } = schedule;
        assert.calledWith(sendSummaryStub, {
          userId,
          initiative,
          processNotifications: mockNotifications,
          period: notificationPreferences?.notificationSchedule?.period ?? TimePeriod.Weekly,
        });
      });
    });
  });

  describe('process func', () => {
    const sandbox = createSandbox();
    let processNotificationSchedulesSpy: Sinon.SinonSpy;

    beforeEach(() => {
      processNotificationSchedulesSpy = sandbox.spy(service, 'processNotificationSchedules');
    })
    afterEach(() => sandbox.restore());

    it('should process correctly when notification schedules is an empty array', async () => {
      wrapStub(sandbox, NotificationSchedule, 'aggregate', () => ({ exec: () => Promise.resolve([]) }));
      await service.process();
      assert.calledWith(processNotificationSchedulesSpy, []);
    });

    it('should process correctly when notification schedules has value', async () => {
      wrapStub(sandbox, NotificationSchedule, 'aggregate', () => ({ exec: () => Promise.resolve(mockSchedules) }));
      await service.process();
      assert.calledWith(processNotificationSchedulesSpy, mockSchedules);
    });
  });
});
