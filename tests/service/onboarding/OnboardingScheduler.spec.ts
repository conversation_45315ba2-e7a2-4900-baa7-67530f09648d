import { createSandbox } from 'sinon';
import { getOnboardingRepository } from '../../../server/repository/OnboardingRepository';
import { getOnboardingEmailService } from '../../../server/service/onboarding/OnboardingEmailService';
import { OnboardingScheduler } from '../../../server/service/onboarding/OnboardingScheduler';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { ObNotificationCode, ObNotificationService, ObType } from '../../../server/models/onboarding';
import { onboardingOne } from '../../fixtures/onboardingFixtures';
import { expect } from 'chai';
import { organizationOneId } from '../../fixtures/organization';

describe('OnboardingScheduler', () => {
  const obEmailService = getOnboardingEmailService();
  const obRepo = getOnboardingRepository();
  const sandbox = createSandbox();

  const schedulerService = new OnboardingScheduler(obEmailService, obRepo, wwgLogger);
  describe('process', () => {
    afterEach(() => sandbox.restore());

    it('should process initiative onboardings', async () => {
      sandbox.stub(obRepo, 'find').resolves([
        {
          ...onboardingOne,
          type: ObType.Initiative,
          save: async function () {
            return this;
          },
          populate: async function () {
            return this;
          },
          notifications: {
            items: [],
            service: ObNotificationService.Local,
          },
        } as any,
      ]);
      const followUpStub = sandbox.stub(obEmailService, 'handleFollowUpEmail');
      await schedulerService.process({ notificationCode: ObNotificationCode.FirstReminder });
      expect(followUpStub.calledOnce).to.be.true;
    });

    it('should process organization onboardings', async () => {
      sandbox.stub(obRepo, 'find').resolves([
        {
          ...onboardingOne,
          organizationId: organizationOneId,
          type: ObType.Organization,
          save: async function () {
            return this;
          },
          populate: async function () {
            return this;
          },
          notifications: {
            items: [],
            service: ObNotificationService.Local,
          },
        } as any,
      ]);
      const followUpStub = sandbox.stub(obEmailService, 'handleFollowUpEmail');
      await schedulerService.process({ notificationCode: ObNotificationCode.FirstReminder });
      expect(followUpStub.calledOnce).to.be.true;
    });
  });
});
