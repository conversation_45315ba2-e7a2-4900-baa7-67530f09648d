/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as sinon from 'sinon';
import { expect } from 'chai';
import { createOnboardingCrmService } from '../../../server/service/onboarding/OnboardingCrmManager';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { OnboardingStatus, OrganizationOnboardingModel } from '../../../server/models/onboarding';
import { createUserEventService } from '../../../server/service/event/UserEventService';
import { InitiativePermissions } from '../../../server/service/initiative/InitiativePermissions';
import { createMongooseModel } from '../../setup';
import Initiative from '../../../server/models/initiative';
import { ObjectId } from 'bson';
import { OrganizationOnboardingManager } from '../../../server/service/onboarding/OrganizationOnboardingManager';
import { getOrganizationOnboardingRepository } from '../../../server/repository/OrganizationOnboardingRepository';
import { OrganizationPermission } from '../../../server/models/assurancePermission';
import { OrganizationEmailTemplate } from '../../../server/service/email/templates/onboarding/organizationInitial';
import ContextError from '../../../server/error/ContextError';

describe('onboardingManager', () => {
  const sandbox = sinon.createSandbox();

  const onboardingCrmService = createOnboardingCrmService();
  const userEventService = createUserEventService();
  const organizationOnboardingRepo = getOrganizationOnboardingRepository();

  const onboardingManager = new OrganizationOnboardingManager(
    onboardingCrmService,
    wwgLogger,
    userEventService,
    organizationOnboardingRepo
  );

  let userModelPermissionsStub: sinon.SinonStub;
  let crmServiceStub: sinon.SinonStub;
  let userEventsStub: sinon.SinonStub;

  before(() => {
    userModelPermissionsStub = sandbox.stub(InitiativePermissions, 'canAccess');
    userModelPermissionsStub.resolves(true);

    crmServiceStub = sandbox.stub(onboardingCrmService, 'onboard');
    crmServiceStub.callsFake(() => Promise.resolve({}));

    crmServiceStub = sandbox.stub(onboardingCrmService, 'removeOnboarding');
    crmServiceStub.callsFake(() => Promise.resolve({}));

    userEventsStub = sandbox.stub(userEventService, 'emit');
    userEventsStub.callsFake(() => Promise.resolve(true));

    sandbox.stub(Initiative, 'find').returns(createMongooseModel([]));
  });

  after(() => sandbox.restore());

  describe('getEmailTemplate', () => {
    it('should return Manager template for multiple permissions including Admin', () => {
      const admin = [OrganizationPermission.Admin, OrganizationPermission.Assurer];
      expect(onboardingManager.getEmailTemplate(admin)).to.eq(OrganizationEmailTemplate.Manager);
    });
    it('should return User template for Assurer or Viewer permission', () => {
      const assurer = [OrganizationPermission.Assurer];
      expect(onboardingManager.getEmailTemplate(assurer)).to.eq(OrganizationEmailTemplate.User);
      const viewer = [OrganizationPermission.Viewer];
      expect(onboardingManager.getEmailTemplate(viewer)).to.eq(OrganizationEmailTemplate.User);
    });
    it('should return Guest template for invalid permission or no permission', () => {
      const invalid: OrganizationPermission[] = ['InvalidPermission' as any];
      expect(onboardingManager.getEmailTemplate(invalid)).to.eq(OrganizationEmailTemplate.Guest);
      const empty: OrganizationPermission[] = [];
      expect(onboardingManager.getEmailTemplate(empty)).to.eq(OrganizationEmailTemplate.Guest);
    });
  });

  describe('onboardEmail', () => {
    it('should throw an error if permissions are missing', async () => {
      const email = '<EMAIL>';
      const organization = { _id: new ObjectId() };
      const delegator = { _id: new ObjectId() };
      const permissions: OrganizationPermission[] = [];

      await expect(
        onboardingManager.onboardEmail({ email, organization, delegator, permissions })
      ).to.be.eventually.rejectedWith(ContextError, /permissions are required/);
    });
  });

  it('should update existing onboardings and start them if they are not started', async () => {
    const email = '<EMAIL>';
    const organization = { _id: new ObjectId() };
    const delegator = { _id: new ObjectId() };
    const permissions = [OrganizationPermission.Admin];
    const existingOnboardings = [
      {
        _id: new ObjectId(),
        organizationRoles: [
          {
            modelId: organization._id,
            permissions: [OrganizationPermission.Viewer],
          },
        ],
        status: OnboardingStatus.Pending,
        save: async function () {
          return this;
        },
      },
      {
        _id: new ObjectId(),
        organizationRoles: [
          {
            modelId: organization._id,
            permissions: [OrganizationPermission.Assurer],
          },
        ],
        status: OnboardingStatus.Pending,
        save: async function () {
          return this;
        },
      },
    ] as unknown as OrganizationOnboardingModel[];

    const findExistingByOrganizationIdStub = sinon
      .stub(organizationOnboardingRepo, 'findExistingByOrganizationId')
      .resolves(existingOnboardings);

    await onboardingManager.onboardEmail({ email, organization, delegator, permissions });

    expect(findExistingByOrganizationIdStub.calledOnce).to.be.true;
    expect(findExistingByOrganizationIdStub.firstCall.args[0]).to.deep.equal({
      email,
      organizationId: organization._id,
    });

    existingOnboardings.forEach((onboarding) => {
      const updatedPermissions = onboarding.organizationRoles[0].permissions;
      expect(updatedPermissions).to.deep.equal(Array.from(new Set([...updatedPermissions, ...permissions])));
      expect(onboarding.status).to.equal(OnboardingStatus.Pending);
    });
  });
});
