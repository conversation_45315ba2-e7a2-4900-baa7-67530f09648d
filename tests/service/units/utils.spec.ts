import { expect } from 'chai';
import { checkMatchedNumberScale, checkMatchedUnit } from '../../../server/service/units/utils';

describe('Units utils', () => {
  describe('checkMatchedUnit', () => {
    const testCases = [
      {
        values: [],
        expected: true,
      },
      {
        values: [undefined],
        expected: true,
      },
      {
        values: ['', undefined],
        expected: true,
      },
      {
        values: ['mt', 'mt'],
        expected: true,
      },
      {
        values: ['mt', 'kg'],
        expected: false,
      },
      {
        values: ['mt', undefined],
        expected: false,
      },
    ];

    testCases.forEach(({ values, expected }) => {
      it(`should return ${expected} for given values`, () => {
        const result = checkMatchedUnit(new Set(values));
        expect(result).to.equal(expected);
      });
    });
  });

  describe('checkMatchedNumberScale', () => {
    const testCases = [
      {
        values: [],
        expected: true,
      },
      {
        values: [undefined],
        expected: true,
      },
      {
        values: ['', undefined, 'single'],
        expected: true,
      },
      {
        values: ['millions', 'millions'],
        expected: true,
      },
      {
        values: ['millions', 'thousands'],
        expected: false,
      },
      {
        values: ['millions', undefined],
        expected: false,
      },
    ];

    testCases.forEach(({ values, expected }) => {
      it(`should return ${expected} for given values`, () => {
        const result = checkMatchedNumberScale(new Set(values));
        expect(result).to.equal(expected);
      });
    });
  });
});
