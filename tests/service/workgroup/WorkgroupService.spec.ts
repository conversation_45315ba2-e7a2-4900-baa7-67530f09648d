import * as sinon from 'sinon';
import { WorkgroupService } from '../../../server/service/workgroup/WorkgroupService';
import { ObjectId } from 'bson';
import { createMongooseModel } from '../../setup';
import { expect } from 'chai';
import { Permission } from '../../../server/models/workgroup';
import UserError from '../../../server/error/UserError';
import { createInitiative } from '../../fixtures/initiativeFixtures';
import { FeatureCode } from '@g17eco/core';

describe('WorkgroupService', () => {
  let instance: WorkgroupService;
  let rootInitiativeService: any;
  let workgroupModel: any;
  let featureUsageService: any;

  const initiative = createInitiative();
  const initiativeId = initiative._id;

  beforeEach(() => {
    workgroupModel = {
      find: sinon.stub(),
      findOne: sinon.stub(),
      create: sinon.stub(),
      updateOne: sinon.stub(),
      deleteOne: sinon.stub(),
    };

    rootInitiativeService = {
      getOrganizationById: sinon.stub(),
    };

    featureUsageService = {
      getUsage: sinon.stub(),
    };

    instance = new WorkgroupService(workgroupModel, rootInitiativeService, featureUsageService);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getWorkgroups', () => {
    it('should get workgroups from root initiative', async () => {
      rootInitiativeService.getOrganizationById.resolves(initiative);
      const workgroups = [
        { _id: new ObjectId(), initiativeId },
        { _id: new ObjectId(), initiativeId },
      ];
      workgroupModel.find.returns(createMongooseModel(workgroups));

      const result = await instance.getWorkgroups({ initiativeId: new ObjectId() });

      expect(workgroupModel.find.calledOnceWith({ initiativeId })).to.be.true;
      expect(result).to.deep.equal(workgroups);
    });
  });

  describe('createWorkgroup', () => {
    const creatorId = new ObjectId();
    const data = { name: 'test', description: 'test', icon: 'test', color: 'test' };

    it('should create a workgroup when usage is within limit', async () => {
      const workgroup = { _id: new ObjectId(), initiativeId, creatorId, ...data };
      featureUsageService.getUsage.resolves({ currentUsage: 0, limit: 5 });
      workgroupModel.create.returns(workgroup);

      const result = await instance.createWorkgroup({ initiative, creatorId, data });

      expect(
        featureUsageService.getUsage.calledOnceWith({ rootInitiative: initiative, featureCode: FeatureCode.Workgroups })
      ).to.be.true;
      expect(workgroupModel.create.calledOnceWith({ ...data, initiativeId, creatorId, users: [] })).to.be.true;
      expect(result).to.deep.equal(workgroup);
    });

    it('should throw UserError when workgroup limit is reached', async () => {
      featureUsageService.getUsage.resolves({ currentUsage: 5, limit: 5 });

      await expect(instance.createWorkgroup({ initiative, creatorId, data })).to.be.rejectedWith(
        UserError,
        'You have reached the maximum number of workgroups (5) for your plan.'
      );
      expect(
        featureUsageService.getUsage.calledOnceWith({ rootInitiative: initiative, featureCode: FeatureCode.Workgroups })
      ).to.be.true;
      expect(workgroupModel.create.notCalled).to.be.true;
    });
  });

  describe('updateWorkgroup', () => {
    it('should update a workgroup', async () => {
      const initiativeId = new ObjectId();
      const workgroupId = new ObjectId();
      const data = { name: 'test', description: 'test', icon: 'test', color: 'test' };
      workgroupModel.updateOne.returns(createMongooseModel({ acknowledged: true }));

      const result = await instance.updateWorkgroup({ initiativeId, workgroupId, data });

      expect(workgroupModel.updateOne.calledOnceWith({ _id: workgroupId, initiativeId }, { $set: data })).to.be.true;
      expect(result.acknowledged).to.be.true;
    });
  });

  describe('deleteWorkgroup', () => {
    it('should delete a workgroup', async () => {
      const initiativeId = new ObjectId();
      const workgroupId = new ObjectId();
      workgroupModel.deleteOne.returns(createMongooseModel({ acknowledged: true }));

      const result = await instance.deleteWorkgroup({ initiativeId, workgroupId });

      expect(workgroupModel.deleteOne.calledOnceWith({ _id: workgroupId, initiativeId })).to.be.true;
      expect(result.acknowledged).to.be.true;
    });
  });

  describe('duplicateWorkgroup', () => {
    const workgroupId = new ObjectId();
    const creatorId = new ObjectId();
    const duplicatedData = {
      name: 'test',
      description: 'test',
      icon: 'test',
      color: 'test',
      users: [{ _id: new ObjectId(), permissions: [Permission.User] }],
    };

    it('should duplicate a workgroup when usage is within limit', async () => {
      const workgroup = { _id: workgroupId, initiativeId, creatorId: new ObjectId(), ...duplicatedData };
      const duplicatedWorkgroup = {
        _id: new ObjectId(),
        ...duplicatedData,
        initiativeId,
        creatorId,
        name: `Copy of ${duplicatedData.name}`,
      };
      featureUsageService.getUsage.resolves({ currentUsage: 0, limit: 5 });
      workgroupModel.findOne.returns(createMongooseModel(workgroup));
      workgroupModel.create.returns(duplicatedWorkgroup);

      const result = await instance.duplicateWorkgroup({ initiative, workgroupId, creatorId });

      expect(
        featureUsageService.getUsage.calledOnceWith({ rootInitiative: initiative, featureCode: FeatureCode.Workgroups })
      ).to.be.true;
      expect(workgroupModel.findOne.calledOnceWith({ _id: workgroupId, initiativeId })).to.be.true;
      expect(
        workgroupModel.create.calledOnceWith({
          ...duplicatedData,
          initiativeId,
          creatorId,
          name: `Copy of ${duplicatedData.name}`,
        })
      ).to.be.true;
      expect(result).to.deep.equal(duplicatedWorkgroup);
    });

    it('should throw UserError when workgroup limit is reached', async () => {
      featureUsageService.getUsage.resolves({ currentUsage: 5, limit: 5 });

      await expect(instance.duplicateWorkgroup({ initiative, workgroupId, creatorId })).to.be.rejectedWith(
        UserError,
        'You have reached the maximum number of workgroups (5) for your plan.'
      );
      expect(
        featureUsageService.getUsage.calledOnceWith({ rootInitiative: initiative, featureCode: FeatureCode.Workgroups })
      ).to.be.true;
      expect(workgroupModel.findOne.notCalled).to.be.true;
      expect(workgroupModel.create.notCalled).to.be.true;
    });
  });

  describe('addUsersToWorkgroup', () => {
    it('should add inexistent users to a workgroup', async () => {
      const workgroupId = new ObjectId();
      const userId1 = new ObjectId();
      const userId2 = new ObjectId();
      const workgroup = { _id: workgroupId, users: [{ _id: userId1, permissions: [Permission.User] }] };
      workgroupModel.findOne.returns(createMongooseModel(workgroup));
      workgroupModel.updateOne.returns(createMongooseModel({ acknowledged: true }));

      const result = await instance.addUsersToWorkgroup({ initiativeId, workgroupId, userIds: [userId1, userId2] });

      expect(workgroupModel.findOne.calledOnceWith({ _id: workgroupId, initiativeId })).to.be.true;
      expect(
        workgroupModel.updateOne.calledOnceWith(
          { _id: workgroupId, initiativeId },
          { $addToSet: { users: { $each: [{ _id: userId2, permissions: [Permission.User] }] } } }
        )
      ).to.be.true;
      expect(result.acknowledged).to.be.true;
    });
  });

  describe('removeUserFromWorkgroup', () => {
    it('should remove user from workgroup', async () => {
      const workgroupId = new ObjectId();
      const userId = new ObjectId();
      workgroupModel.updateOne.returns(createMongooseModel({ acknowledged: true }));

      const result = await instance.removeUserFromWorkgroup({ initiativeId, workgroupId, userId });

      expect(
        workgroupModel.updateOne.calledOnceWith(
          { _id: workgroupId, initiativeId },
          { $pull: { users: { _id: userId } } }
        )
      ).to.be.true;
      expect(result.acknowledged).to.be.true;
    });
  });
});