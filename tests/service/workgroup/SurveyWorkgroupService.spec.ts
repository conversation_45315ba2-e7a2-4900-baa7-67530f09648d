import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import { SurveyWorkgroupService } from '../../../server/service/workgroup/SurveyWorkgroupService';
import { createMongooseModel } from '../../setup';
import { SurveyModel, SurveyPermissionType } from '../../../server/models/survey';
import { DataScopeAccess } from '../../../server/models/dataShare';
import { SurveyUserRoles } from '../../../server/types/roles';
import { Workgroup } from '../../../server/models/workgroup';
import { getBluePrintContribution } from '../../../server/service/survey/BlueprintContribution';
import { createScopeUtrv } from '../../fixtures/universalTrackerValueFixtures';
import { createSurveyPermission } from '../../fixtures/survey';
import { getScopeUtrvsService } from '../../../server/service/survey/ScopeUtrvsService';
import { UniversalTrackerValueRepository } from '../../../server/repository/UniversalTrackerValueRepository';
import { testLogger } from '../../factories/logger';

describe('SurveyWorkgroupService', () => {
  const logger = testLogger;
  const utrvRepo = UniversalTrackerValueRepository;
  const workgroupModel = Workgroup;
  const scopeUtrvsService = getScopeUtrvsService();
  const blueprintContribution = getBluePrintContribution();
  const instance = new SurveyWorkgroupService(
    logger,
    utrvRepo,
    workgroupModel,
    scopeUtrvsService,
    blueprintContribution
  );
  const sandbox = sinon.createSandbox();

  beforeEach(() => {
    sandbox.stub(blueprintContribution, 'getContributions').resolves({});
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getWorkgroups', () => {
    it('should get survey workgroups including permission', async () => {
      const workgroup1 = { _id: new ObjectId() };
      const workgroup2 = { _id: new ObjectId() };
      const survey = {
        permissions: [
          { type: SurveyPermissionType.Workgroup, modelId: workgroup1._id },
          { type: SurveyPermissionType.Workgroup, modelId: workgroup2._id },
          { type: 'survey', modelId: new ObjectId() },
        ],
      } as SurveyModel;
      const workgroups = [workgroup1, workgroup2];

      sandbox.stub(workgroupModel, 'find').returns(createMongooseModel(workgroups));
      sandbox.stub(utrvRepo, 'getScopeUtrvs').resolves([]);
      const result = await instance.getWorkgroups({ survey });

      expect(result).to.deep.equal([
        { ...workgroup1, permission: survey.permissions?.[0], utrvIds: [] },
        { ...workgroup2, permission: survey.permissions?.[1], utrvIds: [] },
      ]);
    });

    it('should filter workgroups by userIds', async () => {
      const user1 = { _id: new ObjectId() };
      const user2 = { _id: new ObjectId() };
      const workgroup1 = { _id: new ObjectId(), name: 'wg1', users: [{ _id: user1._id }] };
      const workgroup2 = { _id: new ObjectId(), name: 'wg2', users: [{ _id: user2._id }] };
      const survey = {
        permissions: [
          createSurveyPermission({ modelId: workgroup1._id }),
          createSurveyPermission({ modelId: workgroup2._id }),
        ],
      } as SurveyModel;

      const findStub = sandbox.stub(workgroupModel, 'find').returns(createMongooseModel([workgroup1]));
      sandbox.stub(utrvRepo, 'getScopeUtrvs').resolves([]);

      const result = await instance.getWorkgroups({ survey, userIds: [user1._id] });

      expect(findStub.calledOnce).to.be.true;
      const findQuery = findStub.firstCall.args.at(0);
      expect(findQuery).to.deep.include({
        'users._id': { $in: [user1._id] },
      });

      expect(result).to.have.lengthOf(1);
      expect(result[0]._id.equals(workgroup1._id)).to.be.true;
      expect(result[0].permission).to.deep.equal(survey.permissions?.[0]);
    });

    describe('utrvIds', () => {
      const utrv1 = createScopeUtrv({});
      const utrv2 = createScopeUtrv({});
      const workgroup = { _id: new ObjectId() };
      const workgroups = [workgroup];

      beforeEach(() => {
        sandbox.stub(workgroupModel, 'find').returns(createMongooseModel(workgroups));
      });

      it('should include all utrvIds if access is full', async () => {
        const permission = createSurveyPermission({ modelId: workgroup._id });
        const survey = {
          permissions: [permission],
        } as SurveyModel;

        const result = await instance.getWorkgroups({ survey, utrvs: [utrv1, utrv2] });

        expect(result).to.deep.equal([
          { ...workgroup, permission: survey.permissions?.[0], utrvIds: [utrv1._id, utrv2._id] },
        ]);
      });

      it('should include no utrvIds if access is none', async () => {
        const permission = createSurveyPermission({ modelId: workgroup._id, access: DataScopeAccess.None });
        const survey = {
          permissions: [permission],
        } as SurveyModel;
        const result = await instance.getWorkgroups({ survey, utrvs: [utrv1, utrv2] });

        expect(result).to.deep.equal([{ ...workgroup, permission: survey.permissions?.[0], utrvIds: [] }]);
      });

      it('should include filtered utrvIds if access is partial', async () => {
        const permission = createSurveyPermission({
          modelId: workgroup._id,
          access: DataScopeAccess.Partial,
          scope: {
            sdg: [],
            materiality: [],
            frameworks: ['tcfd'],
            standards: [],
            custom: [],
          },
        });
        const survey = {
          permissions: [permission],
        } as SurveyModel;
        sandbox.stub(scopeUtrvsService, 'filterUtrvsByScope').resolves([utrv1]);
        const result = await instance.getWorkgroups({ survey, utrvs: [utrv1, utrv2] });

        expect(result).to.deep.equal([{ ...workgroup, permission: survey.permissions?.[0], utrvIds: [utrv1._id] }]);
      });

      it('should fetch utrvs using visibleUtrvs if not provided', async () => {
        const permission = createSurveyPermission({ modelId: workgroup._id, access: DataScopeAccess.Partial });
        const survey = {
          permissions: [permission],
          visibleUtrvs: [utrv1._id, utrv2._id],
        } as SurveyModel;
        const fetchUtrvsStub = sandbox.stub(utrvRepo, 'getScopeUtrvs').resolves([utrv1, utrv2]);
        sandbox.stub(scopeUtrvsService, 'filterUtrvsByScope').resolves([utrv1]);
        const result = await instance.getWorkgroups({ survey });

        expect(fetchUtrvsStub.calledOnceWith({ utrvIds: survey.visibleUtrvs })).to.be.true;
        expect(result).to.deep.equal([{ ...workgroup, permission: survey.permissions?.[0], utrvIds: [utrv1._id] }]);
      });
    });
  });

  describe('delegateWorkgroups', () => {
    it('should delegate workgroups', async () => {
      const survey = { save: sinon.stub() } as any;
      const workgroupIds = [new ObjectId(), new ObjectId()];
      const permission = {
        access: DataScopeAccess.Full,
        scope: {
          sdg: [],
          materiality: [],
          frameworks: [],
          standards: [],
          custom: [],
        },
        roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
      };

      const result = await instance.delegateWorkgroups({ survey, workgroupIds, permission });

      expect(survey.permissions).to.deep.equal([
        { type: SurveyPermissionType.Workgroup, modelId: workgroupIds[0], ...permission },
        { type: SurveyPermissionType.Workgroup, modelId: workgroupIds[1], ...permission },
      ]);
      expect(survey.permissions).to.deep.equal(result);
      expect(survey.save.calledOnce).to.be.true;
    });

    it('should allow to delegate workgroup once', async () => {
      const workgroupId = new ObjectId();
      const workgroupIds = [workgroupId];
      const permission = createSurveyPermission({ modelId: workgroupId });
      const survey = { permissions: [permission], save: sinon.stub() } as any;

      const updatingPermission = {
        access: DataScopeAccess.Partial,
        scope: {
          sdg: [],
          materiality: [],
          frameworks: ['tcfd'],
          standards: [],
          custom: [],
        },
        roles: [SurveyUserRoles.Verifier],
      };

      const result = await instance.delegateWorkgroups({ survey, workgroupIds, permission: updatingPermission });

      expect(survey.permissions).to.deep.equal([permission]);
      expect(survey.permissions).to.deep.equal(result);
      expect(survey.save.calledOnce).to.be.true;
    });
  });

  describe('updateWorkgroup', () => {
    it('should update workgroup', async () => {
      const workgroupId = new ObjectId();
      const permission = createSurveyPermission({ modelId: workgroupId });
      const survey = { save: sinon.stub(), permissions: [permission] } as any;

      const updatingPermission = {
        access: DataScopeAccess.Partial,
        scope: {
          sdg: [],
          materiality: [],
          frameworks: ['tcfd'],
          standards: [],
          custom: [],
        },
        roles: [SurveyUserRoles.Verifier],
      };

      const result = await instance.updateWorkgroup({ survey, workgroupId, permission: updatingPermission });

      expect(survey.permissions).to.deep.equal([{ ...permission, ...updatingPermission }]);
      expect(survey.permissions).to.deep.equal(result);
      expect(survey.save.calledOnce).to.be.true;
    });
  });

  describe('removeWorkgroups', () => {
    it('should remove workgroups', async () => {
      const workgroupIds = [new ObjectId()];
      const permission = createSurveyPermission({ modelId: workgroupIds[0] });
      const survey = { save: sinon.stub(), permissions: [permission] } as any;

      const result = await instance.removeWorkgroups({ survey, workgroupIds });

      expect(survey.permissions).to.be.empty;
      expect(survey.permissions).to.deep.equal(result);
      expect(survey.save.calledOnce).to.be.true;
    });
  });
});
