import '../../../setup';
import { expect } from 'chai';
import { createGroupedBatches } from '../../../../server/service/integration/greenly/greenly-utils';
import { OwnershipType } from '../../../../server/service/integration/greenly/greenlyTypes';

describe('greenly-utils', () => {

  // Identify value is initiativeId
  const createIdentifier = (initiativeId: string) => ({ type: 'operatorId' as const, value: initiativeId });
  const toOwnedCompany = (id: string) => ({ ownedCompanyId: id, ownershipType: OwnershipType.SUBSIDIARY, ownershipPercentage: 100 });

  const rootInitiative = { initiativeId: 'root', name: 'Root Company', parentInitiativeId: undefined };
  const greenlyCompany = { id: 'greenly-root', companyName: 'Root Company', identifier: createIdentifier('root') };

  const initiativeTree = [
    rootInitiative,
    { initiativeId: 'child1', name: 'Child 1', parentInitiativeId: 'root' },
    { initiativeId: 'child2', name: 'Child 2', parentInitiativeId: 'root' },
  ];

  describe('createGroupedBatches', () => {
    it('should handle empty initiative tree', () => {
      const result = createGroupedBatches({
        rootInitiative,
        initiativeTree: [],
        greenlyCompany,
        childrenCompanies: []
      });

      expect(result).to.have.lengthOf(0);
    });

    it('should create batches in breadth-first order', () => {
      const childrenCompanies = [
        { id: 'greenly-child1', companyName: 'Child 1', identifier: createIdentifier('child1') },
        { id: 'greenly-child2', companyName: 'Child 2', identifier: createIdentifier('child2') },
      ];

      // Call the function
      const result = createGroupedBatches({
        rootInitiative,
        initiativeTree,
        greenlyCompany,
        childrenCompanies
      });

      // Verify the result
      expect(result).to.deep.equal([
        {
          greenlyCompany: { id: greenlyCompany.id, companyName: greenlyCompany.companyName },
          ownedCompanies: [toOwnedCompany('greenly-child1'), toOwnedCompany('greenly-child2')]
        }
      ]);
    });

    it('should handle initiative tree with more entries than children companies', () => {
      const initiativeTree = [
        { initiativeId: 'child1', name: 'Child 1', parentInitiativeId: 'root' },
        { initiativeId: 'child2', name: 'Child 2', parentInitiativeId: 'root' },
        { initiativeId: 'child3', name: 'Child 3', parentInitiativeId: 'root' },
      ];

      // Only one child company exists in Greenly
      const childrenCompanies = [
        { id: 'greenly-child1', companyName: 'Child 1', identifier: createIdentifier('child1') },
      ];

      const result = createGroupedBatches({
        rootInitiative,
        initiativeTree,
        greenlyCompany,
        childrenCompanies
      });

      expect(result).to.deep.equal([
        {
          greenlyCompany: { id: greenlyCompany.id, companyName: greenlyCompany.companyName },
          ownedCompanies: [toOwnedCompany('greenly-child1')]
        }
      ]);
    });

    it('should handle 3-level deep tree with missing leaf child', () => {
      // Create a 3-level deep tree, grandchild3 and greatgrandchild3 are missing from Greenly
      const initiativeTree = [
        // Level 1 children
        { initiativeId: 'child1', name: 'Child 1', parentInitiativeId: 'root', externalId: 'greenly-child1' },
        { initiativeId: 'child2', name: 'Child 2', parentInitiativeId: 'root', externalId: 'greenly-child2' },
        // Level 2 children
        { initiativeId: 'grandchild1', name: 'Grandchild 1', parentInitiativeId: 'child1', externalId: 'greenly-grandchild1' },
        { initiativeId: 'grandchild2', name: 'Grandchild 2', parentInitiativeId: 'child1', externalId: 'greenly-grandchild2' },
        { initiativeId: 'grandchild3', name: 'Grandchild 3', parentInitiativeId: 'child2' },
        // Level 3 children
        { initiativeId: 'greatgrandchild3', name: 'Great Grandchild 3', parentInitiativeId: 'grandchild3' }
      ];

      // Create Greenly companies for a few leaf nodes
      const childrenCompanies = [
        { id: 'greenly-grandchild3', companyName: 'Grandchild 3', identifier: createIdentifier('grandchild3') },
        { id: 'greenly-greatgrandchild3', companyName: 'Great Grandchild 1', identifier: createIdentifier('greatgrandchild3') }
      ];

      const result = createGroupedBatches({
        rootInitiative,
        initiativeTree,
        greenlyCompany,
        childrenCompanies
      });

      expect(result).to.deep.equal([
        // Adding grandchild3 to child2
        {
          greenlyCompany: { id: 'greenly-child2', companyName: 'Child 2' },
          ownedCompanies: [toOwnedCompany('greenly-grandchild3')]
        },
        // Adding greatgrandchild3 to grandchild3
        {
          greenlyCompany: { id: 'greenly-grandchild3', companyName: 'Grandchild 3' },
          ownedCompanies: [toOwnedCompany('greenly-greatgrandchild3')]
        }
      ]);
    });
  });
});
