import '../../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import { AxiosError, AxiosHeaders } from 'axios';
import { createGreenlyApiError, greenlyResponseInterceptor, greenlyRequestErrorHandler } from '../../../../server/service/integration/greenly/GreenlyErrorUtils';
import ContextError from '../../../../server/error/ContextError';

describe('GreenlyErrorUtils', () => {
  describe('createGreenlyApiError', () => {
    it('should handle non-Axios errors', () => {
      const regularError = new Error('Regular error');
      const result = createGreenlyApiError(regularError);
      
      expect(result).to.be.instanceOf(ContextError);
      expect(result.message).to.equal('[GREENLY] API Error');
      expect(result.cause).to.equal(regularError);
    });

    it('should extract details from Axios error without response', () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        config: {
          url: '/test-endpoint',
          method: 'POST',
          headers: new AxiosHeaders(),
        },
      };
      
      const result = createGreenlyApiError(axiosError);
      
      expect(result).to.be.instanceOf(ContextError);
      expect(result.message).to.equal('[GREENLY] API Error');
      expect(result.context?.url).to.equal('/test-endpoint');
      expect(result.context?.method).to.equal('POST');
      expect(result.context?.status).to.be.undefined;
      expect(result.responseData).to.be.undefined;
      expect(result.context?.responseData).to.be.undefined;
    });

    it('should extract details from 400 error with response body', () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        config: {
          url: '/users',
          method: 'POST',
          headers: new AxiosHeaders(),
        },
        response: {
          status: 400,
          statusText: 'Bad Request',
          data: {
            error: 'Validation failed',
            details: ['Email is required', 'Name is too short']
          },
          headers: {},
          config: {} as any,
        },
      };
      
      const result = createGreenlyApiError(axiosError);
      
      expect(result).to.be.instanceOf(ContextError);
      expect(result.message).to.equal('[GREENLY] API Error');
      expect(result.context?.url).to.equal('/users');
      expect(result.context?.method).to.equal('POST');
      expect(result.context?.status).to.equal(400);
      // responseData should be at root level, not in context
      expect(result.responseData).to.deep.equal({
        error: 'Validation failed',
        details: ['Email is required', 'Name is too short']
      });
      expect(result.context?.responseData).to.be.undefined;
    });

    it('should extract details from 422 error with validation errors', () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        config: {
          url: '/companies',
          method: 'PUT',
          headers: new AxiosHeaders(),
        },
        response: {
          status: 422,
          statusText: 'Unprocessable Entity',
          data: {
            message: 'The given data was invalid',
            errors: {
              name: ['Name must be unique'],
              industry: ['Invalid industry code']
            }
          },
          headers: {},
          config: {} as any,
        },
      };
      
      const result = createGreenlyApiError(axiosError);
      
      expect(result).to.be.instanceOf(ContextError);
      expect(result.context?.status).to.equal(422);
      // responseData should be at root level, not in context
      expect(result.responseData).to.deep.equal({
        message: 'The given data was invalid',
        errors: {
          name: ['Name must be unique'],
          industry: ['Invalid industry code']
        }
      });
      expect(result.context?.responseData).to.be.undefined;
    });
  });

  describe('greenlyResponseInterceptor', () => {
    it('should transform Axios errors with response to ContextError', () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        response: {
          status: 400,
          statusText: 'Bad Request',
          data: { error: 'Invalid request' },
          headers: {},
          config: {} as any,
        },
      };
      
      expect(() => greenlyResponseInterceptor(axiosError))
        .to.throw(ContextError, '[GREENLY] API Error');
    });

    it('should pass through non-Axios errors', async () => {
      const regularError = new Error('Regular error');
      
      await expect(greenlyResponseInterceptor(regularError))
        .to.be.rejectedWith(Error, 'Regular error');
    });

    it('should pass through Axios errors without response', async () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        message: 'Network error',
      };
      
      await expect(greenlyResponseInterceptor(axiosError))
        .to.be.rejectedWith('Network error');
    });
  });

  describe('greenlyRequestErrorHandler', () => {
    it('should transform Axios errors to ContextError', () => {
      const axiosError: Partial<AxiosError> = {
        isAxiosError: true,
        config: {
          url: '/test',
          method: 'GET',
          headers: new AxiosHeaders(),
        },
      };
      
      expect(() => greenlyRequestErrorHandler(axiosError))
        .to.throw(ContextError, '[GREENLY] API Error');
    });

    it('should pass through non-Axios errors', async () => {
      const regularError = new Error('Regular error');
      
      await expect(greenlyRequestErrorHandler(regularError))
        .to.be.rejectedWith(Error, 'Regular error');
    });
  });
});