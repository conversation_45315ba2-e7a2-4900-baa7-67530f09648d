import Stripe from 'stripe';
import {
  createPrice,
  createStripeDiscount,
  createStripeProduct,
  createStripeSub,
  createStripeSubItem,
} from '../../fixtures/subscriptions';
import { ProductCodes, Subscription } from '../../../server/models/customer';
import { fromStripeSubscriptionWithProduct } from '../../../server/service/payment/StripeClient';
import { expect } from 'chai';
import sinon from 'sinon';
import { wwgLogger } from '../../../server/service/wwgLogger';

describe('StripeClient', () => {
  describe('fromStripeSubscriptionWithProduct', () => {
    const subId = 'sub_g17eco_test';
    const productId = 'prod_g17eco_test';
    const productCode = ProductCodes.CompanyTrackerStarter;
    const discountOne = createStripeDiscount();

    it('should successfully map Stripe subscription to local subscription', () => {
      const productOne = createStripeProduct(productId, productCode);
      const priceOne = createPrice({ product: productOne });
      const itemOne = createStripeSubItem({ id: 'sub_g17eco_item', price: priceOne });
      const items = {
        object: 'list',
        data: [itemOne],
      } as Stripe.Subscription['items'];

      const subscription: Stripe.Subscription = createStripeSub(
        { id: subId, items, discount: discountOne, discounts: [discountOne] },
        productCode
      );

      const expectedSubscription: Subscription = {
        id: subscription.id,
        status: subscription.status,
        items: [
          {
            id: itemOne.id,
            priceId: itemOne.price.id,
            productId: productId,
            productCode: productCode,
            quantity: itemOne.quantity,
            created: itemOne.created,
            price: {
              id: itemOne.price.id,
              type: itemOne.price.type,
              recurring: itemOne.price.recurring ?? undefined,
            },
          },
        ],
        startDate: subscription.start_date,
        endDate: subscription.ended_at ?? undefined,
        cancelDate: subscription.canceled_at ?? subscription.cancel_at ?? undefined,
        periodStart: subscription.current_period_start,
        periodEnd: subscription.current_period_end,
        trialStart: subscription.trial_start ?? undefined,
        trialEnd: subscription.trial_end ?? undefined,
        discount: {
          id: discountOne.id,
          start: discountOne.start,
          end: discountOne.end,
          coupon: {
            id: discountOne.coupon.id,
            name: discountOne.coupon.name ?? undefined,
            currency: discountOne.coupon.currency,
            percent_off: discountOne.coupon.percent_off,
          },
        },
        discounts: [
          {
            id: discountOne.id,
            start: discountOne.start,
            end: discountOne.end,
            coupon: {
              id: discountOne.coupon.id,
              name: discountOne.coupon.name ?? undefined,
              currency: discountOne.coupon.currency,
              percent_off: discountOne.coupon.percent_off,
            },
          },
        ],
      };

      expect(fromStripeSubscriptionWithProduct(subscription)).to.deep.eq(expectedSubscription);
    });
    it('should throw error when product is not expanded', () => {
      const priceOne = createPrice({ product: 'product_string' });
      const itemOne = createStripeSubItem({ id: 'sub_g17eco_item', price: priceOne });
      const items = {
        object: 'list',
        data: [itemOne],
      } as Stripe.Subscription['items'];

      const subscription: Stripe.Subscription = createStripeSub(
        { id: subId, items, discount: discountOne, discounts: [discountOne] },
        productCode
      );
      expect(() => fromStripeSubscriptionWithProduct(subscription)).to.throw(
        Error,
        /Require expanded product property for subscription/
      );
    });
    it('should log error when product code is missing', () => {
      const productOne = createStripeProduct(productId, undefined);
      const priceOne = createPrice({ product: productOne });
      const itemOne = createStripeSubItem({ id: 'sub_g17eco_item', price: priceOne });
      const items = {
        object: 'list',
        data: [itemOne],
      } as Stripe.Subscription['items'];

      const subscription: Stripe.Subscription = createStripeSub(
        { id: subId, items, discount: discountOne, discounts: [discountOne] },
        undefined
      );

      const loggerSpy = sinon.spy(wwgLogger, 'error');

      fromStripeSubscriptionWithProduct(subscription);

      expect(loggerSpy.calledOnce).to.be.true;
      sinon.assert.calledOnceWithMatch(
        loggerSpy,
        sinon.match
          .instanceOf(Error)
          .and(
            sinon.match.has(
              'message',
              `Subscription ${subscription.id}, item ${itemOne.id} Product ${productId} is missing productCode metadata`
            )
          )
      );
    });
    it('should throw error when receive invalid discounts', () => {
      const productOne = createStripeProduct(productId, productCode);
      const priceOne = createPrice({ product: productOne });
      const itemOne = createStripeSubItem({ id: 'sub_g17eco_item', price: priceOne });
      const items = {
        object: 'list',
        data: [itemOne],
      } as Stripe.Subscription['items'];

      const subscription: Stripe.Subscription = createStripeSub(
        { id: subId, items, discount: discountOne, discounts: undefined },
        productCode
      );
      expect(() => fromStripeSubscriptionWithProduct(subscription)).to.throw(
        Error,
        /Received invalid stripe discounts/
      );
    });
  });
});
