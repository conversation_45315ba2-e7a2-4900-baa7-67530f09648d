import { expect } from 'chai';
import { ReportDocumentType } from '../../../server/models/reportDocument';
import { getData, getMappingsByType, getStringData } from '../../../server/service/reporting/utils';
import {
  factOne,
  factThree,
  factTwo,
  noRefFact,
  utrCodeToUtrvMap,
  xbrlMapping,
} from '../../fixtures/xbrlReportFixtures';

describe('getCsrdMapping', () => {
  const existingCSRDFact = 'esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory';

  it('returns default mapping when no overrides are provided', () => {
    const defaultMapping = getMappingsByType({ type: ReportDocumentType.CSRD });
    expect(defaultMapping).to.have.property(existingCSRDFact);
  });

  it('overrides existing mapping items when overrides are provided', () => {
    const overrides = {
      [existingCSRDFact]: {
        factName: existingCSRDFact,
        utrCode: 'custom-utr',
        valueListCode: 'custom-value',
      },
    };
    const newMapping = getMappingsByType({ type: ReportDocumentType.CSRD, overrides });
    expect(newMapping[existingCSRDFact]?.utrCode).to.equal('custom-utr');
    expect(newMapping[existingCSRDFact]?.valueListCode).to.equal('custom-value');
  });

  it('adds new mapping items when overrides are provided', () => {
    const overrides = {
      'esrs:NewFact': { factName: 'esrs:NewFact', utrCode: 'new-utr' },
    };
    const newMapping = getMappingsByType({ type: ReportDocumentType.CSRD, overrides });
    expect(newMapping).to.have.property('esrs:NewFact');
    expect(newMapping['esrs:NewFact']?.utrCode).to.equal('new-utr');
  });
});

describe('getData', () => {
  it('returns value for number type', () => {
    expect(getData({ factName: factOne, mapping: xbrlMapping, utrCodeToUtrvMap })).to.equal(123);
  });
  it('returns value for text type', () => {
    expect(getData({ factName: factTwo, mapping: xbrlMapping, utrCodeToUtrvMap })).to.equal('abc');
  });
  it('returns fallback for missing mapping', () => {
    expect(getData({ factName: factThree, mapping: xbrlMapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal(
      'fallback'
    );
  });
  it('returns fallback for missing utrv', () => {
    expect(getData({ factName: noRefFact, mapping: xbrlMapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal(
      'fallback'
    );
  });
});

describe('getStringData', () => {
  it('returns string value', () => {
    expect(getStringData({ factName: factOne, mapping: xbrlMapping, utrCodeToUtrvMap })).to.equal('123');
  });
  it('returns fallback as string', () => {
    expect(
      getStringData({ factName: noRefFact, mapping: xbrlMapping, utrCodeToUtrvMap, fallback: 'fallback' })
    ).to.equal('fallback');
  });
});
