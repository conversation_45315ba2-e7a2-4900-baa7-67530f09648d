/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import '../setup'
import { createSandbox } from "sinon";
import { expect } from "chai";
import { UserApiKeyService, getAccessTokenService } from "../../server/public-api/UserApiKeyService";
import UserApiKey from "../../server/models/userApiKey";
import { userOne } from "../fixtures/userFixtures";
import { initiativeOneSimple } from "../fixtures/initiativeFixtures";
import { createMongooseModel } from "../setup";
import { testLogger } from "../factories/logger";
import { ConnectionRole, ScopePermission } from "../../server/public-api/scopePermissionModels";

describe('UserApiKeyService', () => {

  const service = new UserApiKeyService(testLogger);
  const sandbox = createSandbox();

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getAccessTokenService()).to.be.instanceOf(UserApiKeyService);
  });

  describe('listPersonalTokens method', () => {

    afterEach(() => {
      sandbox.restore();
    });

    it('should return empty array', async () => {
      sandbox.stub(UserApiKey, 'find').returns(createMongooseModel([]));
      const tokens = await service.listPersonalTokens(userOne);
      expect(tokens).to.be.an('array').that.is.empty;
    });
  })

  describe('createPersonalToken method', () => {

    const initiativeId = initiativeOneSimple._id;
    const baseData = {
      initiativeId,
      scopes: [],
      roles: [] ,
    }

    beforeEach(() => {
      sandbox.stub(UserApiKey.prototype, 'save').callsFake(function (this: unknown) {
        return this;
      })
    })

    afterEach(() => {
      sandbox.restore();
    });

    it('should create token', async () => {
      const { token } = await service.createPersonalToken(userOne, baseData);
      const [prefix, shortToken, longToken] = token.split('_');

      expect(prefix).eq('g17eco');

      expect(shortToken).to.be.lengthOf(10);
      expect(shortToken.startsWith('pa')).to.be.true;

      expect(longToken).to.be.lengthOf(36);
    });


    it('should create token name', async () => {
      const name = 'Test personal token one'
      const { apiKey } = await service.createPersonalToken(userOne, { ...baseData, name });

      expect(apiKey.name).eq(name);
      expect(apiKey.initiativeId.toString()).eq(initiativeId.toString());
    });

    it('should create with custom scopes and roles', async () => {
      const data = { ...baseData, roles: [ConnectionRole.DateEntry], scopes: [ScopePermission.InitiativeRead] };
      const { apiKey } = await service.createPersonalToken(userOne, data);

      expect(apiKey.scopes).eqls(data.scopes);
      expect(apiKey.roles).eqls(data.roles);
    });
  });

  describe('revoke method', () => {

    it('should revoke token', async () => {
      const apiKey = new UserApiKey({ userId: userOne._id });
      sandbox.stub(apiKey, 'save').resolves(apiKey);
      sandbox.stub(UserApiKey, 'findOne').returns(createMongooseModel(apiKey));
      const userApiKey = await service.revoke(userOne, apiKey._id.toString());
      expect(userApiKey.revokedDate).to.be.instanceOf(Date);
    });

    it('should error on already revoked token', async () => {
      const apiKey = new UserApiKey({ userId: userOne._id, revokedDate: new Date() });
      sandbox.stub(apiKey, 'save').resolves(apiKey);
      sandbox.stub(UserApiKey, 'findOne').returns(createMongooseModel(apiKey));
      await expect(service.revoke(userOne, apiKey._id.toString())).to.be.rejectedWith(/api key has already been revoked/i);
    });
  });
});
