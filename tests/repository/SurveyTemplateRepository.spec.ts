import { ObjectId } from 'bson';
import { expect } from 'chai';
import { SurveyTemplateRepository } from '../../server/repository/SurveyTemplateRepository';
import { connect, disconnect } from '../setup/mongoInMemory';
import {
  clearFixtures,
  setupInitiative,
  setupSurveyTemplate,
  setupSurveyTemplateHistory,
} from '../setup/mongoInMemoryFixtures';

before(connect);
after(disconnect);

describe('SurveyTemplateRepository', () => {
  describe('getTemplatesByCurrentLevelAndAbove', () => {
    const templateHistoryCreatedDay = new Date('2025-01-02');
    const initiativeId = new ObjectId();
    const templateId = new ObjectId();
    const surveyTemplate = {
      _id: templateId,
      initiativeId,
      name: 'ABC',
      created: new Date('2025-01-01'),
    };
    const surveyTemplateHistory = {
      _id: new ObjectId(),
      initiativeId,
      userId: new ObjectId(),
      templateId,
      created: templateHistoryCreatedDay,
    };

    beforeEach(async () => {
      await setupInitiative(initiativeId, []);
      await setupSurveyTemplate(surveyTemplate);
    });

    afterEach(async () => {
      await clearFixtures();
    });

    it('should return templates sorted by created date in descending order', async () => {
      await setupSurveyTemplateHistory(surveyTemplateHistory);

      const result = await SurveyTemplateRepository.getTemplatesByCurrentLevelAndAbove([initiativeId]);

      expect(result).to.be.an('array').with.lengthOf(1);
      expect(result[0]._id.toString()).to.equal(templateId.toString());
      expect(result[0].initiative).to.be.an('object');
      expect(result[0].initiative._id.toString()).to.equal(initiativeId.toString());
      expect(result[0].lastUsed.toISOString()).to.equal(templateHistoryCreatedDay.toISOString());
    });

    it('should return templates without lastUsed if no history exists', async () => {
      const result = await SurveyTemplateRepository.getTemplatesByCurrentLevelAndAbove([initiativeId]);

      expect(result).to.be.an('array').with.lengthOf(1);
      expect(result[0]._id.toString()).to.equal(templateId.toString());
      expect(result[0].initiative).to.be.an('object');
      expect(result[0].initiative._id.toString()).to.equal(initiativeId.toString());
      expect(result[0].lastUsed).to.be.undefined;
    });

    it('should return an empty array if no templates match the criteria', async () => {
      const result = await SurveyTemplateRepository.getTemplatesByCurrentLevelAndAbove([new ObjectId()]);

      expect(result).to.be.an('array').that.is.empty;
    });
  });
});
