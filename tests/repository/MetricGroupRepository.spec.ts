import { ObjectId } from 'bson';
import { expect } from 'chai';
import MetricGroup, { type MetricGroupPlain } from '../../server/models/metricGroup';
import { MetricGroupRepository } from '../../server/repository/MetricGroupRepository';
import { connect, disconnect } from '../setup/mongoInMemory';
import { clearFixtures, setupMetricGroup } from '../setup/mongoInMemoryFixtures';

before(connect);
after(disconnect);

describe('MetricGroupRepository', () => {
  describe('getAllChildrenById', () => {
    afterEach(async () => {
      await clearFixtures([MetricGroup]);
    });

    it('should return empty array when parent does not exist', async () => {
      const nonExistentId = new ObjectId();
      const result = await MetricGroupRepository.getAllChildrenById(nonExistentId);
      expect(result).to.be.an('array');
      expect(result).to.have.length(0);
    });

    it('should return empty array when no children exist', async () => {
      // Create a metric group with no children
      const parentId = new ObjectId();
      await setupMetricGroup({ _id: parentId, groupName: 'Lonely Parent Group' });

      const result = await MetricGroupRepository.getAllChildrenById(parentId);

      expect(result).to.be.an('array');
      expect(result).to.have.length(0);
    });

    it('should return children metric groups from database', async () => {
      // Create parent metric group
      const parentId = new ObjectId();
      await setupMetricGroup({
        _id: parentId,
        groupName: 'Parent Group',
      });

      // Create child metric groups
      const childId1 = new ObjectId();
      await setupMetricGroup({
        _id: childId1,
        groupName: 'Child Group 1',
        parentId: parentId,
      });

      const childId2 = new ObjectId();
      await setupMetricGroup({
        _id: childId2,
        groupName: 'Child Group 2',
        parentId: parentId,
      });

      // Create grandchild metric group
      const grandchildId = new ObjectId();
      await setupMetricGroup({
        _id: grandchildId,
        groupName: 'Grandchild Group',
        parentId: childId1,
      });

      const result = await MetricGroupRepository.getAllChildrenById(parentId);

      expect(result).to.have.length(3);

      const resultGroupNames = result.map((group: MetricGroupPlain) => group.groupName).sort();
      expect(resultGroupNames).to.deep.equal(['Child Group 1', 'Child Group 2', 'Grandchild Group']);
    });
  });
});
