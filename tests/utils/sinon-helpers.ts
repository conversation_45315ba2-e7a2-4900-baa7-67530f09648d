/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { SinonStub } from 'sinon';

/**
 * Type-safe helper to extract arguments from a Sinon stub call
 * @param stub - The Sinon stub
 * @param callIndex - The index of the call (default 0)
 * @returns The arguments array or undefined if call doesn't exist
 */
export function getStubCallArgs<T extends any[]>(
  stub: SinonStub,
  callIndex: number = 0
): T | undefined {
  if (!stub || stub.callCount <= callIndex) {
    return undefined;
  }
  
  const call = stub.getCall(callIndex);
  return call?.args as T;
}

/**
 * Type-safe helper to get the first argument of a stub call
 * @param stub - The Sinon stub
 * @param callIndex - The index of the call (default 0)
 * @returns The first argument or undefined
 */
export function getFirstArg<T = any>(
  stub: SinonStub,
  callIndex: number = 0
): T | undefined {
  const args = getStubCallArgs(stub, callIndex);
  return args?.[0] as T;
}

/**
 * Type-safe helper to assert a stub was called with specific arguments
 * @param stub - The Sinon stub
 * @param expectedArgs - The expected arguments
 * @param callIndex - The index of the call (default 0)
 */
export function assertCalledWithArgs<T extends any[]>(
  stub: SinonStub,
  expectedArgs: T,
  callIndex: number = 0
): void {
  const args = getStubCallArgs<T>(stub, callIndex);
  if (!args) {
    throw new Error(`Stub was not called ${callIndex + 1} time(s)`);
  }
  
  // Deep equality check would be done by the test framework
  // This just ensures type safety
}

/**
 * Type-safe helper to check if stub was called with partial object match
 * @param stub - The Sinon stub
 * @param partialMatch - Partial object to match against
 * @param callIndex - The index of the call (default 0)
 */
export function wasCalledWithMatch<T extends Record<string, any>>(
  stub: SinonStub,
  partialMatch: Partial<T>,
  callIndex: number = 0
): boolean {
  const firstArg = getFirstArg<T>(stub, callIndex);
  if (!firstArg) return false;
  
  return Object.entries(partialMatch).every(([key, value]) => 
    firstArg[key] === value || 
    (typeof value === 'object' && JSON.stringify(firstArg[key]) === JSON.stringify(value))
  );
}