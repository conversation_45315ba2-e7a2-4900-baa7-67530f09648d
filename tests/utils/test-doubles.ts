/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { SinonStubbedInstance } from 'sinon';
import * as sinon from 'sinon';

/**
 * Deep partial type for creating test doubles with nested objects
 */
export type DeepPartial<T> = T extends object ? {
  [P in keyof T]?: T[P] extends (infer U)[]
    ? DeepPartial<U>[]
    : T[P] extends readonly (infer U)[]
    ? readonly DeepPartial<U>[]
    : T[P] extends Set<infer U>
    ? Set<DeepPartial<U>>
    : T[P] extends Map<infer K, infer V>
    ? Map<DeepPartial<K>, DeepPartial<V>>
    : T[P] extends object
    ? DeepPartial<T[P]>
    : T[P];
} : T;

/**
 * Type that converts all methods to SinonStub
 */
export type StubbedInstance<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? sinon.SinonStub : T[K];
};

/**
 * Creates a type-safe test double from a partial implementation
 * This allows creating mocks without implementing every property
 */
export function createTestDouble<T>(partial: DeepPartial<T>): T {
  return partial as T;
}

/**
 * Creates a test double with all methods as SinonStubs
 * Returns properly typed instance where methods are SinonStubs
 */
export function createStubbedTestDouble<T>(partial: Partial<StubbedInstance<T>>): StubbedInstance<T> {
  return partial as StubbedInstance<T>;
}

/**
 * Creates a stubbed instance of a class with all methods stubbed
 * This is useful for mocking service dependencies
 */
export function createStubbedInstance<T>(
  constructor: new (...args: any[]) => T,
  overrides?: Partial<SinonStubbedInstance<T>>
): SinonStubbedInstance<T> {
  const stub = sinon.createStubInstance(constructor);
  
  if (overrides) {
    Object.assign(stub, overrides);
  }
  
  return stub;
}

/**
 * Type guard to check if a value is defined (not null or undefined)
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Creates a partial mock that only implements specified methods
 * Useful when you only need to mock certain methods of an interface
 */
export function createPartialMock<T extends object>(methods: Partial<T>): T {
  const proxy = new Proxy({} as T, {
    get(target, prop) {
      if (prop in methods) {
        return methods[prop as keyof T];
      }
      // Return a stub for any unmocked method
      return sinon.stub();
    }
  });
  
  return proxy;
}

/**
 * Type-safe way to create a mock that matches an interface
 * with all methods as sinon stubs
 */
export function createMockFromInterface<T extends Record<string, any>>(): {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? sinon.SinonStub : T[K]
} {
  const handler = {
    get(target: any, prop: string) {
      if (!(prop in target)) {
        target[prop] = sinon.stub();
      }
      return target[prop];
    }
  };
  
  return new Proxy({}, handler) as any;
}

/**
 * Helper to create a mock with specific return values
 */
export interface MockBuilder<T> {
  withMethod<K extends keyof T>(
    method: K,
    implementation: T[K] extends (...args: any[]) => any ? sinon.SinonStub : T[K]
  ): MockBuilder<T>;
  build(): T;
}

export function mockBuilder<T extends object>(): MockBuilder<T> {
  const mock: any = {};
  
  return {
    withMethod<K extends keyof T>(method: K, implementation: any): MockBuilder<T> {
      mock[method] = implementation;
      return this;
    },
    build(): T {
      return createPartialMock<T>(mock);
    }
  };
}

/**
 * Type helper to extract the resolved type from a Promise
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

/**
 * Type helper to make specific properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Type helper to make specific properties required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/**
 * Type-safe wrapper for dependency injection mocks
 * Provides both the original type for DI and stub access
 */
export interface MockWithStubs<T> {
  // The mock as the original type for dependency injection
  mock: T;
  // Access to the underlying stubs for test assertions
  stubs: StubbedInstance<T>;
}

/**
 * Creates a type-safe mock for dependency injection that also provides stub access
 * 
 * @example
 * const mockRepo = createMockWithStubs<Repository>({
 *   find: sandbox.stub().resolves([]),
 *   save: sandbox.stub().resolves({ id: '123' })
 * });
 * 
 * // Use for dependency injection - properly typed as Repository
 * const service = new Service(mockRepo.mock);
 * 
 * // Use stubs for assertions - no casting needed
 * expect(mockRepo.stubs.find.calledOnce).to.be.true;
 */
export function createMockWithStubs<T>(stubs: Partial<StubbedInstance<T>>): MockWithStubs<T> {
  const stubbedInstance = stubs as StubbedInstance<T>;
  return {
    mock: stubbedInstance as unknown as T,
    stubs: stubbedInstance
  };
}

/**
 * Creates a type-safe mock for Mongoose models (typeof Model pattern)
 * This is specifically for mocking static model methods like find, create, etc.
 * 
 * @example
 * const mockUserModel = createModelMock<typeof User>({
 *   findById: sandbox.stub().resolves(testUser),
 *   find: sandbox.stub().resolves([testUser]),
 *   create: sandbox.stub().resolves(testUser)
 * });
 * 
 * // Use for dependency injection - no casting needed!
 * const service = new UserService(mockUserModel.mock);
 * 
 * // Access stubs for assertions
 * expect(mockUserModel.stubs.findById.calledWith('123')).to.be.true;
 */
export function createModelMock<T extends { new(...args: any[]): any }>(
  stubs: Partial<StubbedInstance<T>>
): MockWithStubs<T> {
  // Create a minimal constructor function that satisfies typeof Model
  const ModelConstructor = function() {} as any;
  
  // Copy all stubs to the constructor
  Object.assign(ModelConstructor, stubs);
  
  return {
    mock: ModelConstructor as T,
    stubs: stubs as StubbedInstance<T>
  };
}

/**
 * Creates a type-safe service mock with automatic type inference
 * Useful when mocking service instances that have complex method signatures
 * 
 * @example
 * const mockService = createServiceMock<EmailService>({
 *   sendEmail: sandbox.stub().resolves(),
 *   sendBulkEmail: sandbox.stub().resolves()
 * });
 * 
 * // No casting needed - fully typed
 * const manager = new UserManager(mockService.mock);
 * 
 * // Type-safe stub access
 * mockService.stubs.sendEmail.calledWith(expectedArgs);
 */
export function createServiceMock<T extends object>(
  stubs: Partial<StubbedInstance<T>>
): MockWithStubs<T> {
  // Use a Proxy to handle any unmocked methods gracefully
  const handler: ProxyHandler<any> = {
    get(target, prop) {
      if (prop in stubs) {
        return stubs[prop as keyof typeof stubs];
      }
      // Return a default stub for unmocked methods
      if (typeof prop === 'string' && !['then', 'catch', 'finally'].includes(prop)) {
        return sinon.stub();
      }
      return undefined;
    }
  };
  
  const proxiedMock = new Proxy({}, handler);
  
  return {
    mock: proxiedMock as T,
    stubs: stubs as StubbedInstance<T>
  };
}