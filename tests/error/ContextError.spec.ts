import { expect } from 'chai';
import ContextError from '../../server/error/ContextError';

describe('ContextError', () => {
  describe('basic functionality', () => {
    it('should create error with message and context', () => {
      const error = new ContextError('Test error', {
        userId: '123',
        status: 400,
      });
      
      expect(error.message).to.equal('Test error');
      expect(error.context).to.deep.equal({
        userId: '123',
        status: 400,
      });
    });

    it('should handle error without context', () => {
      const error = new ContextError('Test error');
      
      expect(error.message).to.equal('Test error');
      expect(error.context).to.be.undefined;
    });
  });

  describe('responseData promotion to root', () => {
    it('should move responseData from context to root property', () => {
      const responseData = { 
        error: 'Validation failed', 
        details: ['Field required'] 
      };
      
      const error = new ContextError('API Error', {
        status: 400,
        responseData,
        url: '/api/test',
      });
      
      // responseData should be at root level
      expect(error.responseData).to.deep.equal(responseData);
      
      // responseData should be removed from context
      expect(error.context).to.deep.equal({
        status: 400,
        url: '/api/test',
      });
      expect(error.context?.responseData).to.be.undefined;
    });

    it('should handle context without responseData', () => {
      const error = new ContextError('Test error', {
        status: 500,
        url: '/api/test',
      });
      
      expect(error.responseData).to.be.undefined;
      expect(error.context).to.deep.equal({
        status: 500,
        url: '/api/test',
      });
    });
  });

  describe('nested ContextError - 2 levels', () => {
    it('should inherit responseData from cause when cause is ContextError', () => {
      const responseData = {
        message: 'Bad Request',
        errors: { field: 'Invalid value' },
      };
      
      // Inner error with responseData
      const innerError = new ContextError('[INNER] API Error', {
        status: 400,
        responseData,
        method: 'POST',
      });
      
      // Outer error with inner as cause
      const outerError = new ContextError('[OUTER] Request failed', {
        userId: 'user-123',
        initiativeId: 'init-456',
        cause: innerError,
      });
      
      // Outer should inherit responseData from inner
      expect(outerError.responseData).to.deep.equal(responseData);
      expect(outerError.cause).to.equal(innerError);
      expect(outerError.context).to.deep.equal({
        userId: 'user-123',
        initiativeId: 'init-456',
      });
    });

    it('should prioritize cause responseData over current context responseData', () => {
      const innerResponseData = { error: 'Inner error' };
      const outerResponseData = { error: 'Outer error' };
      
      const innerError = new ContextError('Inner', {
        responseData: innerResponseData,
      });
      
      const outerError = new ContextError('Outer', {
        responseData: outerResponseData,
        cause: innerError,
      });
      
      // Should use inner error's responseData (cause takes priority)
      expect(outerError.responseData).to.deep.equal(innerResponseData);
    });
  });

  describe('nested ContextError - 3 levels', () => {
    it('should bubble up responseData through 3 levels of nesting', () => {
      const apiResponseData = {
        message: 'Company already exists',
        code: 'DUPLICATE_COMPANY',
        details: {
          companyId: 'comp-123',
          name: 'Test Company',
        },
      };
      
      // Level 1: Deepest error with API response
      const level1Error = new ContextError('[GREENLY] API Error', {
        status: 422,
        url: '/companies',
        method: 'POST',
        responseData: apiResponseData,
      });
      
      // Level 2: Service error wrapping API error
      const level2Error = new ContextError('[SERVICE] Failed to create company', {
        companyName: 'Test Company',
        greenlyCompanyId: 'greenly-123',
        cause: level1Error,
      });
      
      // Level 3: Top-level handler error
      const level3Error = new ContextError('[HANDLER] Integration setup failed', {
        userId: 'user-456',
        initiativeId: 'init-789',
        cause: level2Error,
      });
      
      // responseData should bubble up to the top level
      expect(level3Error.responseData).to.deep.equal(apiResponseData);
      
      // Each level should maintain its own context
      expect(level3Error.context).to.deep.equal({
        userId: 'user-456',
        initiativeId: 'init-789',
      });
      
      // Verify the cause chain
      expect(level3Error.cause).to.equal(level2Error);
      expect(level2Error.cause).to.equal(level1Error);
    });
  });

  describe('mixed error types', () => {
    it('should not inherit responseData when cause is not ContextError', () => {
      const regularError = new Error('Regular error');
      
      const contextError = new ContextError('Wrapped error', {
        status: 500,
        cause: regularError,
      });
      
      expect(contextError.responseData).to.be.undefined;
      expect(contextError.cause).to.equal(regularError);
      expect(contextError.context).to.deep.equal({
        status: 500,
      });
    });

    it('should handle TypeError as cause', () => {
      const typeError = new TypeError('Cannot read property of undefined');
      
      const contextError = new ContextError('Operation failed', {
        operation: 'getData',
        cause: typeError,
      });
      
      expect(contextError.responseData).to.be.undefined;
      expect(contextError.cause).to.equal(typeError);
      expect(contextError.context).to.deep.equal({
        operation: 'getData',
      });
    });
  });

  describe('addContext method', () => {
    it('should add additional context after creation', () => {
      const error = new ContextError('Test error', {
        initial: 'value',
      });
      
      error.addContext({
        additional: 'data',
        status: 400,
      });
      
      expect(error.context).to.deep.equal({
        initial: 'value',
        additional: 'data',
        status: 400,
      });
    });

    it('should override existing context properties', () => {
      const error = new ContextError('Test error', {
        status: 200,
        message: 'initial',
      });
      
      error.addContext({
        status: 400,
        newField: 'value',
      });
      
      expect(error.context).to.deep.equal({
        status: 400,
        message: 'initial',
        newField: 'value',
      });
    });

    it('should not affect responseData when adding context', () => {
      const responseData = { error: 'API error' };
      const error = new ContextError('Test error', {
        responseData,
        initial: 'value',
      });
      
      error.addContext({
        additional: 'data',
      });
      
      expect(error.responseData).to.deep.equal(responseData);
      expect(error.context).to.deep.equal({
        initial: 'value',
        additional: 'data',
      });
    });
  });

  describe('real-world Greenly scenario', () => {
    it('should handle Greenly API error chain correctly', () => {
      // Simulate actual Greenly API error response
      const greenlyApiResponse = {
        message: 'BadRequest',
        details: 'Bad Request. "[createCompany] Error while creating company: A company with type standalone already exists"',
        validationErrors: [
          { field: 'companyType', message: 'Already exists' },
        ],
      };
      
      // Level 1: GreenlyErrorUtils creates initial error
      const apiError = new ContextError('[GREENLY] API Error', {
        url: '/companies',
        method: 'POST',
        status: 400,
        responseData: greenlyApiResponse,
      });
      
      // Level 2: Service catches and wraps
      const serviceError = new ContextError('[GREENLY] Failed to create main user', {
        initiativeId: 'init-123',
        userId: 'user-456',
        greenlyCompanyId: 'company-789',
        cause: apiError,
      });
      
      // Verify responseData is available at top level
      expect(serviceError.responseData).to.deep.equal(greenlyApiResponse);
      
      // Verify context is clean (no responseData duplication)
      expect(serviceError.context).to.deep.equal({
        initiativeId: 'init-123',
        userId: 'user-456',
        greenlyCompanyId: 'company-789',
      });
      
      // Original error should have responseData at root too
      expect(apiError.responseData).to.deep.equal(greenlyApiResponse);
      expect(apiError.context?.responseData).to.be.undefined;
    });
  });
});