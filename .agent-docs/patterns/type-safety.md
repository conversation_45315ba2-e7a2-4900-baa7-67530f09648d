# Type Safety Patterns - G17Eco API

## Core Principles

1. **Never use `any`** - Always use proper types
2. **Avoid type assertions with `!`** - <PERSON>le null/undefined properly
3. **Use Mongoose types** instead of generic types
4. **Create interfaces only when needed** - Don't over-abstract

## Type Safety Patterns

### ✅ GOOD: Using Proper Mongoose Types
```typescript
import { AIPromptTemplatePlain, AIPromptTemplateModel } from '../models/aiPromptTemplate';

export class PromptTemplateRepository {
  // Use specific Mongoose types instead of 'any'
  async findAllActive(): Promise<AIPromptTemplatePlain[]> {
    return AIPromptTemplate.find({ isActive: true }).lean().exec();
  }
  
  async create(data: Partial<AIPromptTemplatePlain>): Promise<AIPromptTemplateModel> {
    const template = new AIPromptTemplate(data);
    return template.save();
  }
  
  async findWithQuery(
    query: Record<string, any>, // Specific object type for queries
    sort: Record<string, 1 | -1> = { created: -1 } // Specific sort type
  ): Promise<AIPromptTemplatePlain[]> {
    return AIPromptTemplate.find(query).sort(sort).lean().exec();
  }
}
```

### ❌ BAD: Using 'any' Types
```typescript
// DON'T DO THIS
export class BadRepository {
  async findAll(): Promise<any[]> { // ❌ any[]
    return Model.find().exec();
  }
  
  async create(data: any): Promise<any> { // ❌ any
    return new Model(data).save();
  }
}
```

## Handling Null/Undefined

### ✅ GOOD: Proper Null Handling
```typescript
// Handle potential null/undefined
async function getUser(id: string): Promise<User | null> {
  const user = await UserModel.findById(id);
  if (!user) {
    return null;
  }
  return user;
}

// In consuming code
const user = await getUser(id);
if (!user) {
  throw new NotFoundError('User not found');
}
// TypeScript now knows user is not null
console.log(user.email);
```

### ❌ BAD: Using Non-Null Assertion
```typescript
// DON'T DO THIS
const user = await UserModel.findById(id);
console.log(user!.email); // ❌ Dangerous - could crash at runtime
```

## Interface Usage Guidelines

### When to Create Interfaces

✅ **DO create interfaces when:**
- Breaking circular dependencies
- Defining contracts between layers
- Creating public APIs
- Multiple implementations exist

❌ **DON'T create interfaces when:**
- There's only one implementation
- The interface mirrors the class exactly
- It adds no value beyond the concrete type

### Example: Breaking Circular Dependencies
```typescript
// ✅ GOOD - Interface to break circular dependency
export interface IPromptService {
  getPromptTemplate(id: string): Promise<PromptTemplate | null>;
  createPrompt(data: CreatePromptRequest): Promise<PromptTemplate>;
}

// Service A can depend on interface
export class ServiceA {
  constructor(private promptService: IPromptService) {}
}

// Service B implements interface
export class ServiceB implements IPromptService {
  constructor(private serviceA: ServiceA) {} // No circular dep!
  
  async getPromptTemplate(id: string) { /* ... */ }
  async createPrompt(data: CreatePromptRequest) { /* ... */ }
}
```

## Type-Safe Error Handling

### ✅ GOOD: Typed Errors
```typescript
import BadRequestError from '../error/BadRequestError';
import NotFoundError from '../error/NotFoundError';

async function validateAndProcess(data: unknown): Promise<ProcessedData> {
  // Type guard for validation
  if (!isValidData(data)) {
    throw new BadRequestError('Invalid data format');
  }
  
  const resource = await findResource(data.id);
  if (!resource) {
    throw new NotFoundError(`Resource ${data.id} not found`);
  }
  
  return processResource(resource);
}

// Type guard function
function isValidData(data: unknown): data is ValidDataType {
  return (
    typeof data === 'object' &&
    data !== null &&
    'id' in data &&
    typeof (data as any).id === 'string'
  );
}
```

## Generic Type Patterns

### ✅ GOOD: Properly Typed Generics
```typescript
// Repository with proper generic constraints
export class Repository<T extends Document> {
  constructor(private model: Model<T>) {}
  
  async findById(id: string): Promise<T | null> {
    return this.model.findById(id).exec();
  }
  
  async create(data: Partial<T>): Promise<T> {
    return this.model.create(data);
  }
}

// Usage with specific type
const userRepo = new Repository<UserDocument>(UserModel);
```

## Type Utilities

### Using Type Helpers from test-doubles.ts
```typescript
import { 
  DeepPartial, 
  StubbedInstance, 
  PartialBy, 
  RequiredBy 
} from '../utils/test-doubles';

// Create partial types for testing
const partialUser: DeepPartial<User> = {
  email: '<EMAIL>',
  profile: {
    name: 'Test User'
    // Other profile fields are optional
  }
};

// Make specific fields required
type UserUpdateData = RequiredBy<Partial<User>, 'email' | 'id'>;
```

## Best Practices

1. **Use TypeScript strict mode** - Enable all strict checks
2. **Avoid type casting** - Use type guards instead
3. **Leverage type inference** - Let TypeScript infer when possible
4. **Document complex types** - Add JSDoc comments for clarity
5. **Use discriminated unions** for type safety in switch statements