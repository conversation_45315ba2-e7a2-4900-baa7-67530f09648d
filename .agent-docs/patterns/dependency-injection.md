# Dependency Injection Patterns - G17Eco API

## Core Principles

### 1. Constructor-Based Dependency Injection
All service dependencies MUST be injected through the constructor. This ensures testability and clear dependency graphs.

### 2. Factory Methods Usage
Factory methods should ONLY call other factory methods, never be called within service classes.

## Patterns

### ✅ GOOD: Constructor Injection
```typescript
// Service with proper constructor injection
export class AITestingService {
  constructor(
    private logger: LoggerInterface,
    private modelFactory: UnifiedAIModelFactory,
    private promptService: AITestingPromptService,
    private vectorStoreService: VectorStoreService,
    private universalTrackerModel: typeof UniversalTracker,
    private providerFileManager: ProviderFileManager
  ) {
    // Initialize any composed services here
    this.utrService = new UTRPreparationService(universalTrackerModel);
  }
}

// Factory method only calls other factories
export const getAITestingService = (): AITestingService => {
  if (!instance) {
    instance = new AITestingService(
      wwgLogger,
      getUnifiedAIModelFactory(), // Factory calling factory
      getAITestingPromptService(), // Factory calling factory
      getVectorStoreService(),      // Factory calling factory
      UniversalTracker,
      getProviderFileManager()      // Factory calling factory
    );
  }
  return instance;
};
```

### ❌ BAD: Service Calling Factory
```typescript
// DON'T DO THIS
export class BadService {
  private modelFactory: UnifiedAIModelFactory;
  
  constructor() {
    // Services should NOT call factory methods
    this.modelFactory = getUnifiedAIModelFactory(); // ❌ BAD
  }
}
```

## Testing with Dependency Injection

### ✅ GOOD: Test with Constructor Injection
```typescript
describe('AITestingPromptService', () => {
  let service: AITestingPromptService;
  let mockRepository: StubbedInstance<PromptTemplateRepository>;
  
  beforeEach(() => {
    // Create mock with proper types
    mockRepository = createStubbedTestDouble<PromptTemplateRepository>({
      findAllActive: sandbox.stub(),
      findById: sandbox.stub(),
      create: sandbox.stub()
    });
    
    // Inject mock via constructor
    service = new AITestingPromptService(
      mockRepository as unknown as PromptTemplateRepository
    );
  });
});
```

### ❌ BAD: Accessing Private Properties
```typescript
// DON'T DO THIS
describe('BadTest', () => {
  it('should test something', () => {
    const service = getAITestingPromptService();
    // Don't access private properties
    (service as any).promptRepository = mockRepo; // ❌ BAD
  });
});
```

## Service Instantiation Patterns

### 1. Simple Services (No Singleton)
```typescript
// For services that don't need to be singletons
export class FileProcessingService {
  constructor(
    private logger: Logger,
    private fileStorage: FileStorageService
  ) {}
}

// Usage
const service = new FileProcessingService(logger, fileStorage);
```

### 2. Singleton Services (With Factory)
```typescript
let instance: ComplexService | undefined;

export const getComplexService = (): ComplexService => {
  if (!instance) {
    instance = new ComplexService(
      getDependency1(),
      getDependency2(),
      getDependency3()
    );
  }
  return instance;
};
```

### 3. Composed Services
```typescript
export class ComposedService {
  private subService1: SubService1;
  private subService2: SubService2;
  
  constructor(
    private logger: Logger,
    dependency1: Dependency1,
    dependency2: Dependency2
  ) {
    // Create composed services in constructor
    this.subService1 = new SubService1(dependency1);
    this.subService2 = new SubService2(dependency2);
  }
}
```

## Best Practices

1. **Explicit Dependencies**: All dependencies should be visible in the constructor signature
2. **No Hidden Dependencies**: Avoid using global imports or singletons inside services
3. **Testability First**: Design services to be easily testable with mock dependencies
4. **Type Safety**: Use proper types for all dependencies, avoid `any`
5. **Single Responsibility**: Each service should have one clear purpose

## Common Mistakes to Avoid

1. **Circular Dependencies**: Use interfaces or reorganize services to avoid cycles
2. **God Objects**: Don't inject too many dependencies (max 5) - consider service composition
3. **Lazy Loading**: Avoid `require()` or dynamic imports for dependencies
4. **Static Methods**: Prefer instance methods for better testability
