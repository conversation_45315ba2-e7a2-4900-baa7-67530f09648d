# Architectural Patterns - G17Eco API

## Core Architectural Principles

### 1. One-Way Dependency Flow
Services should have clear, unidirectional dependencies. Lower-level services should not depend on higher-level services.

```
Routes → Services → Repositories → Models
         ↓
      External Services (Email, AI, etc.)
```

### 2. Avoiding Circular Dependencies
Circular dependencies indicate poor architecture and must be avoided.

## Dependency Flow Patterns

### ✅ GOOD: Clear One-Way Dependencies
```typescript
// Models (lowest level)
export class User { /* ... */ }

// Repository (depends on models)
export class UserRepository {
  constructor(private userModel: typeof User) {}
}

// Service (depends on repository)
export class UserService {
  constructor(
    private userRepo: UserRepository,
    private emailService: EmailService // External service
  ) {}
}

// Route (depends on service)
router.post('/users', async (req, res) => {
  const userService = getUserService();
  const user = await userService.createUser(req.body);
  res.json(user);
});
```

### ❌ BAD: Circular Dependencies
```typescript
// DON'T DO THIS - Circular dependency
class ServiceA {
  constructor(private serviceB: ServiceB) {} // ❌ A depends on B
}

class ServiceB {
  constructor(private serviceA: ServiceA) {} // ❌ B depends on A
}
```

## Breaking Circular Dependencies

### Solution 1: Service Composition
```typescript
// Break into smaller, focused services
export class UTRPreparationService {
  constructor(private utrModel: typeof UniversalTracker) {}
  
  async prepareUTRs(selection: UTRSelection) { /* ... */ }
}

export class AIFileHandlingService {
  constructor(
    private logger: Logger,
    private fileManager: ProviderFileManager
  ) {}
  
  async handleFiles(files: FileData[]) { /* ... */ }
}

// Compose services in orchestrator
export class AITestingService {
  private utrService: UTRPreparationService;
  private fileService: AIFileHandlingService;
  
  constructor(dependencies: Dependencies) {
    this.utrService = new UTRPreparationService(dependencies.utrModel);
    this.fileService = new AIFileHandlingService(dependencies.logger, dependencies.fileManager);
  }
}
```

### Solution 2: Event-Based Decoupling
```typescript
// Use events to decouple services
export class OrderService {
  constructor(private eventBus: EventBus) {}
  
  async createOrder(data: OrderData) {
    const order = await this.saveOrder(data);
    // Don't directly call EmailService
    this.eventBus.emit('order.created', order);
    return order;
  }
}

export class EmailService {
  constructor(private eventBus: EventBus) {
    // Listen for events
    eventBus.on('order.created', this.sendOrderEmail.bind(this));
  }
}
```

## Repository Pattern

### When to Use Repository Pattern
✅ **Use repositories when:**
- Centralizing data access logic
- Abstracting database operations
- Complex queries with aggregations
- Need to swap data sources

❌ **Don't use repositories when:**
- Simple CRUD with no business logic
- Direct Mongoose operations suffice

### Repository Implementation
```typescript
export class PromptTemplateRepository {
  async findAllActive(): Promise<AIPromptTemplatePlain[]> {
    return AIPromptTemplate.find({ isActive: true })
      .lean()
      .exec();
  }
  
  async findWithComplexQuery(params: QueryParams): Promise<AIPromptTemplatePlain[]> {
    const pipeline = [
      { $match: this.buildMatchStage(params) },
      { $lookup: this.buildLookupStage() },
      { $project: this.buildProjectionStage() },
      { $sort: { created: -1 } }
    ];
    
    return AIPromptTemplate.aggregate(pipeline).exec();
  }
}
```

## Service Layer Patterns

### 1. Orchestration Services
```typescript
// Orchestrates multiple services for complex operations
export class AITestingService {
  constructor(
    private promptService: PromptService,
    private fileService: FileService,
    private modelService: ModelService
  ) {}
  
  async executeTest(request: TestRequest): Promise<TestResponse> {
    // Orchestrate the workflow
    const prompt = await this.promptService.prepare(request.prompt);
    const files = await this.fileService.process(request.files);
    const results = await this.modelService.execute(prompt, files);
    
    return this.formatResponse(results);
  }
}
```

### 2. Domain Services
```typescript
// Focused on single domain with clear boundaries
export class UserAuthenticationService {
  constructor(
    private userRepo: UserRepository,
    private tokenService: TokenService,
    private cryptoService: CryptoService
  ) {}
  
  async authenticate(credentials: Credentials): Promise<AuthResult> {
    const user = await this.userRepo.findByEmail(credentials.email);
    if (!user) {
      throw new UnauthorizedError('Invalid credentials');
    }
    
    const isValid = await this.cryptoService.verify(
      credentials.password, 
      user.passwordHash
    );
    
    if (!isValid) {
      throw new UnauthorizedError('Invalid credentials');
    }
    
    const token = await this.tokenService.generate(user);
    return { user, token };
  }
}
```

## Composition Over Inheritance

### ✅ GOOD: Composition Pattern
```typescript
// Compose functionality through delegation
export class AITestingService {
  private utrService: UTRPreparationService;
  private fileService: AIFileHandlingService;
  
  constructor(dependencies: Dependencies) {
    this.utrService = new UTRPreparationService(dependencies.utrModel);
    this.fileService = new AIFileHandlingService(dependencies.logger);
  }
  
  async executeTest(request: Request) {
    const utrs = await this.utrService.prepare(request.utrSelection);
    const files = await this.fileService.process(request.files);
    // Delegate to composed services
  }
}
```

### ❌ BAD: Deep Inheritance
```typescript
// DON'T DO THIS - Deep inheritance hierarchies
class BaseService { /* ... */ }
class AuthenticatedService extends BaseService { /* ... */ }
class CrudService extends AuthenticatedService { /* ... */ }
class UserService extends CrudService { /* ... */ } // ❌ Too deep!
```

## Best Practices

1. **Keep services focused** - Single responsibility principle
2. **Inject dependencies** - Don't create them internally
3. **Use interfaces sparingly** - Only when needed
4. **Prefer composition** - Over inheritance
5. **Layer appropriately** - Respect dependency flow
6. **Avoid God objects** - Break down large services
7. **Test in isolation** - Services should be independently testable