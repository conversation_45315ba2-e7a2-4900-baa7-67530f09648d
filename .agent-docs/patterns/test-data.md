# Test Data Management - G17Eco API

## Type-Safe Test Doubles with StubbedInstance

### Using createStubbedTestDouble
```typescript
import { createStubbedTestDouble, StubbedInstance } from '../../utils/test-doubles';

describe('ServiceTest', () => {
  let mockDependency: StubbedInstance<DependencyInterface>;
  
  beforeEach(() => {
    // Create a mock where all methods are SinonStubs
    mockDependency = createStubbedTestDouble<DependencyInterface>({
      method1: sandbox.stub().resolves('result1'),
      method2: sandbox.stub().resolves('result2'),
      asyncMethod: sandbox.stub().resolves({ data: 'test' })
    });
    
    // Use in service - cast to original type
    const service = new MyService(
      mockDependency as unknown as DependencyInterface
    );
  });
  
  it('should use stubbed methods without casting', () => {
    // No casting needed - TypeScript knows these are stubs
    mockDependency.method1.returns('different result');
    mockDependency.method2.calledOnce; // ✅ No casting!
    
    expect(mockDependency.asyncMethod.firstCall.args[0]).to.equal('expected');
  });
});
```

### Using createMockWithStubs (Recommended)
```typescript
import { createMockWithStubs, MockWithStubs } from '../../utils/test-doubles';

describe('ServiceTest', () => {
  let mockDependency: MockWithStubs<DependencyInterface>;
  
  beforeEach(() => {
    // Create a mock with type-safe stub access
    mockDependency = createMockWithStubs<DependencyInterface>({
      method1: sandbox.stub().resolves('result1'),
      method2: sandbox.stub().resolves('result2'),
      asyncMethod: sandbox.stub().resolves({ data: 'test' })
    });
    
    // Use mock for dependency injection - no casting needed!
    const service = new MyService(mockDependency.mock);
  });
  
  it('should use stubbed methods without casting', () => {
    // Access stubs for test assertions - fully typed
    mockDependency.stubs.method1.returns('different result');
    expect(mockDependency.stubs.method2.calledOnce).to.be.true;
    expect(mockDependency.stubs.asyncMethod.firstCall.args[0]).to.equal('expected');
  });
});
```

### Repository Mock Example
```typescript
// Using the new createMockWithStubs pattern
let mockRepository: MockWithStubs<UserRepository>;

beforeEach(() => {
  mockRepository = createMockWithStubs<UserRepository>({
    findById: sandbox.stub(),
    findByEmail: sandbox.stub(),
    save: sandbox.stub(),
    delete: sandbox.stub()
  });
  
  // Set up default behaviors using stubs
  mockRepository.stubs.findById.resolves(null);
  mockRepository.stubs.save.resolves({ id: 'new-id' });
});

it('should find user by ID', async () => {
  const testUser = { id: '123', email: '<EMAIL>' };
  mockRepository.stubs.findById.withArgs('123').resolves(testUser);
  
  // No casting needed - use mock for DI
  const service = new UserService(mockRepository.mock);
  const result = await service.getUser('123');
  
  expect(result).to.deep.equal(testUser);
  expect(mockRepository.stubs.findById.calledOnceWith('123')).to.be.true;
});
```

## Test Data Management

### 1. Fixtures Organization
```typescript
// Domain-specific fixtures
export const userFixtures = {
  userOne: { _id: new ObjectId(), email: '<EMAIL>' },
  userTwo: { _id: new ObjectId(), email: '<EMAIL>' }
};

// Shared test utilities
export const cloneObject = (obj: any) => JSON.parse(JSON.stringify(obj));
```

### 2. Factory Pattern
```typescript
// Object factories for dynamic test data
export const createUserModel = (overrides = {}) => ({
  _id: new ObjectId(),
  email: '<EMAIL>',
  active: true,
  ...overrides
});
```

### 3. Mock Services
```typescript
// Comprehensive mock implementations
export class MockMailer {
  sendEmail = sinon.stub().resolves();
  sendBulkEmail = sinon.stub().resolves();
}
```

## Performance and Integration Testing

### 1. Database Integration Tests
```typescript
// Using MongoDB Memory Server for integration tests
import setup from '../../setup';

describe('Integration Test', () => {
  before(async () => {
    await setup.connectToDatabase();
  });
  
  after(async () => {
    await setup.disconnectFromDatabase();
  });
});
```

### 2. Timeout Handling
```typescript
// Tests with custom timeouts for slow operations
it('should handle slow operation', async function() {
  this.timeout(10000); // 10 second timeout
  const result = await service.slowMethod();
  expect(result).to.exist;
});
```