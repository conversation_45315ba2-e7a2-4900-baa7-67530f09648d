# Service Testing Patterns - G17Eco API

## Service Test Structure Template

```typescript
import { expect } from 'chai';
import sinon from 'sinon';
import { ServiceClass } from '../../../server/service/domain/ServiceClass';
import { RelatedRepository } from '../../../server/repository/RelatedRepository';
import { testFixtures } from '../../fixtures/testFixtures';

describe('ServiceClass', () => {
  const sandbox = sinon.createSandbox();
  let service: ServiceClass;
  let mockDependency: StubbedInstance<DependencyType>;

  beforeEach(() => {
    // Create mocks with proper types
    mockDependency = createStubbedTestDouble<DependencyType>({
      method1: sandbox.stub(),
      method2: sandbox.stub()
    });
    
    // Use constructor injection
    service = new ServiceClass(
      mockDependency as unknown as DependencyType
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('methodName', () => {
    it('should handle success case correctly', async () => {
      // Arrange
      const stub = sandbox.stub(RelatedRepository, 'method').resolves(testData);
      
      // Act
      const result = await service.methodName(input);
      
      // Assert
      expect(stub.calledOnceWith(expectedArgs)).to.be.true;
      expect(result).to.deep.equal(expectedResult);
    });

    it('should handle error case appropriately', async () => {
      // Arrange
      sandbox.stub(RelatedRepository, 'method').rejects(new Error('Test error'));
      
      // Act & Assert
      await expect(service.methodName(input)).to.be.rejectedWith('Test error');
    });
  });
});
```

## Core Testing Patterns

### 1. Dependency Injection Testing
```typescript
// Service with dependencies
const emailService = createUserEmailService();
const userEventService = createUserEventService();
const manager = new UserManager(emailService, userEventService, repository);

// Test with mocked dependencies
sandbox.stub(emailService, 'sendEmail').resolves();
```

### 2. Repository Stubbing Pattern
```typescript
// Using setup.wrapStub for Mongoose models
const findStub = setup.wrapStub(sandbox, UserRepository, 'findById', testUser);

// Direct stubbing for simple cases
const repoStub = sandbox.stub(repository, 'find').resolves([testData]);
```

### 3. Async Testing with Promises
```typescript
// Successful async operations
it('should process async operation', async () => {
  const result = await service.asyncMethod(input);
  expect(result).to.equal(expectedValue);
});

// Error handling
it('should handle async errors', async () => {
  sandbox.stub(dependency, 'method').rejects(new Error('Async error'));
  await expect(service.asyncMethod(input)).to.be.rejectedWith('Async error');
});
```

### 4. Parameterized Testing
```typescript
const testCases = [
  {
    describe: 'should handle case A',
    input: { type: 'A' },
    expected: { result: 'A processed' }
  },
  {
    describe: 'should handle case B', 
    input: { type: 'B' },
    expected: { result: 'B processed' }
  }
];

testCases.forEach(({ describe, input, expected }) => {
  it(describe, async () => {
    const result = await service.process(input);
    expect(result).to.deep.equal(expected);
  });
});
```

### 5. Complex Object Matching
```typescript
// Using deep equality for complex objects
expect(result).to.deep.equal(expectedComplexObject);

// Partial matching for large objects
expect(result).to.include({
  id: expectedId,
  status: expectedStatus
});

// Array membership testing
expect(result.items).to.have.members(expectedItems);
```

## Stub and Mock Management

### 1. Sinon Sandbox Pattern
```typescript
describe('ServiceTest', () => {
  const sandbox = sinon.createSandbox();
  
  afterEach(() => {
    sandbox.restore(); // Clean up all stubs
  });
  
  // Use sandbox.stub() for all stubs
});
```

### 2. Stub Verification
```typescript
// Verify call count and arguments
expect(stub.calledOnce).to.be.true;
expect(stub.calledWith(expectedArg)).to.be.true;
expect(stub.calledOnceWith(expectedArg)).to.be.true;

// Access call arguments
expect(stub.args[0][0]).to.equal(firstCallFirstArg);
```

### 3. Complex Stub Behaviors
```typescript
// Conditional responses
stub.callsFake((condition) => {
  if (condition === 'success') return Promise.resolve(successData);
  return Promise.reject(new Error('Failure'));
});

// Sequential responses
stub.onFirstCall().resolves(firstResult)
    .onSecondCall().resolves(secondResult);
```

## Anti-Patterns to Avoid

### 1. ❌ Never Use try/catch in Tests
```typescript
// BAD - Don't do this
it('should handle errors', async () => {
  try {
    await service.method();
    expect.fail('Should have thrown');
  } catch (error) {
    expect(error.message).to.equal('Expected error');
  }
});

// GOOD - Use chai-as-promised
it('should handle errors', async () => {
  await expect(service.method())
    .to.be.rejectedWith(ErrorType, 'Expected error');
});
```

### 2. ❌ Don't Access Private Properties
```typescript
// BAD - Don't access private properties
it('should test something', () => {
  (service as any).privateProperty = 'value';
  (service as any)._privateMethod();
});

// GOOD - Test through public interface
it('should test something', () => {
  // Use constructor injection or public methods
  const service = new ServiceClass(mockDependency);
  const result = service.publicMethod();
  expect(result).to.equal(expected);
});
```

### 3. ✅ Keep Stub References to Avoid Casting
```typescript
// BAD - Casting everywhere
it('should call dependency', () => {
  (mockService.method as sinon.SinonStub).returns(value);
  service.doSomething();
  expect((mockService.method as sinon.SinonStub).calledOnce).to.be.true;
});

// GOOD - Keep stub references
it('should call dependency', () => {
  const methodStub = mockService.method; // Already a SinonStub
  methodStub.returns(value);
  service.doSomething();
  expect(methodStub.calledOnce).to.be.true;
});
```

### 4. ✅ Use Proper Type-Safe Mocks
```typescript
import { createStubbedTestDouble, StubbedInstance } from '../../utils/test-doubles';

// Create fully typed mock
const mockRepo: StubbedInstance<Repository> = createStubbedTestDouble<Repository>({
  find: sandbox.stub().resolves([]),
  findById: sandbox.stub().resolves(null),
  save: sandbox.stub().resolves({ id: '123' })
});

// All methods are properly typed as SinonStub
mockRepo.find.calledOnce; // TypeScript knows this is a SinonStub
```